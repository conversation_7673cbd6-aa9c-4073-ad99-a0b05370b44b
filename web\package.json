{"name": "xrpl-launchpad-web", "version": "0.0.1", "author": "Ionic Framework", "homepage": "https://ionicframework.com/", "scripts": {"ng": "ng", "start": "ionic serve --host 0.0.0.0", "start:ionic": "ionic serve --host 0.0.0.0", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test", "test:ci": "ng test --browsers=ChromeHeadless --watch=false", "test:ci:firefox": "ng test --browsers=FirefoxHeadless --watch=false", "test:coverage": "ng test --code-coverage --watch=false", "e2e": "cypress open", "e2e:ci": "cypress run", "e2e:headless": "cypress run --headless", "lint": "ng lint", "docs": "compodoc -p tsconfig.json -s", "docs:build": "compodoc -p tsconfig.json"}, "private": true, "dependencies": {"@angular/animations": "^20.0.0", "@angular/common": "^20.0.0", "@angular/compiler": "^20.0.0", "@angular/core": "^20.0.0", "@angular/forms": "^20.0.0", "@angular/platform-browser": "^20.0.0", "@angular/platform-browser-dynamic": "^20.0.0", "@angular/router": "^20.0.0", "@capacitor/app": "7.0.2", "@capacitor/core": "7.4.3", "@capacitor/haptics": "7.0.2", "@capacitor/keyboard": "7.0.2", "@capacitor/status-bar": "7.0.2", "@ionic/angular": "^8.0.0", "@types/qrcode": "^1.5.5", "ionicons": "^7.0.0", "qrcode": "^1.5.4", "rxjs": "~7.8.0", "socket.io-client": "^4.7.5", "tslib": "^2.3.0", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^20.2.1", "@angular-eslint/builder": "^20.0.0", "@angular-eslint/eslint-plugin": "^20.0.0", "@angular-eslint/eslint-plugin-template": "^20.0.0", "@angular-eslint/schematics": "^20.0.0", "@angular-eslint/template-parser": "^20.0.0", "@angular/cli": "^20.2.1", "@angular/compiler-cli": "^20.0.0", "@angular/language-service": "^20.0.0", "@capacitor/cli": "7.4.3", "@compodoc/compodoc": "^1.1.30", "@ionic/angular-toolkit": "^12.0.0", "@types/jasmine": "~5.1.0", "@typescript-eslint/eslint-plugin": "^8.18.0", "@typescript-eslint/parser": "^8.18.0", "cypress": "^15.1.0", "eslint": "^9.16.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jsdoc": "^48.2.1", "eslint-plugin-prefer-arrow": "1.2.2", "jasmine-core": "~5.1.0", "jasmine-spec-reporter": "~5.0.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.8.0"}, "description": "An Ionic project"}