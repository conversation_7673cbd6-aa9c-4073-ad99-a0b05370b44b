<ion-header [translucent]="true">
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-menu-button></ion-menu-button>
    </ion-buttons>
    <ion-title>Register</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true">
  <div class="register-container">
    <div class="register-card">
      <h1>Create Account</h1>
      <p>Join XRPL Launchpad and start your journey</p>

      <form [formGroup]="registerForm" (ngSubmit)="onSubmit()">
        <ion-item>
          <ion-label position="stacked">Username</ion-label>
          <ion-input
            type="text"
            formControlName="username"
            placeholder="Enter your username"
            [clearInput]="true"
            autocomplete="username"
          ></ion-input>
        </ion-item>
        <div
          class="error-message"
          *ngIf="registerForm.get('username')?.invalid && registerForm.get('username')?.touched"
        >
          <span *ngIf="registerForm.get('username')?.errors?.['required']"
            >Username is required</span
          >
          <span *ngIf="registerForm.get('username')?.errors?.['minlength']"
            >Username must be at least 3 characters</span
          >
          <span *ngIf="registerForm.get('username')?.errors?.['maxlength']"
            >Username must be no more than 30 characters</span
          >
          <span *ngIf="registerForm.get('username')?.errors?.['pattern']"
            >Username can only contain letters, numbers, and underscores</span
          >
        </div>

        <ion-item>
          <ion-label position="stacked">Email</ion-label>
          <ion-input
            type="email"
            formControlName="email"
            placeholder="Enter your email"
            [clearInput]="true"
            autocomplete="email"
          ></ion-input>
        </ion-item>
        <div
          class="error-message"
          *ngIf="registerForm.get('email')?.invalid && registerForm.get('email')?.touched"
        >
          <span *ngIf="registerForm.get('email')?.errors?.['required']"
            >Email is required</span
          >
          <span *ngIf="registerForm.get('email')?.errors?.['email']"
            >Please enter a valid email address</span
          >
        </div>

        <ion-item>
          <ion-label position="stacked">Password</ion-label>
          <ion-input
            type="password"
            formControlName="password"
            placeholder="Enter your password"
            [clearInput]="true"
            autocomplete="new-password"
          ></ion-input>
        </ion-item>
        <div
          class="error-message"
          *ngIf="registerForm.get('password')?.invalid && registerForm.get('password')?.touched"
        >
          <span *ngIf="registerForm.get('password')?.errors?.['required']"
            >Password is required</span
          >
          <span *ngIf="registerForm.get('password')?.errors?.['minlength']"
            >Password must be at least 8 characters</span
          >
          <span *ngIf="registerForm.get('password')?.errors?.['pattern']"
            >Password must contain at least one uppercase letter, one lowercase
            letter, one number, and one special character</span
          >
        </div>

        <div class="error-message" *ngIf="errorMessage">{{ errorMessage }}</div>

        <ion-button
          expand="block"
          type="submit"
          [disabled]="registerForm.invalid || isLoading"
          class="register-button"
        >
          {{ isLoading ? 'Creating Account...' : 'Create Account' }}
        </ion-button>

        <ion-button
          fill="clear"
          expand="block"
          routerLink="/login"
          class="login-link-button"
        >
          Already have an account? Sign In
        </ion-button>
      </form>
    </div>
  </div>
</ion-content>
