# ---- Build Ionic Angular app ----
FROM node:20-alpine AS build
WORKDIR /app
ENV CI=true
ARG API_BASE_URL
ARG NG_BUILD_OUTPUT=www

# Install OS deps and Ionic build deps
RUN apk add --no-cache bash git
COPY web/package*.json ./
RUN npm install --legacy-peer-deps

# Copy web source code
COPY web/ .

# Install Ionic CLI globally for proper Ionic builds
RUN npm install -g @ionic/cli

# Build the Ionic app for production
RUN ionic build --prod

# ---- Nginx runtime ----
FROM nginx:1.27-alpine AS runtime
# Install health check tools
RUN apk add --no-cache dumb-init wget

# Copy SPA
ARG NG_BUILD_OUTPUT=www
COPY --from=build /app/${NG_BUILD_OUTPUT} /usr/share/nginx/html

# Nginx config with SPA fallback and /api proxy to the api service
# (keeps local dev simple and avoids CORS for the SPA)
COPY web/nginx.conf /etc/nginx/conf.d/default.conf

EXPOSE 80

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD wget -qO- http://localhost/healthz || exit 1

CMD ["dumb-init", "nginx", "-g", "daemon off;"]
