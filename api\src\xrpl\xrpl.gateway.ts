import {
  WebSocketGateway,
  WebSocketServer,
  SubscribeMessage,
  OnGatewayConnection,
  OnGatewayDisconnect,
  ConnectedSocket,
  MessageBody,
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { UseGuards } from '@nestjs/common';
import { WsJwtAuthGuard } from '../auth/guards/ws-jwt-auth.guard';
import { XrplService } from './xrpl.service';
import { Logger } from '@nestjs/common';

@WebSocketGateway({
  cors: {
    origin: '*',
  },
  namespace: '/xrpl',
})
@UseGuards(WsJwtAuthGuard)
export class XrplGateway implements OnGatewayConnection, OnGatewayDisconnect {
  @WebSocketServer()
  server!: Server;

  private readonly logger = new Logger(XrplGateway.name);
  private userSockets = new Map<string, Socket[]>();
  private addressSubscriptions = new Map<string, Set<string>>(); // address -> Set of userIds

  constructor(private readonly xrplService: XrplService) {}

  handleConnection(client: Socket) {
    this.logger.log(`Client connected: ${client.id}`);
  }

  handleDisconnect(client: Socket) {
    this.logger.log(`Client disconnected: ${client.id}`);
    this.removeClientFromAllSubscriptions(client);
  }

  @SubscribeMessage('subscribe')
  handleSubscribe(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: { address: string },
  ) {
    try {
      const userId = (client as any).user?.id;
      if (!userId) {
        client.emit('error', {
          type: 'authentication_error',
          message: 'Authentication required',
        });
        return;
      }

      const { address } = data;
      if (!address) {
        client.emit('error', {
          type: 'validation_error',
          message: 'Address is required',
        });
        return;
      }

      // Add client to user's socket list
      if (!this.userSockets.has(userId)) {
        this.userSockets.set(userId, []);
      }
      this.userSockets.get(userId)!.push(client);

      // Add user to address subscription
      if (!this.addressSubscriptions.has(address)) {
        this.addressSubscriptions.set(address, new Set());
      }
      this.addressSubscriptions.get(address)!.add(userId);

      // Subscribe to XRPL account updates
      const subscription = this.xrplService.subscribeToAccount(address);
      subscription.subscribe({
        next: (transaction) => {
          // Emit transaction to all users subscribed to this address
          const userIds = this.addressSubscriptions.get(address);
          if (userIds) {
            userIds.forEach((uid) => {
              const userSockets = this.userSockets.get(uid);
              if (userSockets) {
                userSockets.forEach((socket) => {
                  socket.emit('transaction', {
                    address,
                    transaction,
                    timestamp: new Date().toISOString(),
                  });
                });
              }
            });
          }

          // Also emit balance update after transaction (fire and forget)
          this.xrplService
            .getBalance(address)
            .then((balance) => {
              if (userIds) {
                userIds.forEach((uid) => {
                  const userSockets = this.userSockets.get(uid);
                  if (userSockets) {
                    userSockets.forEach((socket) => {
                      socket.emit('balance', balance);
                    });
                  }
                });
              }
            })
            .catch((error) => {
              this.logger.error(
                `Failed to get balance for ${address}: ${error instanceof Error ? error.message : String(error)}`,
              );
            });
        },
        error: (error) => {
          this.logger.error(`Subscription error for ${address}: ${error}`);
          client.emit('error', {
            type: 'subscription_error',
            message: 'Subscription failed',
            address,
            details: error instanceof Error ? error.message : String(error),
          });
        },
      });

      // Send initial balance
      this.xrplService
        .getBalance(address)
        .then((initialBalance) => {
          client.emit('balance', initialBalance);
        })
        .catch((error) => {
          this.logger.error(
            `Failed to get initial balance for ${address}: ${error instanceof Error ? error.message : String(error)}`,
          );
        });

      client.emit('subscribed', { address });
      this.logger.log(`User ${userId} subscribed to address ${address}`);
    } catch (error) {
      this.logger.error(`Subscribe error: ${error as string}`);
      client.emit('error', {
        type: 'subscription_error',
        message: 'Subscription failed',
        details: error instanceof Error ? error.message : String(error),
      });
    }
  }

  @SubscribeMessage('unsubscribe')
  handleUnsubscribe(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: { address: string },
  ) {
    try {
      const userId = (client as any).user?.id;
      if (!userId) {
        client.emit('error', {
          type: 'authentication_error',
          message: 'Authentication required',
        });
        return;
      }

      const { address } = data;
      if (!address) {
        client.emit('error', {
          type: 'validation_error',
          message: 'Address is required',
        });
        return;
      }

      // Remove user from address subscription
      const addressSubs = this.addressSubscriptions.get(address);
      if (addressSubs) {
        addressSubs.delete(userId);
        if (addressSubs.size === 0) {
          // No more users subscribed to this address
          this.xrplService.unsubscribeFromAccount(address);
          this.addressSubscriptions.delete(address);
        }
      }

      // Remove client from user's socket list
      const userSockets = this.userSockets.get(userId);
      if (userSockets) {
        const index = userSockets.indexOf(client);
        if (index > -1) {
          userSockets.splice(index, 1);
        }
        if (userSockets.length === 0) {
          this.userSockets.delete(userId);
        }
      }

      client.emit('unsubscribed', { address });
      this.logger.log(`User ${userId} unsubscribed from address ${address}`);
    } catch (error) {
      this.logger.error(`Unsubscribe error: ${error as string}`);
      client.emit('error', {
        type: 'unsubscribe_error',
        message: 'Unsubscribe failed',
        details: error instanceof Error ? error.message : String(error),
      });
    }
  }

  private removeClientFromAllSubscriptions(client: Socket) {
    const userId = (client as any).user?.id;
    if (!userId) return;

    // Remove client from user's socket list
    const userSockets = this.userSockets.get(userId);
    if (userSockets) {
      const index = userSockets.indexOf(client);
      if (index > -1) {
        userSockets.splice(index, 1);
      }
      if (userSockets.length === 0) {
        this.userSockets.delete(userId);
      }
    }

    // Check if user has no more active connections
    if (!this.userSockets.has(userId)) {
      // Remove user from all address subscriptions
      this.addressSubscriptions.forEach((userIds, address) => {
        userIds.delete(userId);
        if (userIds.size === 0) {
          this.xrplService.unsubscribeFromAccount(address);
          this.addressSubscriptions.delete(address);
        }
      });
    }
  }
}
