<ion-header [translucent]="true">
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-menu-button></ion-menu-button>
    </ion-buttons>
    <ion-title>Launchpad Sales</ion-title>
    <ion-buttons slot="end">
      <ion-button (click)="onRefreshData()" [disabled]="isLoading()">
        <ion-icon name="refresh" [class.spin]="isLoading()"></ion-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true">
  <ion-refresher slot="fixed" (ionRefresh)="onRefreshData()">
    <ion-refresher-content></ion-refresher-content>
  </ion-refresher>

  <!-- Filter Segment -->
  <ion-segment
    [value]="filterStatus()"
    (ionChange)="onFilterChange($event.detail.value || 'all')"
    class="filter-segment"
  >
    <ion-segment-button value="all">
      <ion-label>All</ion-label>
    </ion-segment-button>
    <ion-segment-button value="active">
      <ion-label>Active</ion-label>
    </ion-segment-button>
    <ion-segment-button value="ended">
      <ion-label>Ended</ion-label>
    </ion-segment-button>
    <ion-segment-button value="canceled">
      <ion-label>Canceled</ion-label>
    </ion-segment-button>
  </ion-segment>

  <div class="launchpad-container">
    <!-- Loading State -->
    <div *ngIf="isLoading()" class="loading-state">
      <ion-spinner name="crescent"></ion-spinner>
      <p>Loading sales...</p>
    </div>

    <!-- Sales List -->
    @if (filteredSales$ | async; as sales) { @if (sales.length > 0) {
    <div class="sales-list">
      @for (sale of sales; track sale.id) {
      <ion-card class="sale-card" button (click)="onViewSale(sale.id)">
        <ion-card-content>
          <div class="sale-header">
            <div class="sale-info">
              <h2 class="sale-symbol">{{ sale.symbol }}</h2>
              <p class="sale-description">{{ sale.description }}</p>
            </div>
            <ion-badge
              [color]="getStatusColor(sale.status)"
              class="status-badge"
            >
              {{ sale.status | titlecase }}
            </ion-badge>
          </div>

          <div class="sale-progress">
            <div class="progress-header">
              <span class="progress-label">Progress</span>
              <span class="progress-percentage"
                >{{ getProgressPercentage(sale.softCap, sale.hardCap) |
                number:'1.0-1' }}%</span
              >
            </div>
            <ion-progress-bar
              [value]="getProgressPercentage(sale.softCap, sale.hardCap) / 100"
              [color]="getStatusColor(sale.status)"
              class="progress-bar"
            ></ion-progress-bar>
            <div class="progress-amounts">
              <span class="raised">{{ sale.softCap }} XRP</span>
              <span class="target">of {{ sale.hardCap }} XRP</span>
            </div>
          </div>

          <div class="sale-details">
            <div class="detail-row">
              <div class="detail-item">
                <ion-icon name="calendar-outline"></ion-icon>
                <span class="detail-label">Start Date</span>
                <span class="detail-value">{{ formatDate(sale.start) }}</span>
              </div>
              <div class="detail-item">
                <ion-icon name="calendar-outline"></ion-icon>
                <span class="detail-label">End Date</span>
                <span class="detail-value">{{ formatDate(sale.end) }}</span>
              </div>
            </div>
            <div class="detail-row">
              <div class="detail-item">
                <ion-icon name="wallet-outline"></ion-icon>
                <span class="detail-label">Price</span>
                <span class="detail-value">{{ sale.price }} XRP</span>
              </div>
              <div class="detail-item">
                <ion-icon name="location-outline"></ion-icon>
                <span class="detail-label">Collection</span>
                <span class="detail-value"
                  >{{ sale.collectionAddress | slice:0:8 }}...</span
                >
              </div>
            </div>
          </div>

          <div class="sale-actions">
            <ion-button
              expand="block"
              [color]="getStatusColor(sale.status)"
              [disabled]="sale.status !== 'active'"
              (click)="onSaleAction(sale)"
              color="white"
            >
              <ion-icon
                [name]="getStatusIcon(sale.status)"
                slot="start"
              ></ion-icon>
              {{ sale.status === 'active' ? 'Contribute Now' : sale.status ===
              'ended' ? 'View Details' : sale.status === 'canceled' ? 'Canceled'
              : 'View Details' }}
            </ion-button>
          </div>
        </ion-card-content>
      </ion-card>
      }
    </div>
    } @else {
    <div class="empty-state">
      <ion-icon name="rocket-outline" size="large"></ion-icon>
      <h3>No sales found</h3>
      <p>There are no sales matching your current filter.</p>
      <ion-button fill="clear" (click)="onFilterChange('all')">
        View all sales
      </ion-button>
    </div>
    } } @else {
    <ion-skeleton-text animated></ion-skeleton-text>
    }
  </div>
</ion-content>
