import { ComponentFixture, TestBed } from '@angular/core/testing';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { Router, ActivatedRoute } from '@angular/router';
import { of, throwError } from 'rxjs';
import { RegisterPage } from './register.page';
import { ApiService } from '../../services/api/api.service';
import { RegisterRequest } from '../../types';

describe('RegisterPage', () => {
  let component: RegisterPage;
  let fixture: ComponentFixture<RegisterPage>;
  let mockApiService: jasmine.SpyObj<ApiService>;
  let mockRouter: jasmine.SpyObj<Router>;

  const mockAuthResponse = {
    accessToken: 'mock-access-token',
    refreshToken: 'mock-refresh-token',
    user: {
      id: '1',
      username: 'testuser',
      email: '<EMAIL>',
      role: 'user',
      xrplAddress: null,
    },
  };

  beforeEach(async () => {
    const apiServiceSpy = jasmine.createSpyObj('ApiService', ['register']);
    const routerSpy = jasmine.createSpyObj(
      'Router',
      ['navigate', 'createUrlTree', 'serializeUrl'],
      {
        url: '/register',
        events: of(),
      }
    );
    const activatedRouteSpy = jasmine.createSpyObj('ActivatedRoute', [], {
      snapshot: { params: {}, queryParams: {} },
      params: of({}),
      queryParams: of({}),
    });

    // Mock the Router methods
    routerSpy.createUrlTree.and.returnValue({} as any);
    routerSpy.serializeUrl.and.returnValue('/login');

    await TestBed.configureTestingModule({
      imports: [RegisterPage, HttpClientTestingModule],
      providers: [
        { provide: ApiService, useValue: apiServiceSpy },
        { provide: Router, useValue: routerSpy },
        { provide: ActivatedRoute, useValue: activatedRouteSpy },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(RegisterPage);
    component = fixture.componentInstance;
    mockApiService = TestBed.inject(ApiService) as jasmine.SpyObj<ApiService>;
    mockRouter = TestBed.inject(Router) as jasmine.SpyObj<Router>;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize form with empty values', () => {
    expect(component.registerForm.get('username')?.value).toBe('');
    expect(component.registerForm.get('email')?.value).toBe('');
    expect(component.registerForm.get('password')?.value).toBe('');
  });

  it('should validate required fields', () => {
    const usernameControl = component.registerForm.get('username');
    const emailControl = component.registerForm.get('email');
    const passwordControl = component.registerForm.get('password');

    expect(usernameControl?.hasError('required')).toBeTruthy();
    expect(emailControl?.hasError('required')).toBeTruthy();
    expect(passwordControl?.hasError('required')).toBeTruthy();
  });

  it('should validate username pattern', () => {
    const usernameControl = component.registerForm.get('username');

    usernameControl?.setValue('invalid username!');
    expect(usernameControl?.hasError('pattern')).toBeTruthy();

    usernameControl?.setValue('valid_username123');
    expect(usernameControl?.hasError('pattern')).toBeFalsy();
  });

  it('should validate email format', () => {
    const emailControl = component.registerForm.get('email');

    emailControl?.setValue('invalid-email');
    expect(emailControl?.hasError('email')).toBeTruthy();

    emailControl?.setValue('<EMAIL>');
    expect(emailControl?.hasError('email')).toBeFalsy();
  });

  it('should validate password strength', () => {
    const passwordControl = component.registerForm.get('password');

    passwordControl?.setValue('weak');
    expect(passwordControl?.hasError('pattern')).toBeTruthy();

    passwordControl?.setValue('StrongPass123!');
    expect(passwordControl?.hasError('pattern')).toBeFalsy();
  });

  it('should not submit form when invalid', () => {
    component.onSubmit();
    expect(mockApiService.register).not.toHaveBeenCalled();
  });

  it('should submit form when valid and call API service', () => {
    const registerData: RegisterRequest = {
      username: 'testuser',
      email: '<EMAIL>',
      password: 'StrongPass123!',
    };

    component.registerForm.patchValue(registerData);
    mockApiService.register.and.returnValue(of(mockAuthResponse));

    component.onSubmit();

    expect(mockApiService.register).toHaveBeenCalledWith(registerData);
    expect(mockRouter.navigate).toHaveBeenCalledWith(['/dashboard']);
  });

  it('should handle registration success', () => {
    component.registerForm.patchValue({
      username: 'testuser',
      email: '<EMAIL>',
      password: 'StrongPass123!',
    });

    mockApiService.register.and.returnValue(of(mockAuthResponse));

    component.onSubmit();
    fixture.detectChanges();

    expect(component.isLoading).toBeFalsy();
    expect(component.errorMessage).toBe('');
  });

  it('should handle registration error', () => {
    component.registerForm.patchValue({
      username: 'testuser',
      email: '<EMAIL>',
      password: 'StrongPass123!',
    });

    const errorResponse = {
      error: { message: 'Registration failed' },
      status: 400,
    };

    mockApiService.register.and.returnValue(throwError(() => errorResponse));

    component.onSubmit();
    fixture.detectChanges();

    expect(component.errorMessage).toBe(
      'Registration failed. Please try again.'
    );
    expect(component.isLoading).toBeFalsy();
  });

  it('should mark all fields as touched when form is invalid on submit', () => {
    const usernameControl = component.registerForm.get('username');
    const emailControl = component.registerForm.get('email');
    const passwordControl = component.registerForm.get('password');

    component.onSubmit();

    expect(usernameControl?.touched).toBeTruthy();
    expect(emailControl?.touched).toBeTruthy();
    expect(passwordControl?.touched).toBeTruthy();
  });
});
