module.exports = {
  projects: [
    {
      displayName: 'API Tests',
      testMatch: [
        '<rootDir>/api/**/*.spec.ts',
        '<rootDir>/api/**/*.e2e-spec.ts',
      ],
      testEnvironment: 'node',
      transform: {
        '^.+\\.(t|j)s$': 'ts-jest',
      },
      moduleFileExtensions: ['js', 'json', 'ts'],
      collectCoverageFrom: ['<rootDir>/api/src/**/*.(t|j)s'],
      coverageDirectory: '<rootDir>/coverage/api',
      setupFilesAfterEnv: ['<rootDir>/api/test/jest.setup.ts'],
    },
    {
      displayName: 'Web Tests',
      testMatch: ['<rootDir>/web/src/**/*.spec.ts'],
      testEnvironment: 'jsdom',
      transform: {
        '^.+\\.(t|j)s$': 'ts-jest',
      },
      moduleFileExtensions: ['js', 'json', 'ts'],
      collectCoverageFrom: ['<rootDir>/web/src/**/*.(t|j)s'],
      coverageDirectory: '<rootDir>/coverage/web',
      setupFilesAfterEnv: [],
    },
  ],
  collectCoverage: true,
  coverageReporters: ['text', 'lcov', 'html'],
  coverageThreshold: {
    global: {
      branches: 70,
      functions: 70,
      lines: 70,
      statements: 70,
    },
  },
  testTimeout: 30000,
};
