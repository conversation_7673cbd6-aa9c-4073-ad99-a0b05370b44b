// Sale Form Component Styles
.create-sale-card {
  margin-bottom: 24px;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  overflow: hidden;

  ion-card-header {
    background: linear-gradient(
      135deg,
      var(--ion-color-primary),
      var(--ion-color-secondary)
    );
    color: white;
    padding: 24px;

    ion-card-title {
      display: flex;
      align-items: center;
      font-size: 1.5rem;
      font-weight: 600;
      margin: 0;

      .title-icon {
        margin-right: 12px;
        font-size: 1.8rem;
      }
    }

    ion-card-subtitle {
      color: rgba(255, 255, 255, 0.9);
      margin-top: 8px;
      font-size: 1rem;
    }
  }

  ion-card-content {
    padding: 24px;
  }
}

// Form Styles
.sale-form {
  .form-alert {
    margin-bottom: 20px;
    border-radius: 8px;
    padding: 12px 16px;
    background: var(--ion-color-danger-tint);
    border-left: 4px solid var(--ion-color-danger);
  }

  .form-section {
    margin-bottom: 32px;
    padding: 20px;
    background: var(--ion-color-step-100);
    border-radius: 12px;
    border: 1px solid var(--ion-color-step-200);

    .section-title {
      display: flex;
      align-items: center;
      font-size: 1.2rem;
      font-weight: 600;
      color: var(--ion-color-primary);
      margin: 0 0 20px 0;
      padding-bottom: 12px;
      border-bottom: 2px solid var(--ion-color-primary-tint);

      .section-icon {
        margin-right: 8px;
        font-size: 1.3rem;
      }
    }

    .form-row {
      display: grid;
      grid-template-columns: 1fr;
      gap: 16px;
      margin-bottom: 16px;

      @media (min-width: 768px) {
        grid-template-columns: 1fr 1fr;
      }
    }

    .form-item {
      --background: var(--ion-color-step-50);
      --border-radius: 8px;
      --padding-start: 16px;
      --padding-end: 16px;
      --padding-top: 16px;
      --padding-bottom: 16px;
      margin-bottom: 8px;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
      transition: all 0.3s ease;

      &:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }

      &.error {
        --border-color: var(--ion-color-danger);
        --background: var(--ion-color-danger-tint);
        box-shadow: 0 0 0 2px var(--ion-color-danger-tint);
      }

      ion-label {
        font-weight: 500;
        color: var(--ion-color-step-900);

        .required {
          color: var(--ion-color-danger);
          font-weight: 600;
        }
      }

      ion-input,
      ion-textarea,
      ion-datetime {
        --color: var(--ion-color-step-900);
        --placeholder-color: var(--ion-color-step-500);
        font-size: 1rem;
      }

      ion-note {
        font-size: 0.85rem;
        color: var(--ion-color-step-600);
        margin-top: 4px;
      }
    }
  }

  .error-messages {
    margin: 16px 0;
    padding: 16px;
    background: var(--ion-color-danger-tint);
    border-radius: 8px;
    border-left: 4px solid var(--ion-color-danger);

    .error-item {
      --background: transparent;
      --padding-start: 0;
      --padding-end: 0;
      --padding-top: 4px;
      --padding-bottom: 4px;
      margin-bottom: 8px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .submit-section {
    margin-top: 32px;
    padding-top: 24px;
    border-top: 2px solid var(--ion-color-light);

    .submit-button {
      --border-radius: 12px;
      --padding-top: 16px;
      --padding-bottom: 16px;
      font-size: 1.1rem;
      font-weight: 600;
      text-transform: none;
      height: 56px;
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;

      &:hover:not([disabled]) {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
      }

      &[disabled] {
        opacity: 0.6;
        transform: none;
      }
    }
  }
}

// Focus States
.form-item:focus-within {
  box-shadow: 0 0 0 2px var(--ion-color-primary-tint);
}

// Responsive Design
@media (max-width: 767px) {
  .create-sale-card {
    margin-bottom: 16px;

    ion-card-header {
      padding: 16px;

      ion-card-title {
        font-size: 1.3rem;
      }
    }

    ion-card-content {
      padding: 16px;
    }
  }

  .sale-form {
    .form-section {
      padding: 16px;
      margin-bottom: 24px;

      .section-title {
        font-size: 1.1rem;
      }
    }

    .form-item {
      --padding-start: 12px;
      --padding-end: 12px;
      --padding-top: 12px;
      --padding-bottom: 12px;
    }
  }
}
