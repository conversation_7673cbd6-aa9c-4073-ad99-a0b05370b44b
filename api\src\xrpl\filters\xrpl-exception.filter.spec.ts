import { Test, TestingModule } from '@nestjs/testing';
import { ArgumentsHost, HttpStatus } from '@nestjs/common';
import { Response } from 'express';
import {
  XrplExceptionFilter,
  XrplException,
  createXrplException,
} from './xrpl-exception.filter';

describe('XrplExceptionFilter', () => {
  let filter: XrplExceptionFilter;
  let mockResponse: Partial<Response>;
  let mockRequest: any;
  let mockHost: ArgumentsHost;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [XrplExceptionFilter],
    }).compile();

    filter = module.get<XrplExceptionFilter>(XrplExceptionFilter);

    mockResponse = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
    };

    mockRequest = {
      url: '/api/xrpl/balance',
      method: 'GET',
    };

    mockHost = {
      switchToHttp: jest.fn().mockReturnValue({
        getResponse: () => mockResponse,
        getRequest: () => mockRequest,
      }),
      getArgs: jest.fn(),
      getArgByIndex: jest.fn(),
      switchToRpc: jest.fn(),
      switchToWs: jest.fn(),
      getType: jest.fn(),
    } as ArgumentsHost;
  });

  describe('XrplException', () => {
    it('should create XrplException with correct properties', () => {
      const exception = new XrplException(
        'Test error message',
        'TEST_ERROR',
        HttpStatus.BAD_REQUEST,
        { original: 'error' },
      );

      expect(exception.message).toBe('Test error message');
      expect(exception.code).toBe('TEST_ERROR');
      expect(exception.httpStatus).toBe(HttpStatus.BAD_REQUEST);
      expect(exception.originalError).toEqual({ original: 'error' });
    });
  });

  describe('createXrplException helper', () => {
    it('should create XrplException using helper function', () => {
      const exception = createXrplException(
        'Helper error message',
        'HELPER_ERROR',
        HttpStatus.SERVICE_UNAVAILABLE,
      );

      expect(exception).toBeInstanceOf(XrplException);
      expect(exception.message).toBe('Helper error message');
      expect(exception.code).toBe('HELPER_ERROR');
      expect(exception.httpStatus).toBe(HttpStatus.SERVICE_UNAVAILABLE);
    });
  });

  describe('filter.catch', () => {
    it('should handle XrplException correctly', () => {
      const exception = new XrplException(
        'Custom XRPL error',
        'CUSTOM_ERROR',
        HttpStatus.BAD_REQUEST,
      );

      filter.catch(exception, mockHost);

      expect(mockResponse.status).toHaveBeenCalledWith(HttpStatus.BAD_REQUEST);
      expect(mockResponse.json).toHaveBeenCalledWith(
        expect.objectContaining({
          statusCode: HttpStatus.BAD_REQUEST,
          message: 'Custom XRPL error',
          code: 'CUSTOM_ERROR',
          error: expect.objectContaining({
            error: 'Bad Request',
            message: 'Custom XRPL error',
            statusCode: HttpStatus.BAD_REQUEST,
          }),
          timestamp: expect.any(String),
          path: '/api/xrpl/balance',
        }),
      );
    });

    it('should handle XRPL SDK errors with engine results', () => {
      const sdkError = {
        name: 'RippledError',
        data: {
          engine_result: 'tecUNFUNDED_PAYMENT',
          engine_result_message: 'Insufficient balance',
        },
        message: 'Transaction failed',
      };

      filter.catch(sdkError, mockHost);

      expect(mockResponse.status).toHaveBeenCalledWith(HttpStatus.BAD_REQUEST);
      expect(mockResponse.json).toHaveBeenCalledWith(
        expect.objectContaining({
          statusCode: HttpStatus.BAD_REQUEST,
          message: 'Insufficient balance to complete the transaction',
          code: 'tecUNFUNDED_PAYMENT',
          error: expect.objectContaining({
            error: 'Bad Request',
            message: 'Insufficient balance to complete the transaction',
            statusCode: HttpStatus.BAD_REQUEST,
          }),
        }),
      );
    });

    it('should handle network disconnection errors', () => {
      const networkError = {
        name: 'RippledError',
        error: 'disconnected',
        message: 'Connection lost',
      };

      filter.catch(networkError, mockHost);

      expect(mockResponse.status).toHaveBeenCalledWith(
        HttpStatus.SERVICE_UNAVAILABLE,
      );
      expect(mockResponse.json).toHaveBeenCalledWith(
        expect.objectContaining({
          statusCode: HttpStatus.SERVICE_UNAVAILABLE,
          message: 'XRPL network connection lost. Please try again.',
          code: 'NETWORK_DISCONNECTED',
        }),
      );
    });

    it('should handle timeout errors', () => {
      const timeoutError = {
        name: 'XrplError',
        error: 'timeout',
        message: 'Request timed out',
      };

      filter.catch(timeoutError, mockHost);

      expect(mockResponse.status).toHaveBeenCalledWith(
        HttpStatus.REQUEST_TIMEOUT,
      );
      expect(mockResponse.json).toHaveBeenCalledWith(
        expect.objectContaining({
          statusCode: HttpStatus.REQUEST_TIMEOUT,
          message: 'XRPL request timed out. Please try again.',
          code: 'REQUEST_TIMEOUT',
        }),
      );
    });

    it('should handle unknown XRPL errors with default mapping', () => {
      const unknownError = {
        name: 'UnknownError',
        message: 'Some unknown error',
      };

      filter.catch(unknownError, mockHost);

      // The filter now catches all errors and maps unknown ones to InternalServerError
      expect(mockResponse.status).toHaveBeenCalledWith(
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
      expect(mockResponse.json).toHaveBeenCalledWith(
        expect.objectContaining({
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          message: 'An unexpected error occurred',
          code: 'INTERNAL_ERROR',
        }),
      );
    });

    it('should handle non-XRPL errors as internal server errors', () => {
      const genericError = new Error('Generic error');

      filter.catch(genericError, mockHost);

      expect(mockResponse.status).toHaveBeenCalledWith(
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
      expect(mockResponse.json).toHaveBeenCalledWith(
        expect.objectContaining({
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          message: 'An unexpected error occurred',
          code: 'INTERNAL_ERROR',
        }),
      );
    });
  });

  describe('error mappings', () => {
    it('should map tecUNFUNDED_PAYMENT to BadRequestException', () => {
      const sdkError = {
        data: { engine_result: 'tecUNFUNDED_PAYMENT' },
      };

      filter.catch(sdkError, mockHost);

      expect(mockResponse.status).toHaveBeenCalledWith(HttpStatus.BAD_REQUEST);
      expect(mockResponse.json).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Insufficient balance to complete the transaction',
        }),
      );
    });

    it('should map tecFROZEN to UnprocessableEntityException', () => {
      const sdkError = {
        data: { engine_result: 'tecFROZEN' },
      };

      filter.catch(sdkError, mockHost);

      expect(mockResponse.status).toHaveBeenCalledWith(
        HttpStatus.UNPROCESSABLE_ENTITY,
      );
      expect(mockResponse.json).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Account is frozen',
        }),
      );
    });

    it('should map tecINTERNAL to InternalServerErrorException', () => {
      const sdkError = {
        data: { engine_result: 'tecINTERNAL' },
      };

      filter.catch(sdkError, mockHost);

      expect(mockResponse.status).toHaveBeenCalledWith(
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
      expect(mockResponse.json).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Internal XRPL error occurred',
        }),
      );
    });
  });
});
