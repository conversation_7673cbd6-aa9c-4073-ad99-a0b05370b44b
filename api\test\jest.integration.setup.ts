import { config } from 'dotenv';
import { existsSync } from 'fs';

import 'jest-extended';

// Load environment variables for testing if .env.test exists
if (existsSync('.env.test')) {
  config({ path: '.env.test' });
}

// Set test environment
process.env.NODE_ENV = 'test';

// Set test database configuration
process.env.DATABASE_NAME = process.env.DATABASE_NAME || 'test_db';
process.env.JWT_SECRET =
  process.env.JWT_SECRET || 'test-super-secret-jwt-key-for-testing-32-chars';

// Set XRPL test configuration
process.env.XRPL_NETWORK = process.env.XRPL_NETWORK || 'testnet';
process.env.XRPL_SERVER =
  process.env.XRPL_SERVER || 'wss://s.altnet.rippletest.net:51233';

// Disable NestJS Logger during tests
import { Logger } from '@nestjs/common';

// Override Logger methods to do nothing
Logger.prototype.log = () => {};
Logger.prototype.error = () => {};
Logger.prototype.warn = () => {};
Logger.prototype.debug = () => {};
Logger.prototype.verbose = () => {};

// Mock console methods to reduce noise in tests
const originalConsole = { ...console };
beforeAll(() => {
  console.log = jest.fn();
  console.warn = jest.fn();
  console.error = jest.fn();
});

afterAll(() => {
  console.log = originalConsole.log.bind(console);
  console.warn = originalConsole.warn.bind(console);
  console.error = originalConsole.error.bind(console);
});

// Global test timeout for integration tests
jest.setTimeout(60000);

// Mock XRPL network calls for integration tests
jest.mock('xrpl', () => {
  const originalModule = jest.requireActual('xrpl');

  return {
    ...originalModule,
    Client: jest.fn().mockImplementation(() => ({
      connect: jest.fn().mockResolvedValue(undefined),
      disconnect: jest.fn().mockResolvedValue(undefined),
      isConnected: jest.fn().mockReturnValue(true),
      on: jest.fn(),
      request: jest.fn().mockImplementation((method: string) => {
        // Mock common XRPL API responses
        switch (method) {
          case 'account_info':
            return Promise.resolve({
              result: {
                account_data: {
                  Balance: '**********',
                  Sequence: 123,
                },
                ledger_index: 12345,
              },
            });
          case 'server_info':
            return Promise.resolve({
              result: {
                info: {
                  complete_ledgers: '1-1000',
                },
              },
            });
          case 'account_lines':
            return Promise.resolve({
              result: {
                lines: null,
              },
            });
          default:
            return Promise.resolve({ result: {} });
        }
      }),
      submit: jest.fn().mockResolvedValue({
        result: {
          engine_result: 'tesSUCCESS',
          engine_result_message: 'The transaction was applied.',
          tx_blob: 'mock_transaction_blob',
          tx_json: {
            hash: 'mock_transaction_hash',
          },
        },
      }),
      submitAndWait: jest.fn().mockResolvedValue({
        result: {
          engine_result: 'tesSUCCESS',
          engine_result_message: 'The transaction was applied.',
          tx_blob: 'mock_transaction_blob',
          tx_json: {
            hash: 'mock_transaction_hash',
          },
        },
      }),
      autofill: jest.fn().mockImplementation((tx: any) => ({
        ...tx,
        Fee: '10000',
        Sequence: 123,
        LastLedgerSequence: 1000,
      })),
    })),
    Wallet: {
      ...originalModule.Wallet,
      fromSeed: jest.fn().mockReturnValue({
        address: 'rTestWalletAddress123456789',
        publicKey: 'mock_public_key',
        privateKey: 'mock_private_key',
        sign: jest.fn().mockReturnValue({ tx_blob: 'signedTransactionBlob' }),
      }),
    },
  };
});

// Setup global test utilities
declare global {
  var testUtils: {
    mockXrplResponses: Map<string, any>;
    setMockXrplResponse: (method: string, response: any) => void;
    getMockXrplResponse: (method: string) => any;
  };
}

global.testUtils = {
  // Add any global test utilities here
  mockXrplResponses: new Map(),
  setMockXrplResponse: (method: string, response: any) => {
    global.testUtils.mockXrplResponses.set(method, response);
  },
  getMockXrplResponse: (method: string) => {
    return global.testUtils.mockXrplResponses.get(method);
  },
};
