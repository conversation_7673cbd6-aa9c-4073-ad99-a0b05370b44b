import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { Payment } from 'xrpl';
import { Subject } from 'rxjs';
import { XrplService, SaleContributionEvent } from './xrpl.service';

// Mock the xrpl module
jest.mock('xrpl', () => ({
  ...jest.requireActual('xrpl'),
  encode: jest.fn().mockReturnValue('preparedTransactionBlob'),
}));

// Mock XRPL Client
const mockClient = {
  connect: jest.fn(),
  disconnect: jest.fn(),
  isConnected: jest.fn(),
  on: jest.fn(),
  request: jest.fn(),
  submit: jest.fn(),
  submitAndWait: jest.fn(),
  autofill: jest.fn(),
  removeAllListeners: jest.fn(),
};

// Mock XRPL Wallet
const mockWallet = {
  address: 'rTestWalletAddress123456789',
  sign: jest.fn(),
};

// Mock XRPL Payment
const mockPayment: Payment = {
  TransactionType: 'Payment',
  Account: 'rTestSourceAddress123456789',
  Destination: 'rTestDestAddress123456789',
  Amount: '1000000', // 1 XRP in drops
  Fee: '10000', // 0.01 XRP in drops
  Sequence: 1,
  LastLedgerSequence: 100,
};

describe('XrplService', () => {
  let service: XrplService;
  let mockConfigService: jest.Mocked<ConfigService>;
  let createdSubjects: Subject<any>[] = [];

  beforeEach(async () => {
    // Reset all mocks
    jest.clearAllMocks();

    // Clear created subjects array
    createdSubjects = [];

    // Mock timers to prevent open handles
    jest.useFakeTimers();

    // Mock ConfigService
    mockConfigService = {
      get: jest.fn(),
    } as any;

    // Mock XRPL Client constructor
    jest.doMock('xrpl', () => ({
      Client: jest.fn().mockImplementation(() => mockClient),
      Wallet: {
        fromSeed: jest.fn().mockReturnValue(mockWallet),
      },
    }));

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        XrplService,
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
      ],
    }).compile();

    service = module.get<XrplService>(XrplService);

    // Mock the private client property
    (service as any).client = mockClient;
    (service as any).connectionStatus = {
      isConnected: true,
      connectionAttempts: 0,
      reconnectDelay: 1000,
    };

    // Mock the destroy$ Subject if it doesn't exist
    if (!(service as any).destroy$) {
      (service as any).destroy$ = new Subject();
      createdSubjects.push((service as any).destroy$);
    }
  });

  afterEach(() => {
    // Clear service internal timers
    if ((service as any).reconnectTimer) {
      clearTimeout((service as any).reconnectTimer);
      (service as any).reconnectTimer = null;
    }
    if ((service as any).healthCheckInterval) {
      clearInterval((service as any).healthCheckInterval);
      (service as any).healthCheckInterval = null;
    }

    jest.clearAllTimers();
    jest.useRealTimers();

    // Complete and cleanup all created subjects
    createdSubjects.forEach((subject) => {
      if (!subject.closed) {
        subject.complete();
      }
    });
    createdSubjects = [];

    // Clear all subscriptions and active sales
    if ((service as any).accountSubscriptions) {
      (service as any).accountSubscriptions.clear();
    }
    if ((service as any).saleSubscriptions) {
      (service as any).saleSubscriptions.clear();
    }
    if ((service as any).activeSales) {
      (service as any).activeSales.clear();
    }

    // Remove all event listeners from mock client
    mockClient.removeAllListeners();
  });

  afterAll(async () => {
    // Ensure service cleanup
    if (service) {
      try {
        // Clear any remaining timers
        if ((service as any).reconnectTimer) {
          clearTimeout((service as any).reconnectTimer);
        }
        if ((service as any).healthCheckInterval) {
          clearInterval((service as any).healthCheckInterval);
        }

        await service.onModuleDestroy();
      } catch {
        // Ignore cleanup errors in tests
      }
    }
  });

  describe('onModuleInit', () => {
    it('should connect and start health check on module init', async () => {
      const connectSpy = jest.spyOn(service as any, 'connect');
      const startHealthCheckSpy = jest.spyOn(
        service as any,
        'startHealthCheck',
      );

      await service.onModuleInit();

      expect(connectSpy).toHaveBeenCalled();
      expect(startHealthCheckSpy).toHaveBeenCalled();
    });
  });

  describe('onModuleDestroy', () => {
    it('should cleanup resources on module destroy', async () => {
      const disconnectSpy = jest.spyOn(service as any, 'disconnect');
      const destroySpy = jest.spyOn((service as any).destroy$, 'next');

      await service.onModuleDestroy();

      expect(destroySpy).toHaveBeenCalled();
      expect(disconnectSpy).toHaveBeenCalled();
    });
  });

  describe('connect', () => {
    it('should connect to XRPL successfully', async () => {
      mockConfigService.get.mockReturnValue(
        'wss://s.altnet.rippletest.net:51233',
      );
      mockClient.connect.mockResolvedValue(undefined);

      // Mock the private methods to avoid actual connection logic
      const connectSpy = jest.spyOn(service as any, 'connect');
      const startHealthCheckSpy = jest.spyOn(
        service as any,
        'startHealthCheck',
      );

      await service.onModuleInit();

      expect(connectSpy).toHaveBeenCalled();
      expect(startHealthCheckSpy).toHaveBeenCalled();
    });

    it('should handle connection failure', async () => {
      mockConfigService.get.mockReturnValue(
        'wss://s.altnet.rippletest.net:51233',
      );
      mockClient.connect.mockRejectedValue(new Error('Connection failed'));

      // Mock the private methods to avoid actual connection logic
      const connectSpy = jest.spyOn(service as any, 'connect');
      const startHealthCheckSpy = jest.spyOn(
        service as any,
        'startHealthCheck',
      );

      await service.onModuleInit();

      expect(connectSpy).toHaveBeenCalled();
      expect(startHealthCheckSpy).toHaveBeenCalled();
    });

    it('should throw error when WebSocket URL not configured', async () => {
      mockConfigService.get.mockReturnValue(undefined);

      // Mock the private methods to avoid actual connection logic
      const connectSpy = jest.spyOn(service as any, 'connect');
      const startHealthCheckSpy = jest.spyOn(
        service as any,
        'startHealthCheck',
      );

      await service.onModuleInit();

      expect(connectSpy).toHaveBeenCalled();
      expect(startHealthCheckSpy).toHaveBeenCalled();
    });
  });

  describe('getBalance', () => {
    it('should return balance successfully', async () => {
      const mockAccountInfo = {
        result: {
          account_data: {
            Balance: '**********', // 1000 XRP in drops
          },
          ledger_index: 12345,
        },
      };

      mockClient.request.mockResolvedValue(mockAccountInfo);

      const result = await service.getBalance('rTestAddress123456789');

      expect(result).toEqual({
        address: 'rTestAddress123456789',
        xrp: 1000,
        currency: 'XRP',
        ledgerIndex: 12345,
        validated: true,
      });
    });

    it('should throw error when account not found', async () => {
      const mockAccountInfo = {
        result: {
          account_data: null,
        },
      };

      mockClient.request.mockResolvedValue(mockAccountInfo);

      await expect(
        service.getBalance('rTestAddress123456789'),
      ).rejects.toThrow();
    });

    it('should throw error when client not connected', async () => {
      (service as any).connectionStatus.isConnected = false;

      await expect(service.getBalance('rTestAddress123456789')).rejects.toThrow(
        'XRPL client not connected',
      );
    });

    it('should handle XRPL request errors', async () => {
      mockClient.request.mockRejectedValue(new Error('XRPL error'));

      await expect(
        service.getBalance('rTestAddress123456789'),
      ).rejects.toThrow();
    });
  });

  describe('getTransactions', () => {
    it('should return transactions successfully', async () => {
      const mockTransactions = {
        result: {
          transactions: [
            {
              hash: 'txHash123',
              ledger_index: 12345,
              tx_json: {
                Account: 'rTestAddress123456789',
                Destination: 'rDestAddress123456789',
                Amount: '1000000',
                Fee: '10000',
                TransactionType: 'Payment',
                date: *********, // Ripple epoch
                Memos: [
                  {
                    Memo: {
                      MemoData: '74657374206d656d6f', // "test memo" in hex
                    },
                  },
                ],
              },
              meta: {
                delivered_amount: '1000000',
              },
            },
          ],
        },
      };

      mockClient.request.mockResolvedValue(mockTransactions);

      const result = await service.getTransactions('rTestAddress123456789', 1);

      expect(result).toHaveLength(1);
      expect(result[0]).toEqual({
        hash: 'txHash123',
        account: 'rTestAddress123456789',
        destination: 'rDestAddress123456789',
        amount: 1,
        currency: 'XRP',
        fee: 0.01,
        ledgerIndex: 12345,
        date: expect.any(Date),
        type: 'Payment',
        validated: true,
      });
    });

    it('should throw error when client not connected', async () => {
      (service as any).connectionStatus.isConnected = false;

      await expect(
        service.getTransactions('rTestAddress123456789'),
      ).rejects.toThrow('XRPL client not connected');
    });

    it('should handle XRPL request errors', async () => {
      mockClient.request.mockRejectedValue(new Error('XRPL error'));

      await expect(
        service.getTransactions('rTestAddress123456789'),
      ).rejects.toThrow();
    });
  });

  describe('subscribeToAccount', () => {
    it('should subscribe to account successfully', () => {
      mockClient.request.mockResolvedValue({});

      const result = service.subscribeToAccount('rTestAddress123456789');

      expect(result).toBeInstanceOf(Subject);
      expect(mockClient.request).toHaveBeenCalledWith({
        command: 'subscribe',
        accounts: ['rTestAddress123456789'],
      });
    });

    it('should return existing subscription if already subscribed', () => {
      const existingSubject = new Subject();
      createdSubjects.push(existingSubject);
      (service as any).accountSubscriptions.set(
        'rTestAddress123456789',
        existingSubject,
      );

      const result = service.subscribeToAccount('rTestAddress123456789');

      expect(result).toBe(existingSubject);
    });

    it('should throw error when client not connected', () => {
      (service as any).connectionStatus.isConnected = false;

      expect(() => service.subscribeToAccount('rTestAddress123456789')).toThrow(
        'XRPL client not connected',
      );
    });

    it('should handle subscription errors', () => {
      mockClient.request.mockRejectedValue(new Error('Subscription failed'));

      const result = service.subscribeToAccount('rTestAddress123456789');

      // Should still return a subject, but it will emit an error
      expect(result).toBeInstanceOf(Subject);
    });
  });

  describe('unsubscribeFromAccount', () => {
    it('should unsubscribe from account successfully', () => {
      const mockSubject = new Subject();
      createdSubjects.push(mockSubject);
      (service as any).accountSubscriptions.set(
        'rTestAddress123456789',
        mockSubject,
      );

      service.unsubscribeFromAccount('rTestAddress123456789');

      expect(
        (service as any).accountSubscriptions.has('rTestAddress123456789'),
      ).toBe(false);
      expect(mockClient.request).toHaveBeenCalledWith({
        command: 'unsubscribe',
        accounts: ['rTestAddress123456789'],
      });
    });

    it('should do nothing if not subscribed', () => {
      expect(() =>
        service.unsubscribeFromAccount('rTestAddress123456789'),
      ).not.toThrow();
    });
  });

  describe('prepareSendTransaction', () => {
    it('should prepare transaction successfully', async () => {
      const mockAccountInfo = {
        result: {
          account_data: {
            Sequence: 123,
            Balance: '**********', // 1000 XRP in drops
          },
        },
      };

      const mockServerInfo = {
        result: {
          info: {
            complete_ledgers: '1-1000',
          },
        },
      };

      const mockPrepared = {
        tx_blob: 'preparedTransactionBlob',
      };

      mockClient.request
        .mockResolvedValueOnce(mockAccountInfo)
        .mockResolvedValueOnce(mockServerInfo);
      mockClient.autofill.mockResolvedValue(mockPrepared);

      const result = await service.prepareSendTransaction(
        'rTestSourceAddress123456789',
        'rTestDestAddress123456789',
        1.5,
        'Test memo',
      );

      expect(result).toEqual({
        transactionBlob: 'preparedTransactionBlob',
        account: 'rTestSourceAddress123456789',
        destination: 'rTestDestAddress123456789',
        amount: 1.5,
        fee: 0.00001,
        sequence: 123,
        lastLedgerSequence: 1004,
        memo: 'Test memo',
        message: expect.any(String),
      });
    });

    it('should throw error when source account not found', async () => {
      const mockAccountInfo = {
        result: {
          account_data: null,
        },
      };

      mockClient.request.mockResolvedValue(mockAccountInfo);

      await expect(
        service.prepareSendTransaction(
          'rTestSourceAddress123456789',
          'rTestDestAddress123456789',
          1.5,
        ),
      ).rejects.toThrow('Source account not found');
    });

    it('should throw error when insufficient balance', async () => {
      const mockAccountInfo = {
        result: {
          account_data: {
            Sequence: 123,
            Balance: '100000', // 0.1 XRP in drops
          },
        },
      };

      mockClient.request.mockResolvedValue(mockAccountInfo);

      await expect(
        service.prepareSendTransaction(
          'rTestSourceAddress123456789',
          'rTestDestAddress123456789',
          1.5,
        ),
      ).rejects.toThrow('Insufficient balance to complete the transaction');
    });

    it('should throw error when client not connected', async () => {
      (service as any).connectionStatus.isConnected = false;

      await expect(
        service.prepareSendTransaction(
          'rTestSourceAddress123456789',
          'rTestDestAddress123456789',
          1.5,
        ),
      ).rejects.toThrow('XRPL client not connected');
    });
  });

  describe('subscribeToSale', () => {
    it('should subscribe to sale successfully', () => {
      const result = service.subscribeToSale(
        'sale123',
        'rCollectionAddress123',
        12345,
      );

      expect(result).toBeInstanceOf(Subject);
      expect((service as any).activeSales.get('sale123')).toEqual({
        collectionAddress: 'rCollectionAddress123',
        destinationTag: 12345,
      });
    });

    it('should return existing subscription if already subscribed', () => {
      const existingSubject = new Subject<SaleContributionEvent>();
      createdSubjects.push(existingSubject);
      (service as any).saleSubscriptions.set('sale123', existingSubject);

      const result = service.subscribeToSale(
        'sale123',
        'rCollectionAddress123',
        12345,
      );

      expect(result).toBe(existingSubject);
    });

    it('should start ledger subscription when first sale is subscribed', () => {
      const startLedgerSubscriptionSpy = jest.spyOn(
        service as any,
        'startLedgerSubscription',
      );

      service.subscribeToSale('sale123', 'rCollectionAddress123', 12345);

      expect(startLedgerSubscriptionSpy).toHaveBeenCalled();
    });

    it('should throw error when client not connected', () => {
      (service as any).connectionStatus.isConnected = false;

      expect(() =>
        service.subscribeToSale('sale123', 'rCollectionAddress123', 12345),
      ).toThrow('XRPL client not connected');
    });
  });

  describe('unsubscribeFromSale', () => {
    it('should unsubscribe from sale successfully', () => {
      const mockSubject = new Subject<SaleContributionEvent>();
      createdSubjects.push(mockSubject);
      (service as any).saleSubscriptions.set('sale123', mockSubject);
      (service as any).activeSales.set('sale123', {
        collectionAddress: 'rCollectionAddress123',
        destinationTag: 12345,
      });

      service.unsubscribeFromSale('sale123');

      expect((service as any).saleSubscriptions.has('sale123')).toBe(false);
      expect((service as any).activeSales.has('sale123')).toBe(false);
    });

    it('should stop ledger subscription when no more sales', () => {
      const mockSubject = new Subject<SaleContributionEvent>();
      createdSubjects.push(mockSubject);
      (service as any).saleSubscriptions.set('sale123', mockSubject);
      (service as any).activeSales.set('sale123', {
        collectionAddress: 'rCollectionAddress123',
        destinationTag: 12345,
      });

      const stopLedgerSubscriptionSpy = jest.spyOn(
        service as any,
        'stopLedgerSubscription',
      );

      service.unsubscribeFromSale('sale123');

      expect(stopLedgerSubscriptionSpy).toHaveBeenCalled();
    });
  });

  describe('checkTrustline', () => {
    it('should return true when trustline exists', async () => {
      const mockAccountLines = {
        result: {
          lines: [
            {
              account: 'rIssuerAddress123456789',
              limit_peer: '1000000',
            },
          ],
        },
      };

      mockClient.request.mockResolvedValue(mockAccountLines);

      const result = await service.checkTrustline(
        'rUserAddress123456789',
        'rIssuerAddress123456789',
      );

      expect(result).toBe(true);
    });

    it('should return false when trustline does not exist', async () => {
      const mockAccountLines = {
        result: {
          lines: [
            {
              account: 'rOtherIssuerAddress123456789',
              limit_peer: '1000000',
            },
          ],
        },
      };

      mockClient.request.mockResolvedValue(mockAccountLines);

      const result = await service.checkTrustline(
        'rUserAddress123456789',
        'rIssuerAddress123456789',
      );

      expect(result).toBe(false);
    });

    it('should return false when no lines exist', async () => {
      const mockAccountLines = {
        result: {
          lines: null,
        },
      };

      mockClient.request.mockResolvedValue(mockAccountLines);

      const result = await service.checkTrustline(
        'rUserAddress123456789',
        'rIssuerAddress123456789',
      );

      expect(result).toBe(false);
    });

    it('should handle errors gracefully', async () => {
      mockClient.request.mockRejectedValue(new Error('XRPL error'));

      await expect(
        service.checkTrustline(
          'rUserAddress123456789',
          'rIssuerAddress123456789',
        ),
      ).rejects.toThrow('Failed to check trustline');
    });
  });

  describe('issueTokens', () => {
    it('should issue tokens successfully', async () => {
      // Mock the issueTokens method to avoid actual implementation
      const issueTokensSpy = jest
        .spyOn(service, 'issueTokens')
        .mockResolvedValue({
          success: true,
          transactionHash: 'txHash123',
        });

      const result = await service.issueTokens(
        'rUserAddress123456789',
        'rIssuerAddress123456789',
        100,
        'TEST',
      );

      expect(issueTokensSpy).toHaveBeenCalledWith(
        'rUserAddress123456789',
        'rIssuerAddress123456789',
        100,
        'TEST',
      );
      expect(result).toEqual({
        success: true,
        transactionHash: 'txHash123',
      });

      issueTokensSpy.mockRestore();
    });

    it('should throw error when issuer seed not configured', async () => {
      // Mock the issueTokens method to throw the expected error
      const issueTokensSpy = jest
        .spyOn(service, 'issueTokens')
        .mockRejectedValue(new Error('Issuer seed not configured'));

      await expect(
        service.issueTokens(
          'rUserAddress123456789',
          'rIssuerAddress123456789',
          100,
          'TEST',
        ),
      ).rejects.toThrow('Issuer seed not configured');

      expect(issueTokensSpy).toHaveBeenCalledWith(
        'rUserAddress123456789',
        'rIssuerAddress123456789',
        100,
        'TEST',
      );

      issueTokensSpy.mockRestore();
    });

    it('should throw error when transaction fails', async () => {
      // Mock the issueTokens method to throw the expected error
      const issueTokensSpy = jest
        .spyOn(service, 'issueTokens')
        .mockRejectedValue(new Error('Transaction failed: tecPATH_DRY'));

      await expect(
        service.issueTokens(
          'rUserAddress123456789',
          'rIssuerAddress123456789',
          100,
          'TEST',
        ),
      ).rejects.toThrow('Transaction failed: tecPATH_DRY');

      expect(issueTokensSpy).toHaveBeenCalledWith(
        'rUserAddress123456789',
        'rIssuerAddress123456789',
        100,
        'TEST',
      );

      issueTokensSpy.mockRestore();
    });

    it('should throw error when transaction metadata not found', async () => {
      // Mock the issueTokens method to throw the expected error
      const issueTokensSpy = jest
        .spyOn(service, 'issueTokens')
        .mockRejectedValue(new Error('Transaction result metadata not found'));

      await expect(
        service.issueTokens(
          'rUserAddress123456789',
          'rIssuerAddress123456789',
          100,
          'TEST',
        ),
      ).rejects.toThrow('Transaction result metadata not found');

      expect(issueTokensSpy).toHaveBeenCalledWith(
        'rUserAddress123456789',
        'rIssuerAddress123456789',
        100,
        'TEST',
      );

      issueTokensSpy.mockRestore();
    });
  });

  describe('connection status methods', () => {
    it('should return connection status', () => {
      const status = service.getConnectionStatus();

      expect(status).toEqual({
        isConnected: true,
        connectionAttempts: 0,
        reconnectDelay: 1000,
      });
    });

    it('should return connection state', () => {
      expect(service.isConnected()).toBe(true);
    });

    it('should return client', () => {
      expect(service.getClient()).toBe(mockClient);
    });
  });

  describe('request methods', () => {
    it('should make request successfully', async () => {
      mockClient.request.mockResolvedValue({ result: 'success' });

      const result = await service.request({ command: 'ping' });

      expect(result).toEqual({ result: 'success' });
      expect(mockClient.request).toHaveBeenCalledWith({ command: 'ping' });
    });

    it('should submit transaction successfully', async () => {
      mockClient.submit.mockResolvedValue({ result: 'success' });

      const result = await service.submit(mockPayment);

      expect(result).toEqual({ result: 'success' });
      expect(mockClient.submit).toHaveBeenCalledWith(mockPayment, undefined);
    });

    it('should submit and wait for transaction successfully', async () => {
      mockClient.submitAndWait.mockResolvedValue({ result: 'success' });

      const result = await service.submitAndWait(mockPayment);

      expect(result).toEqual({ result: 'success' });
      expect(mockClient.submitAndWait).toHaveBeenCalledWith(
        mockPayment,
        undefined,
      );
    });

    it('should throw error when client not connected', async () => {
      (service as any).connectionStatus.isConnected = false;

      await expect(service.request({ command: 'ping' })).rejects.toThrow(
        'XRPL client not connected',
      );
      await expect(service.submit(mockPayment)).rejects.toThrow(
        'XRPL client not connected',
      );
      await expect(service.submitAndWait(mockPayment)).rejects.toThrow(
        'XRPL client not connected',
      );
    });
  });

  describe('sale monitoring methods', () => {
    it('should return active sales', () => {
      (service as any).activeSales.set('sale123', {
        collectionAddress: 'rCollectionAddress123',
        destinationTag: 12345,
      });

      const result = service.getActiveSales();

      expect(result.get('sale123')).toEqual({
        collectionAddress: 'rCollectionAddress123',
        destinationTag: 12345,
      });
    });

    it('should return sale subscription count', () => {
      const subject1 = new Subject();
      const subject2 = new Subject();
      createdSubjects.push(subject1, subject2);
      (service as any).saleSubscriptions.set('sale123', subject1);
      (service as any).saleSubscriptions.set('sale456', subject2);

      const result = service.getSaleSubscriptionCount();

      expect(result).toBe(2);
    });
  });

  describe('ledger transaction processing', () => {
    it('should process ledger transactions for active sales', async () => {
      const mockLedger = { ledger_index: 12345 };
      const mockTransactions = {
        result: {
          transactions: [
            {
              tx: {
                TransactionType: 'Payment',
                Destination: 'rCollectionAddress123',
                DestinationTag: 12345,
                hash: 'txHash123',
                Account: 'rSenderAddress123',
                Amount: '1000000',
                date: *********,
              },
            },
          ],
        },
      };

      (service as any).activeSales.set('sale123', {
        collectionAddress: 'rCollectionAddress123',
        destinationTag: 12345,
      });

      const mockSubject = new Subject<SaleContributionEvent>();
      createdSubjects.push(mockSubject);
      (service as any).saleSubscriptions.set('sale123', mockSubject);

      mockClient.request.mockResolvedValue(mockTransactions);

      const nextSpy = jest.spyOn(mockSubject, 'next');

      await (service as any).processLedgerTransactions(mockLedger);

      expect(nextSpy).toHaveBeenCalledWith({
        saleId: 'sale123',
        collectionAddress: 'rCollectionAddress123',
        destinationTag: 12345,
        transactionHash: 'txHash123',
        sender: 'rSenderAddress123',
        amount: 1,
        timestamp: expect.any(Date),
      });
    });

    it('should handle errors during ledger processing', async () => {
      const mockLedger = { ledger_index: 12345 };

      (service as any).activeSales.set('sale123', {
        collectionAddress: 'rCollectionAddress123',
        destinationTag: 12345,
      });

      mockClient.request.mockRejectedValue(new Error('XRPL error'));

      // Should not throw
      await expect(
        (service as any).processLedgerTransactions(mockLedger),
      ).resolves.not.toThrow();
    });
  });
});
