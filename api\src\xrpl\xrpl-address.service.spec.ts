import { Test, TestingModule } from '@nestjs/testing';
import { XrplAddressService } from './xrpl-address.service';

describe('XrplAddressService', () => {
  let service: XrplAddressService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [XrplAddressService],
    }).compile();

    service = module.get<XrplAddressService>(XrplAddressService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('validateAddress', () => {
    it('should validate correct XRPL addresses', () => {
      const validAddresses = [
        'rHb9CJAWyB4rj91VRWn96DkukG4bwdtyTh',
        'r3kmLJN5D28dHuH8vZNUZpMC43pEHpaocV',
      ];

      validAddresses.forEach((address) => {
        expect(service.validateAddress(address)).toBe(true);
      });
    });

    it('should reject invalid XRPL addresses', () => {
      const invalidAddresses = [
        'invalid-address',
        '**********',
        'rHb9CJAWyB4rj91VRWn96DkukG4bwdtyTh1', // too long
        'rHb9CJAWyB4rj91VRWn96DkukG4bwdtyT', // too short
        'xHb9CJAWyB4rj91VRWn96DkukG4bwdtyTh', // wrong prefix
        '', // empty string
        'rHb9CJAWyB4rj91VRWn96DkukG4bwdtyTh!', // invalid character
        'r9cZA1uH4zjBvHxWDbSdH9jKdvTewmxYz', // invalid checksum
        'rU2mEJSLqBRkYLVTv55rFTgQajkYqygBP6', // invalid checksum
      ];

      invalidAddresses.forEach((address) => {
        expect(service.validateAddress(address)).toBe(false);
      });
    });

    it('should handle edge cases gracefully', () => {
      // Test with addresses that have the right format but fail validation
      expect(service.validateAddress('r' + 'A'.repeat(24))).toBe(false);
      expect(service.validateAddress('r' + 'A'.repeat(34))).toBe(false);
      expect(service.validateAddress('r' + 'A'.repeat(23))).toBe(false);
      expect(service.validateAddress('r' + 'A'.repeat(35))).toBe(false);
    });
  });

  describe('validateAndNormalizeAddress', () => {
    it('should return the address if valid', () => {
      const validAddress = 'rHb9CJAWyB4rj91VRWn96DkukG4bwdtyTh';
      expect(service.validateAndNormalizeAddress(validAddress)).toBe(
        validAddress,
      );
    });

    it('should throw error if address is invalid', () => {
      const invalidAddress = 'invalid-address';
      expect(() => service.validateAndNormalizeAddress(invalidAddress)).toThrow(
        'Invalid XRPL address format',
      );
    });
  });

  describe('isValidTestnetAddress', () => {
    it('should accept valid classic addresses for testnet', () => {
      const validAddresses = [
        'rHb9CJAWyB4rj91VRWn96DkukG4bwdtyTh',
        'r3kmLJN5D28dHuH8vZNUZpMC43pEHpaocV',
      ];

      validAddresses.forEach((address) => {
        expect(service.isValidTestnetAddress(address)).toBe(true);
      });
    });

    it('should reject invalid addresses for testnet', () => {
      const invalidAddresses = ['invalid-address', '**********', ''];

      invalidAddresses.forEach((address) => {
        expect(service.isValidTestnetAddress(address)).toBe(false);
      });
    });
  });
});
