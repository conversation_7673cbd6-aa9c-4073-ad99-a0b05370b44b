import {
  ComponentFixture,
  TestBed,
  fakeAsync,
  tick,
} from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { of, throwError, BehaviorSubject } from 'rxjs';
import { XrplConnectPage } from './xrpl-connect.page';
import { ApiService } from '../../services/api/api.service';
import { User, SuccessResponse } from '../../types';

describe('XrplConnectPage', () => {
  let component: XrplConnectPage;
  let fixture: ComponentFixture<XrplConnectPage>;
  let mockApiService: jasmine.SpyObj<ApiService>;
  let mockRouter: jasmine.SpyObj<Router>;
  let currentUserSubject: BehaviorSubject<User | null>;

  const mockUser: User = {
    id: '1',
    username: 'testuser',
    email: '<EMAIL>',
    role: 'user',
  };

  const mockUserWithAddress: User = {
    id: '1',
    username: 'testuser',
    email: '<EMAIL>',
    role: 'user',
    xrplAddress: 'rValidTestnetAddress123456789',
  };

  const mockSuccessResponse: SuccessResponse = {
    success: true,
    message: 'XRPL address successfully linked',
  };

  beforeEach(async () => {
    currentUserSubject = new BehaviorSubject<User | null>(null);

    const apiServiceSpy = jasmine.createSpyObj(
      'ApiService',
      ['linkXrplAddress'],
      {
        currentUser$: currentUserSubject.asObservable(),
      }
    );
    const routerSpy = jasmine.createSpyObj('Router', ['navigate']);

    await TestBed.configureTestingModule({
      imports: [XrplConnectPage, ReactiveFormsModule],
      providers: [
        { provide: ApiService, useValue: apiServiceSpy },
        { provide: Router, useValue: routerSpy },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(XrplConnectPage);
    component = fixture.componentInstance;
    mockApiService = TestBed.inject(ApiService) as jasmine.SpyObj<ApiService>;
    mockRouter = TestBed.inject(Router) as jasmine.SpyObj<Router>;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize form with empty values', () => {
    component.ngOnInit();
    expect(component.xrplForm.get('xrplAddress')?.value).toBe('');
  });

  it('should validate required XRPL address', () => {
    component.ngOnInit(); // Initialize the component
    const addressControl = component.xrplAddressControl;
    expect(addressControl?.hasError('required')).toBeTruthy();
  });

  it('should validate XRPL address format', () => {
    component.ngOnInit(); // Initialize the component
    const addressControl = component.xrplAddressControl;

    // Invalid formats
    addressControl?.setValue('invalid-address');
    expect(addressControl?.hasError('pattern')).toBeTruthy();

    addressControl?.setValue('rShort');
    expect(addressControl?.hasError('pattern')).toBeTruthy();

    addressControl?.setValue('notStartingWithR123456789012345678901234');
    expect(addressControl?.hasError('pattern')).toBeTruthy();

    // Valid format
    addressControl?.setValue('rValidTestnetAddress123456789');
    expect(addressControl?.hasError('pattern')).toBeFalsy();
  });

  it('should show correct error messages', () => {
    component.ngOnInit(); // Initialize the component
    const addressControl = component.xrplAddressControl;

    addressControl?.setValue('');
    expect(component.addressErrorMessage).toBe('XRPL address is required');

    addressControl?.setValue('invalid');
    expect(component.addressErrorMessage).toBe(
      'Invalid XRPL address format. Must start with "r" followed by 24-34 alphanumeric characters.'
    );

    addressControl?.setValue('rValidTestnetAddress123456789');
    expect(component.addressErrorMessage).toBe('');
  });

  it('should not submit form when invalid', () => {
    component.ngOnInit(); // Initialize the component
    component.onSubmit();
    expect(mockApiService.linkXrplAddress).not.toHaveBeenCalled();
  });

  it('should submit form when valid and call API service', () => {
    component.ngOnInit(); // Initialize the component
    const validAddress = 'rValidTestnetAddress123456789';
    component.xrplForm.patchValue({ xrplAddress: validAddress });

    mockApiService.linkXrplAddress.and.returnValue(of(mockSuccessResponse));

    component.onSubmit();

    expect(mockApiService.linkXrplAddress).toHaveBeenCalledWith(validAddress);
    expect(mockRouter.navigate).toHaveBeenCalledWith(['/dashboard']);
  });

  it('should handle successful address linking', () => {
    component.ngOnInit(); // Initialize the component
    const validAddress = 'rValidTestnetAddress123456789';
    component.xrplForm.patchValue({ xrplAddress: validAddress });

    mockApiService.linkXrplAddress.and.returnValue(of(mockSuccessResponse));

    component.onSubmit();

    expect(component.isSubmitting()).toBeFalsy();
  });

  it('should handle address linking error', () => {
    component.ngOnInit(); // Initialize the component
    const validAddress = 'rValidTestnetAddress123456789';
    component.xrplForm.patchValue({ xrplAddress: validAddress });

    const errorResponse = {
      error: { message: 'Address already linked' },
      status: 409,
    };

    mockApiService.linkXrplAddress.and.returnValue(
      throwError(() => errorResponse)
    );

    component.onSubmit();

    expect(component.isSubmitting()).toBeFalsy();
  });

  it('should set loading state during submission', () => {
    component.ngOnInit(); // Initialize the component
    const validAddress = 'rValidTestnetAddress123456789';
    component.xrplForm.patchValue({ xrplAddress: validAddress });

    mockApiService.linkXrplAddress.and.returnValue(of(mockSuccessResponse));

    component.onSubmit();

    expect(component.isSubmitting()).toBeFalsy(); // Should be false after completion
  });

  it('should not submit when already submitting', () => {
    component.ngOnInit(); // Initialize the component
    const validAddress = 'rValidTestnetAddress123456789';
    component.xrplForm.patchValue({ xrplAddress: validAddress });

    component.isSubmitting.set(true);
    component.onSubmit();

    expect(mockApiService.linkXrplAddress).not.toHaveBeenCalled();
  });

  it('should disable form when user already has linked address', fakeAsync(() => {
    currentUserSubject.next(mockUserWithAddress);
    component.ngOnInit();
    tick();
    fixture.detectChanges();

    expect(component.isAlreadyLinked()).toBeTruthy();
    expect(component.xrplForm.disabled).toBeTruthy();
  }));

  it('should enable form when user has no linked address', fakeAsync(() => {
    currentUserSubject.next(mockUser);
    component.ngOnInit();
    tick();
    fixture.detectChanges();

    expect(component.isAlreadyLinked()).toBeFalsy();
    expect(component.xrplForm.disabled).toBeFalsy();
  }));

  it('should navigate to dashboard when go to dashboard is clicked', () => {
    component.onGoToDashboard();
    expect(mockRouter.navigate).toHaveBeenCalledWith(['/dashboard']);
  });

  it('should return correct form validation state', () => {
    component.ngOnInit(); // Initialize the component
    expect(component.isFormValid).toBeFalsy();

    component.xrplForm.patchValue({
      xrplAddress: 'rValidTestnetAddress123456789',
    });
    expect(component.isFormValid).toBeTruthy();

    component.isSubmitting.set(true);
    expect(component.isFormValid).toBeFalsy();
  });
});
