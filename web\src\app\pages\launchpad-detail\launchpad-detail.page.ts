import {
  Component,
  ChangeDetectionStrategy,
  inject,
  OnInit,
  OnD<PERSON>roy,
  signal,
  computed,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { IonicModule, RefresherCustomEvent } from '@ionic/angular';
import { Observable, of, Subject } from 'rxjs';
import { catchError, startWith, takeUntil } from 'rxjs/operators';
import { ApiService } from '../../services/api/api.service';
import { WebSocketService } from '../../services/websocket/websocket.service';
import { QRService } from '../../services/qr/qr.service';
import { Sale, UserAllocation, PreparedTransaction } from '../../types';

export interface ContributionForm {
  amount: string;
  destinationTag: string;
  acceptTerms: boolean;
}

@Component({
  selector: 'app-launchpad-detail',
  templateUrl: './launchpad-detail.page.html',
  styleUrls: [
    './launchpad-detail-shared.scss',
    './launchpad-detail-layout.scss',
    './launchpad-detail-progress.scss',
    './launchpad-detail-trustline.scss',
    './launchpad-detail-contribution.scss',
    './launchpad-detail-realtime.scss',
    './launchpad-detail-actions.scss',
  ],
  standalone: true,
  imports: [CommonModule, IonicModule, ReactiveFormsModule],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class LaunchpadDetailPage implements OnInit, OnDestroy {
  private apiService = inject(ApiService);
  private webSocketService = inject(WebSocketService);
  private qrService = inject(QRService);
  private route = inject(ActivatedRoute);
  private router = inject(Router);
  private fb = inject(FormBuilder);
  private destroy$ = new Subject<void>();

  // Observable properties for use with async pipe
  sale$!: Observable<Sale | null>;
  userAllocation$!: Observable<UserAllocation | null>;

  // State
  isLoading = signal<boolean>(false);
  saleId = signal<string>('');
  preparedTransaction: PreparedTransaction | null = null;
  isTransactionBlobVisible = signal<boolean>(false);
  isAuthenticated = signal<boolean>(false);
  qrCodeDataUrl = signal<string>('');

  // WebSocket signals
  balance = this.webSocketService.balance;
  transactions = this.webSocketService.transactions;
  saleProgress = this.webSocketService.saleProgress;
  isConnected = this.webSocketService.isConnected$;

  // Computed signals
  progressPercentage = computed(() => {
    const progress = this.saleProgress();
    return progress ? progress.progressPercent : 0;
  });

  userAllocationAmount = computed(() => {
    const progress = this.saleProgress();
    return progress ? progress.userAllocation : 0;
  });

  totalRaised = computed(() => {
    const progress = this.saleProgress();
    return progress ? progress.totalRaised : 0;
  });

  contributionForm: FormGroup;

  constructor() {
    this.contributionForm = this.fb.group({
      amount: ['', [Validators.required, Validators.min(0.01)]],
      destinationTag: ['', [Validators.required, Validators.pattern(/^\d+$/)]],
      acceptTerms: [false, [Validators.requiredTrue]],
    });
  }

  ngOnInit() {
    // Check authentication status
    this.isAuthenticated.set(this.apiService.isAuthenticated());

    // Start WebSocket service if ready
    if (this.webSocketService.isReady()) {
      console.log('Starting WebSocket service...');
      this.webSocketService.start();
    } else {
      console.log('WebSocket service not ready - no authentication token');
    }

    // Get sale ID from route parameters
    this.route.params.pipe(takeUntil(this.destroy$)).subscribe(params => {
      const saleId = params['id'];
      if (saleId) {
        this.saleId.set(saleId);
        this.loadData(saleId);

        // Subscribe to real-time updates for this sale (with delay to ensure connection)
        setTimeout(() => {
          if (this.webSocketService.getIsConnected()) {
            this.webSocketService.subscribeToSale(saleId);
            this.webSocketService.requestBalanceUpdate();
            this.webSocketService.requestTransactionsUpdate();
          }
        }, 1000);
      }
    });
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
    // Unsubscribe from sale updates
    this.webSocketService.unsubscribeFromSale();
  }

  private loadData(saleId: string) {
    this.isLoading.set(true);

    // Load sale details
    this.sale$ = this.apiService.getSale(saleId).pipe(
      startWith(null),
      catchError(() => of(null))
    );

    // Load user allocation only if authenticated
    if (this.isAuthenticated()) {
      this.userAllocation$ = this.apiService.getUserAllocation(saleId).pipe(
        startWith(null),
        catchError(() => of(null))
      );
    } else {
      this.userAllocation$ = of(null);
    }

    // Pre-fill destination tag when sale is loaded
    this.sale$.pipe(takeUntil(this.destroy$)).subscribe(sale => {
      if (sale) {
        // Generate a unique destination tag based on sale ID and user
        const destinationTag = this.generateDestinationTag(saleId);
        this.contributionForm.patchValue({
          destinationTag: destinationTag,
        });

        // Generate QR code for trustline setup
        this.generateTrustlineQRCode(sale);
      }
    });

    // Set loading to false after a short delay
    setTimeout(() => this.isLoading.set(false), 500);
  }

  onContribute(): void {
    if (this.contributionForm.valid) {
      const formData = this.contributionForm.value;
      const saleId = this.saleId();

      // Get the current sale to access collection address
      this.sale$.pipe(takeUntil(this.destroy$)).subscribe(sale => {
        if (sale) {
          this.isLoading.set(true);

          // First, prepare the XRP transaction
          this.apiService
            .prepareSendTransaction({
              destination: sale.collectionAddress,
              amount: parseFloat(formData.amount),
            })
            .subscribe({
              next: preparedTx => {
                // Store the prepared transaction for display
                this.preparedTransaction = preparedTx;
                this.isLoading.set(false);

                // Show the transaction blob for user to sign
                this.showTransactionBlob(preparedTx);
              },
              error: error => {
                console.error('Failed to prepare transaction:', error);
                this.isLoading.set(false);
              },
            });
        }
      });
    }
  }

  onClaim(): void {
    const saleId = this.saleId();
    this.isLoading.set(true);

    this.apiService.claimTokens(saleId).subscribe({
      next: response => {
        console.log('Claim successful:', response);
        this.isLoading.set(false);
        // Refresh data after successful claim
        this.loadData(saleId);
      },
      error: error => {
        console.error('Claim failed:', error);
        this.isLoading.set(false);
      },
    });
  }

  onRefreshData(): void {
    const saleId = this.saleId();
    if (saleId) {
      this.loadData(saleId);
    }
  }

  onRefresh(event: RefresherCustomEvent): void {
    const saleId = this.saleId();
    if (saleId) {
      // Refresh API data
      this.loadData(saleId);

      // Request WebSocket updates
      this.webSocketService.requestBalanceUpdate();
      this.webSocketService.requestTransactionsUpdate();
      this.webSocketService.subscribeToSale(saleId);
    }

    // Complete the refresh after a short delay
    setTimeout(() => {
      event.target.complete();
    }, 1000);
  }

  onOpenWebsite(url: string): void {
    window.open(url, '_blank');
  }

  onOpenWhitepaper(url: string): void {
    window.open(url, '_blank');
  }

  onOpenSocialLink(platform: string, url: string): void {
    window.open(url, '_blank');
  }

  getStatusColor(status: string): string {
    switch (status) {
      case 'active':
        return 'success';
      case 'ended':
        return 'primary';
      case 'canceled':
        return 'danger';
      default:
        return 'medium';
    }
  }

  getStatusIcon(status: string): string {
    switch (status) {
      case 'active':
        return 'play-circle';
      case 'ended':
        return 'checkmark-circle';
      case 'canceled':
        return 'close-circle';
      default:
        return 'help-circle';
    }
  }

  getProgressPercentage(raised: number, target: number): number {
    if (target === 0) return 0;
    return Math.min((raised / target) * 100, 100);
  }

  formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  }

  formatCurrency(amount: number): string {
    return amount.toLocaleString('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 6,
    });
  }

  getTimeRemaining(sale: Sale): string {
    if (!sale || sale.status !== 'active') return '';

    const now = new Date();
    const endDate = new Date(sale.end);
    const diff = endDate.getTime() - now.getTime();

    if (diff <= 0) return 'Ended';

    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));

    if (days > 0) {
      return `${days} day${days > 1 ? 's' : ''} remaining`;
    } else {
      return `${hours} hour${hours > 1 ? 's' : ''} remaining`;
    }
  }

  isFormValid(): boolean {
    return (
      this.contributionForm.valid &&
      this.contributionForm.get('acceptTerms')?.value
    );
  }

  canUserClaim(allocation: UserAllocation | null): boolean {
    if (!allocation) return false;

    // Check if sale has ended and user has allocation
    const now = new Date();
    const saleEnded = allocation.saleStatus === 'ended' || allocation.saleEnded;
    const hasAllocation = allocation.allocationAmount > 0;

    return saleEnded && hasAllocation;
  }

  // Helper methods for template compatibility
  getRaisedAmount(sale: Sale): number {
    return sale.softCap;
  }

  getTargetAmount(sale: Sale): number {
    return sale.hardCap;
  }

  getMinContribution(): number {
    // Default minimum contribution - could be made configurable
    return 1;
  }

  getMaxContribution(sale: Sale): number {
    return sale.hardCap;
  }

  getCurrency(): string {
    return 'XRP'; // Default currency
  }

  showTransactionBlob(preparedTx: PreparedTransaction): void {
    this.preparedTransaction = preparedTx;
    this.isTransactionBlobVisible.set(true);
  }

  copyTransactionBlob(): void {
    if (this.preparedTransaction) {
      navigator.clipboard
        .writeText(this.preparedTransaction.transactionBlob)
        .then(() => {
          // Show success toast
          console.log('Transaction blob copied to clipboard');
        })
        .catch(err => {
          console.error('Failed to copy transaction blob:', err);
        });
    }
  }

  openXamanWallet(): void {
    if (this.preparedTransaction) {
      // Create Xaman URL scheme for signing transaction
      const xamanUrl = `xumm://xapp?xapp=sign&blob=${this.preparedTransaction.transactionBlob}`;
      window.open(xamanUrl, '_blank');
    }
  }

  closeTransactionBlob(): void {
    this.isTransactionBlobVisible.set(false);
    this.preparedTransaction = null;
  }

  copyToClipboard(text: string): void {
    navigator.clipboard
      .writeText(text)
      .then(() => {
        console.log('Text copied to clipboard');
        // You could add a toast notification here
      })
      .catch(err => {
        console.error('Failed to copy text:', err);
      });
  }

  generateDestinationTag(saleId: string): string {
    // Generate a unique numeric destination tag based on sale ID
    // This is a simple implementation - in production, you might want a more sophisticated approach
    const timestamp = Date.now().toString().slice(-6); // Last 6 digits of timestamp
    const numericSaleId = saleId.replace(/[^0-9]/g, '').slice(0, 4); // Extract only numeric chars from sale ID
    return `${numericSaleId}${timestamp}`;
  }

  clearDestinationTag(): void {
    this.contributionForm.patchValue({
      destinationTag: '',
    });
  }

  navigateToLogin(): void {
    this.router.navigate(['/login']);
  }

  /**
   * Generate QR code for trustline setup
   */
  private async generateTrustlineQRCode(sale: Sale): Promise<void> {
    try {
      // Generate Xaman deep link for trustline setup
      const trustlineLink = this.qrService.generateXamanTrustlineLink(
        sale.collectionAddress,
        sale.symbol,
        '1000000000' // High limit for trustline
      );

      // Generate QR code as data URL
      const qrDataUrl = await this.qrService.generateDataURL(trustlineLink, {
        width: 200,
        margin: 2,
        color: {
          dark: '#000000',
          light: '#FFFFFF',
        },
      });

      this.qrCodeDataUrl.set(qrDataUrl);
    } catch (error) {
      console.error('Error generating trustline QR code:', error);
    }
  }

  /**
   * Generate QR code for payment
   */
  async generatePaymentQRCode(
    sale: Sale,
    amount: string,
    destinationTag?: string
  ): Promise<string> {
    try {
      // Generate Xaman deep link for payment
      const paymentLink = this.qrService.generateXamanPaymentLink(
        sale.collectionAddress,
        amount,
        'XRP',
        destinationTag
      );

      // Generate QR code as data URL
      return await this.qrService.generateDataURL(paymentLink, {
        width: 200,
        margin: 2,
        color: {
          dark: '#000000',
          light: '#FFFFFF',
        },
      });
    } catch (error) {
      console.error('Error generating payment QR code:', error);
      throw error;
    }
  }
}
