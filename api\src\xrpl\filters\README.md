# XRPL Exception Filter

A comprehensive exception filter for handling XRPL SDK errors and mapping them to appropriate NestJS HTTP exceptions with user-friendly messages.

## Features

- **Automatic XRPL Error Detection**: Automatically identifies XRPL SDK errors, rippled errors, and network issues
- **Comprehensive Error Mapping**: Maps XRPL engine results (tec*, tes*) to appropriate HTTP status codes
- **User-Friendly Messages**: Provides clear, actionable error messages without exposing internal details
- **Network Error Handling**: Special handling for disconnections, timeouts, and network issues
- **Global Application**: Can be applied globally or per module
- **Detailed Logging**: Comprehensive logging for debugging while maintaining security

## Error Mappings

### Transaction Engine Results (tec\*)

- `tecUNFUNDED_PAYMENT` → `400 Bad Request` - Insufficient balance
- `tecPATH_DRY` → `400 Bad Request` - Insufficient balance for path
- `tecFROZEN` → `422 Unprocessable Entity` - Account is frozen
- `tecNO_AUTH` → `422 Unprocessable Entity` - Not authorized
- `tecINTERNAL` → `500 Internal Server Error` - Internal XRPL error

### Network and Connection Errors

- `disconnected` → `503 Service Unavailable` - Network connection lost
- `timeout` → `408 Request Timeout` - Request timed out
- `network` → `503 Service Unavailable` - Network issues

### Account Errors

- `actNotFound` → `400 Bad Request` - Account not found
- `actInsufficientBalance` → `400 Bad Request` - Insufficient balance

## Usage

### 1. Global Application (Recommended)

Apply the filter globally in your `app.module.ts`:

```typescript
import { Module } from '@nestjs/common';
import { APP_FILTER } from '@nestjs/core';
import { XrplExceptionFilter } from './xrpl/filters/xrpl-exception.filter';

@Module({
  // ... other imports
  providers: [
    // ... other providers
    {
      provide: APP_FILTER,
      useClass: XrplExceptionFilter,
    },
  ],
})
export class AppModule {}
```

### 2. Module-Level Application

Apply the filter to a specific module:

```typescript
import { Module } from '@nestjs/common';
import { APP_FILTER } from '@nestjs/core';
import { XrplExceptionFilter } from './filters/xrpl-exception.filter';

@Module({
  // ... other imports
  providers: [
    // ... other providers
    {
      provide: APP_FILTER,
      useClass: XrplExceptionFilter,
    },
  ],
})
export class XrplModule {}
```

### 3. Controller-Level Application

Apply the filter to a specific controller:

```typescript
import { Controller, UseFilters } from '@nestjs/common';
import { XrplExceptionFilter } from './filters/xrpl-exception.filter';

@Controller('xrpl')
@UseFilters(XrplExceptionFilter)
export class XrplController {
  // ... controller methods
}
```

### 4. Method-Level Application

Apply the filter to a specific method:

```typescript
import { Controller, Post, UseFilters } from '@nestjs/common';
import { XrplExceptionFilter } from './filters/xrpl-exception.filter';

@Controller('xrpl')
export class XrplController {
  @Post('send')
  @UseFilters(XrplExceptionFilter)
  async sendTransaction() {
    // ... method implementation
  }
}
```

## Creating Custom XRPL Exceptions

Use the helper function to create custom XRPL exceptions:

```typescript
import { createXrplException } from './filters/xrpl-exception.filter';
import { HttpStatus } from '@nestjs/common';

// Create a custom exception
const exception = createXrplException(
  'Custom error message',
  'CUSTOM_ERROR_CODE',
  HttpStatus.BAD_REQUEST,
);

throw exception;
```

## Error Response Format

All errors are returned in a consistent format:

```json
{
  "statusCode": 400,
  "message": "Insufficient balance to complete the transaction",
  "error": "XRPL Error",
  "code": "tecUNFUNDED_PAYMENT",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "path": "/api/xrpl"
}
```

## Adding Custom Error Mappings

To add custom error mappings, extend the `XRPL_ERROR_MAPPINGS` object:

```typescript
export const XRPL_ERROR_MAPPINGS = {
  // ... existing mappings

  // Add your custom mapping
  CUSTOM_ERROR_CODE: {
    message: 'Your custom error message',
    exception: BadRequestException, // or any other NestJS exception
  },
} as const;
```

## Testing

Run the filter tests:

```bash
npm test -- xrpl-exception.filter.spec.ts
```

## Best Practices

1. **Global Application**: Apply the filter globally for consistent error handling across your application
2. **User-Friendly Messages**: Always provide clear, actionable error messages
3. **Security**: Never expose internal error details or stack traces to users
4. **Logging**: Use the built-in logging for debugging while keeping user responses clean
5. **Custom Exceptions**: Use the helper functions for consistent error creation

## Error Handling Flow

1. **Exception Caught**: Filter catches all exceptions
2. **Type Detection**: Determines if it's an XRPL error, custom exception, or other error
3. **Error Mapping**: Maps XRPL errors to appropriate HTTP exceptions
4. **Response Creation**: Creates consistent error response format
5. **Logging**: Logs detailed error information for debugging
6. **User Response**: Returns clean, user-friendly error message

## Examples

### Handling XRPL SDK Errors

```typescript
try {
  const result = await this.xrplClient.request({ command: 'account_info' });
  return result;
} catch (error) {
  // The filter will automatically catch and handle XRPL SDK errors
  // No need for manual error handling here
  throw error;
}
```

### Custom Error Handling

```typescript
import { createXrplException } from './filters/xrpl-exception.filter';

async validateTransaction(amount: number, balance: number) {
  if (amount > balance) {
    throw createXrplException(
      'Transaction amount exceeds available balance',
      'INSUFFICIENT_BALANCE',
      HttpStatus.BAD_REQUEST
    );
  }
}
```

## Troubleshooting

### Common Issues

1. **Filter Not Applied**: Ensure the filter is properly registered in your module
2. **Errors Not Caught**: Check that the filter is applied at the correct level
3. **Incorrect Status Codes**: Verify your error mappings in `XRPL_ERROR_MAPPINGS`

### Debug Mode

Enable detailed logging by setting the log level:

```typescript
// In your main.ts or configuration
const app = await NestFactory.create(AppModule, {
  logger: ['error', 'warn', 'debug'],
});
```

## Contributing

When adding new error mappings:

1. Add the mapping to `XRPL_ERROR_MAPPINGS`
2. Include appropriate HTTP status codes
3. Provide clear, user-friendly messages
4. Add corresponding tests
5. Update this documentation
