<ion-header [translucent]="true">
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-menu-button></ion-menu-button>
    </ion-buttons>
    <ion-title>Login</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true">
  <div class="login-container">
    <div class="login-card">
      <h1>Welcome Back</h1>
      <p>Sign in to your XRPL Launchpad account</p>

      <form [formGroup]="loginForm" (ngSubmit)="onSubmit()">
        <ion-item>
          <ion-input
            type="text"
            formControlName="username"
            label="Username or Email"
            labelPlacement="stacked"
            placeholder="Enter your username or email"
            [clearInput]="true"
            autocomplete="username"
          ></ion-input>
        </ion-item>
        <div
          class="error-message"
          *ngIf="loginForm.get('username')?.invalid && loginForm.get('username')?.touched"
        >
          <span *ngIf="loginForm.get('username')?.errors?.['required']"
            >Email is required</span
          >
          <span *ngIf="loginForm.get('username')?.errors?.['email']"
            >Please enter a valid email</span
          >
        </div>

        <ion-item>
          <ion-input
            type="password"
            formControlName="password"
            label="Password"
            labelPlacement="stacked"
            placeholder="Enter your password"
            [clearInput]="true"
            autocomplete="current-password"
          ></ion-input>
        </ion-item>
        <div
          class="error-message"
          *ngIf="loginForm.get('password')?.invalid && loginForm.get('password')?.touched"
        >
          <span *ngIf="loginForm.get('password')?.errors?.['required']"
            >Password is required</span
          >
          <span *ngIf="loginForm.get('password')?.errors?.['minlength']"
            >Password must be at least 6 characters</span
          >
        </div>

        <div class="error-message" *ngIf="errorMessage()">
          {{ errorMessage() }}
        </div>

        <ion-button
          expand="block"
          type="submit"
          [disabled]="loginForm.invalid || isLoading()"
          class="login-button"
        >
          {{ isLoading() ? 'Signing In...' : 'Sign In' }}
        </ion-button>

        <ion-button
          fill="clear"
          expand="block"
          (click)="onForgotPassword()"
          class="forgot-password-button"
        >
          Forgot Password?
        </ion-button>

        <ion-button
          fill="clear"
          expand="block"
          routerLink="/register"
          class="signup-link-button"
        >
          Don't have an account? Sign Up
        </ion-button>
      </form>
    </div>
  </div>
</ion-content>
