import { Module } from '@nestjs/common';
import { ConfigModule } from './config/config.module';
import { ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { APP_GUARD, APP_INTERCEPTOR, APP_FILTER } from '@nestjs/core';
import { ThrottlerModule, ThrottlerGuard } from '@nestjs/throttler';
import { ScheduleModule } from '@nestjs/schedule';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { UsersModule } from './users/users.module';
import { AuthModule } from './auth/auth.module';
import { XrplModule } from './xrpl/xrpl.module';
import { LaunchpadModule } from './launchpad/launchpad.module';
import { LoggingInterceptor } from './common/interceptors/logging.interceptor';
import { XrplExceptionFilter } from './xrpl/filters/xrpl-exception.filter';

@Module({
  imports: [
    ConfigModule,
    ScheduleModule.forRoot(),
    ThrottlerModule.forRoot([
      {
        ttl: 60000,
        limit: 100,
      },
    ]),
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => {
        const nodeEnv =
          configService.get<string>('app.nodeEnv') || 'development';

        return {
          type: 'postgres' as const,
          host: String(configService.get('database.host') || 'localhost'),
          port: Number(configService.get('database.port') || 5432),
          username: String(
            configService.get('database.username') || 'postgres',
          ),
          password: String(
            configService.get('database.password') || 'password',
          ),
          database: String(
            configService.get('database.name') || 'xrpl_launchpad',
          ),
          entities: [__dirname + '/**/*.entity{.ts,.js}'],
          synchronize: nodeEnv === 'development',
          logging: nodeEnv === 'development',
          ssl: nodeEnv === 'production' ? { rejectUnauthorized: false } : false,
        };
      },
      inject: [ConfigService],
    }),
    XrplModule,
    UsersModule,
    AuthModule,
    LaunchpadModule,
  ],
  controllers: [AppController],
  providers: [
    AppService,
    {
      provide: APP_GUARD,
      useClass: ThrottlerGuard,
    },
    {
      provide: APP_INTERCEPTOR,
      useClass: LoggingInterceptor,
    },
    {
      provide: APP_FILTER,
      useClass: XrplExceptionFilter,
    },
  ],
})
export class AppModule {}
