import { Injectable } from '@nestjs/common';
import { XrplService, XrplConnectionStatus } from './xrpl/xrpl.service';

export interface HealthStatus {
  status: string;
  timestamp: string;
  xrpl: XrplConnectionStatus;
  uptime: number;
}

@Injectable()
export class AppService {
  constructor(private readonly xrplService: XrplService) {}

  getHello(): string {
    return 'Hello World!';
  }

  getHealthStatus(): HealthStatus {
    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
      xrpl: this.xrplService.getConnectionStatus(),
      uptime: process.uptime(),
    };
  }
}
