services:
  db:
    image: postgres:16-alpine
    container_name: xrpl_db
    restart: unless-stopped
    # Database environment variables (loaded from api/.env)
    environment:
      POSTGRES_DB: ${DATABASE_NAME:-xrpl_launchpad}
      POSTGRES_USER: ${DATABASE_USERNAME:-postgres}
      POSTGRES_PASSWORD: ${DATABASE_PASSWORD:-your_password_here}
    ports:
      - "${DATABASE_PORT:-5432}:5432"
    volumes:
      - db_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U $$POSTGRES_USER -d $$POSTGRES_DB"]
      interval: 5s
      timeout: 3s
      retries: 20

  api:
    build:
      context: .
      dockerfile: ./api/Dockerfile
    container_name: xrpl_api
    restart: unless-stopped
    depends_on:
      db:
        condition: service_healthy
    # Note: All API endpoints are prefixed with /api (e.g., /api/health, /api/users, etc.)
    # Environment variables are loaded from api/.env file
    env_file:
      - api/.env
    environment:
      # Override specific values for Docker environment
      NODE_ENV: production
      DATABASE_HOST: db  # Use Docker service name instead of localhost

    ports:
      - "3000:3000"
    # Health check is built into the Dockerfile

  web:
    build:
      context: .
      dockerfile: ./web/Dockerfile
      args:
        # Where Ionic outputs the production build
        NG_BUILD_OUTPUT: ${NG_BUILD_OUTPUT:-www}
        API_BASE_URL: ${API_BASE_URL:-http://api:3000/api}
    container_name: xrpl_web
    restart: unless-stopped
    depends_on:
      api:
        condition: service_healthy
    environment:
      # Only used if your web app reads at runtime (most Angular apps bake this at build)
      API_BASE_URL: ${API_BASE_URL:-http://api:3000/api}
    ports:
      - "80:80"      # Production port
      - "8100:80"    # Ionic development port (alternative access)
    # Health check is built into the Dockerfile

  # Development service for Ionic with live reload
  web-dev:
    build:
      context: .
      dockerfile: ./web/Dockerfile.dev
    container_name: xrpl_web_dev
    restart: unless-stopped
    depends_on:
      api:
        condition: service_healthy
    environment:
      API_BASE_URL: ${API_BASE_URL:-http://api:3000/api}
    ports:
      - "8100:8100"  # Ionic development server port
    volumes:
      - ./web:/app
      - /app/node_modules
    profiles:
      - dev

volumes:
  db_data:
