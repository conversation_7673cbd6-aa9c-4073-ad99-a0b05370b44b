@echo off
REM XRPL Launchpad Docker Operations Script
REM Windows batch script for common Docker operations

setlocal enabledelayedexpansion

set ACTION=%1
if "%ACTION%"=="" set ACTION=help

if "%ACTION%"=="up" goto up
if "%ACTION%"=="down" goto down
if "%ACTION%"=="build" goto build
if "%ACTION%"=="logs" goto logs
if "%ACTION%"=="restart" goto restart
if "%ACTION%"=="clean" goto clean
if "%ACTION%"=="reset-db" goto reset-db
goto help

:help
echo XRPL Launchpad Docker Operations Script
echo.
echo Usage: docker-ops.bat [Action]
echo.
echo Actions:
echo   up          - Start all services (docker-compose up -d)
echo   down        - Stop all services (docker-compose down)
echo   build       - Build and start all services (docker-compose up --build -d)
echo   logs        - Show logs for all services (docker-compose logs -f)
echo   restart     - Restart all services
echo   clean       - Stop and remove containers, networks, and images
echo   reset-db    - Stop services, remove database volume, and restart
echo   help        - Show this help message (default)
echo.
echo Examples:
echo   docker-ops.bat up
echo   docker-ops.bat build
echo   docker-ops.bat logs
goto end

:up
echo Starting XRPL Launchpad services...
docker-compose up -d
if %ERRORLEVEL% EQU 0 (
    echo Services started successfully!
    echo Frontend: http://localhost:8080
    echo Backend:  http://localhost:3000
    echo Database: localhost:5432
) else (
    echo Failed to start services!
)
goto end

:down
echo Stopping XRPL Launchpad services...
docker-compose down
if %ERRORLEVEL% EQU 0 (
    echo Services stopped successfully!
) else (
    echo Failed to stop services!
)
goto end

:build
echo Building and starting XRPL Launchpad services...
docker-compose up --build -d
if %ERRORLEVEL% EQU 0 (
    echo Services built and started successfully!
    echo Frontend: http://localhost:8080
    echo Backend:  http://localhost:3000
    echo Database: localhost:5432
) else (
    echo Failed to build and start services!
)
goto end

:logs
echo Showing logs for all services (Press Ctrl+C to exit)...
docker-compose logs -f
goto end

:restart
echo Restarting XRPL Launchpad services...
docker-compose restart
if %ERRORLEVEL% EQU 0 (
    echo Services restarted successfully!
) else (
    echo Failed to restart services!
)
goto end

:clean
echo Cleaning up Docker resources...
echo This will remove all containers, networks, and images!
set /p confirmation="Are you sure? (y/N): "
if /i "!confirmation!"=="y" (
    docker-compose down --rmi all --volumes --remove-orphans
    if %ERRORLEVEL% EQU 0 (
        echo Cleanup completed successfully!
    ) else (
        echo Cleanup failed!
    )
) else (
    echo Cleanup cancelled.
)
goto end

:reset-db
echo Resetting database...
echo This will delete all database data!
set /p confirmation="Are you sure? (y/N): "
if /i "!confirmation!"=="y" (
    echo Stopping services...
    docker-compose down
    
    echo Removing database volume...
    docker volume rm xrpl_db_data 2>nul
    
    echo Starting services...
    docker-compose up -d
    
    if %ERRORLEVEL% EQU 0 (
        echo Database reset completed successfully!
    ) else (
        echo Database reset failed!
    )
) else (
    echo Database reset cancelled.
)
goto end

:end
endlocal
