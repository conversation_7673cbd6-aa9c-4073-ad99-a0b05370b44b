import { Component, inject, signal, computed } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';
import { ThemeService, ThemeMode } from '../../services/theme/theme.service';

@Component({
  selector: 'app-theme-toggle',
  templateUrl: './theme-toggle.component.html',
  styleUrls: ['./theme-toggle.component.scss'],
  standalone: true,
  imports: [CommonModule, IonicModule],
})
export class ThemeToggleComponent {
  private themeService = inject(ThemeService);

  // Signals from theme service
  public themeMode = this.themeService.themeMode;
  public currentTheme = this.themeService.currentTheme;

  // Computed properties
  public themeIcon = computed(() => this.themeService.getThemeIcon());
  public themeLabel = computed(() => {
    const mode = this.themeMode();
    switch (mode) {
      case 'light':
        return 'Light theme';
      case 'dark':
        return 'Dark theme';
      case 'system':
        return 'System theme';
      default:
        return 'Theme';
    }
  });

  async toggleTheme(): Promise<void> {
    const nextMode = this.themeService.getNextThemeMode();
    await this.themeService.setThemeMode(nextMode);
  }

  async setThemeMode(mode: ThemeMode): Promise<void> {
    await this.themeService.setThemeMode(mode);
  }
}
