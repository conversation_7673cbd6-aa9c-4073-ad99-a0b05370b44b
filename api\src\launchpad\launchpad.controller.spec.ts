import { Test, TestingModule } from '@nestjs/testing';
import { Reflector } from '@nestjs/core';
import { JwtService } from '@nestjs/jwt';
import { LaunchpadController } from './launchpad.controller';
import { LaunchpadService } from './launchpad.service';
import { UsersService } from '../users/users.service';
import { Sale, SaleStatus } from './entities/sale.entity';
import { UserAllocationDto } from './dto/allocation.dto';
import { ClaimResponseDto } from './dto/claim-response.dto';

describe('LaunchpadController', () => {
  let controller: LaunchpadController;
  let mockLaunchpadService: any;
  let mockJwtService: any;
  let mockUsersService: any;

  beforeEach(async () => {
    mockLaunchpadService = {
      createSale: jest.fn(),
      findAllSales: jest.fn(),
      findActiveSales: jest.fn(),
      findSaleById: jest.fn(),
      updateSale: jest.fn(),
      deleteSale: jest.fn(),
      endSale: jest.fn(),
      cancelSale: jest.fn(),
      createContribution: jest.fn(),
      findUserContributions: jest.fn(),
      findSaleContributions: jest.fn(),
      getSaleStats: jest.fn(),
      getUserAllocation: jest.fn(),
      claimTokens: jest.fn(),
    };

    mockJwtService = {
      verifyAsync: jest.fn(),
      sign: jest.fn(),
    };

    mockUsersService = {
      findOne: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      remove: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [LaunchpadController],
      providers: [
        {
          provide: LaunchpadService,
          useValue: mockLaunchpadService,
        },
        {
          provide: Reflector,
          useValue: new Reflector(),
        },
        {
          provide: JwtService,
          useValue: mockJwtService,
        },
        {
          provide: UsersService,
          useValue: mockUsersService,
        },
      ],
    }).compile();

    controller = module.get<LaunchpadController>(LaunchpadController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('getAllSales', () => {
    it('should return all sales', async () => {
      const mockSales = [{ id: '1', symbol: 'TEST' } as Sale];
      (mockLaunchpadService.findAllSales as jest.Mock).mockResolvedValue(
        mockSales,
      );

      const result = await controller.getAllSales();
      expect(result).toEqual(mockSales);
      expect(mockLaunchpadService.findAllSales).toHaveBeenCalled();
    });
  });

  describe('getActiveSales', () => {
    it('should return active sales', async () => {
      const mockSales = [{ id: '1', symbol: 'TEST', status: 'active' } as Sale];
      (mockLaunchpadService.findActiveSales as jest.Mock).mockResolvedValue(
        mockSales,
      );

      const result = await controller.getActiveSales();
      expect(result).toEqual(mockSales);
      expect(mockLaunchpadService.findActiveSales).toHaveBeenCalled();
    });
  });

  describe('getSaleById', () => {
    it('should return sale by id', async () => {
      const mockSale = { id: '1', symbol: 'TEST' } as Sale;
      (mockLaunchpadService.findSaleById as jest.Mock).mockResolvedValue(
        mockSale,
      );
      (mockLaunchpadService.findSaleById as jest.Mock).mockResolvedValue(
        mockSale,
      );

      const result = await controller.getSaleById('1');
      expect(result).toEqual(mockSale);
      expect(mockLaunchpadService.findSaleById).toHaveBeenCalledWith('1');
    });
  });

  describe('getUserAllocation', () => {
    it('should return user allocation for a sale', async () => {
      const mockAllocation: UserAllocationDto = {
        saleId: 'sale-uuid',
        userId: 'user-uuid',
        contributionAmount: 1000,
        allocationAmount: 500,
        allocationPercentage: 50.0,
        saleStatus: SaleStatus.ENDED,
        saleEnded: true,
      };

      (mockLaunchpadService.getUserAllocation as jest.Mock).mockResolvedValue(
        mockAllocation,
      );

      const mockRequest = { user: { id: 'user-uuid' } };
      const result = await controller.getUserAllocation(
        mockRequest,
        'sale-uuid',
      );

      expect(result).toEqual(mockAllocation);
      expect(mockLaunchpadService.getUserAllocation).toHaveBeenCalledWith(
        'user-uuid',
        'sale-uuid',
      );
    });
  });

  describe('claimTokens', () => {
    it('should successfully claim tokens', async () => {
      const mockClaimResponse: ClaimResponseDto = {
        success: true,
        message: 'Successfully claimed 500 TEST',
        transactionHash: 'tx-hash-123',
      };

      (mockLaunchpadService.claimTokens as jest.Mock).mockResolvedValue(
        mockClaimResponse,
      );

      const mockRequest = { user: { id: 'user-uuid' } };
      const result = await controller.claimTokens(mockRequest, 'sale-uuid');

      expect(result).toEqual(mockClaimResponse);
      expect(mockLaunchpadService.claimTokens).toHaveBeenCalledWith(
        'user-uuid',
        'sale-uuid',
      );
    });
  });
});
