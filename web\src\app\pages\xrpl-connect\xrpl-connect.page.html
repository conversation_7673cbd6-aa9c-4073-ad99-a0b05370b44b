<ion-header [translucent]="true">
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-menu-button></ion-menu-button>
    </ion-buttons>
    <ion-title>Connect XRPL Account</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true">
  <ion-header collapse="condense">
    <ion-toolbar>
      <ion-title size="large">Connect XRPL Account</ion-title>
    </ion-toolbar>
  </ion-header>

  <div class="container">
    <!-- Already Linked State -->
    <div *ngIf="isAlreadyLinked()" class="already-linked-section">
      <ion-card>
        <ion-card-header>
          <ion-card-title>
            <ion-icon name="checkmark-circle" color="success"></ion-icon>
            XRPL Account Connected
          </ion-card-title>
        </ion-card-header>
        <ion-card-content>
          <p>Your XRPL account is already connected and ready to use.</p>
          <div class="connected-address">
            <strong>Connected Address:</strong>
            <ion-chip color="primary">
              <ion-label>{{ (currentUser$ | async)?.xrplAddress }}</ion-label>
            </ion-chip>
          </div>
          <ion-button
            expand="block"
            fill="solid"
            color="primary"
            (click)="onGoToDashboard()"
            class="ion-margin-top"
          >
            Go to Dashboard
          </ion-button>
        </ion-card-content>
      </ion-card>
    </div>

    <!-- Link Account Form -->
    <div *ngIf="!isAlreadyLinked()" class="link-form-section">
      <ion-card>
        <ion-card-header>
          <ion-card-title>
            <ion-icon name="link" color="primary"></ion-icon>
            Link Your XRPL Account
          </ion-card-title>
          <ion-card-subtitle>
            Connect your XRPL Testnet address to start using the launchpad
          </ion-card-subtitle>
        </ion-card-header>
        <ion-card-content>
          <form [formGroup]="xrplForm" (ngSubmit)="onSubmit()">
            <ion-item>
              <ion-label position="stacked">
                XRPL Testnet Address
                <span class="required">*</span>
              </ion-label>
              <ion-input
                formControlName="xrplAddress"
                type="text"
                placeholder="rPT1Sjq2YGrBMTttX4GZHjKu9yfEVRox89"
                [class.ion-invalid]="xrplAddressControl?.invalid && xrplAddressControl?.touched"
                [class.ion-valid]="xrplAddressControl?.valid && xrplAddressControl?.touched"
              >
              </ion-input>
            </ion-item>

            <!-- Error Message -->
            <div
              *ngIf="xrplAddressControl?.invalid && xrplAddressControl?.touched"
              class="error-message"
            >
              <ion-text color="danger">
                <small>{{ addressErrorMessage }}</small>
              </ion-text>
            </div>

            <!-- Help Text -->
            <div class="help-text">
              <ion-text color="medium">
                <small>
                  <ion-icon name="information-circle-outline"></ion-icon>
                  Enter your XRPL Testnet address (starts with 'r' followed by
                  24-34 characters)
                </small>
              </ion-text>
            </div>

            <!-- Submit Button -->
            <ion-button
              expand="block"
              fill="solid"
              color="primary"
              type="submit"
              [disabled]="!isFormValid"
              [class.ion-margin-top]="true"
            >
              <ion-spinner *ngIf="isSubmitting()" name="crescent"></ion-spinner>
              <ion-icon
                *ngIf="!isSubmitting()"
                name="link"
                slot="start"
              ></ion-icon>
              {{ isSubmitting() ? 'Linking Account...' : 'Link XRPL Account' }}
            </ion-button>
          </form>
        </ion-card-content>
      </ion-card>

      <!-- Information Card -->
      <ion-card class="info-card">
        <ion-card-header>
          <ion-card-title>
            <ion-icon name="information-circle" color="medium"></ion-icon>
            How to Get an XRPL Testnet Address
          </ion-card-title>
        </ion-card-header>
        <ion-card-content>
          <ion-list>
            <ion-item>
              <ion-icon
                name="wallet-outline"
                slot="start"
                color="primary"
              ></ion-icon>
              <ion-label>
                <h3>Use XUMM Wallet</h3>
                <p>Download XUMM and create a testnet account</p>
              </ion-label>
            </ion-item>
            <ion-item>
              <ion-icon
                name="globe-outline"
                slot="start"
                color="primary"
              ></ion-icon>
              <ion-label>
                <h3>XRPL Testnet Faucet</h3>
                <p>Generate a testnet address at xrpl.org</p>
              </ion-label>
            </ion-item>
            <ion-item>
              <ion-icon
                name="code-outline"
                slot="start"
                color="primary"
              ></ion-icon>
              <ion-label>
                <h3>Developer Tools</h3>
                <p>Use XRPL libraries to generate testnet addresses</p>
              </ion-label>
            </ion-item>
          </ion-list>
        </ion-card-content>
      </ion-card>
    </div>
  </div>
</ion-content>
