// Shared styles for launchpad detail page
// Common components, cards, buttons, and form elements

ion-card {
  margin-bottom: 16px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  ion-card-header {
    padding: 16px 16px 8px 16px;

    ion-card-title {
      font-size: 18px;
      font-weight: 600;
      color: var(--ion-color-dark);
      display: flex;
      align-items: center;

      ion-icon {
        margin-right: 8px;
      }
    }
  }

  ion-card-content {
    padding: 8px 16px 16px 16px;
  }
}

// Common button styles
.contribute-button,
.claim-button,
.login-button {
  --border-radius: 8px;
  --padding-top: 16px;
  --padding-bottom: 16px;
  font-weight: 600;
  font-size: 16px;
}

.copy-button {
  --color: var(--ion-color-primary);
}

.close-button {
  --color: var(--ion-color-medium);
}

// Common form styles
ion-item {
  --border-radius: 8px;
  --background: var(--ion-color-light-shade);
  --border-color: var(--ion-color-medium);
  --border-width: 1px;
  --border-style: solid;
  margin-bottom: 16px;
  --padding-start: 16px;
  --padding-end: 16px;
  --padding-top: 8px;
  --padding-bottom: 8px;

  &:focus-within {
    --border-color: var(--ion-color-primary);
    --background: var(--ion-color-light);
  }
}

.error-message {
  color: var(--ion-color-danger);
  font-size: 14px;
  margin: -8px 0 16px 16px;
  min-height: 20px;
}

// Common section headers
h4 {
  font-size: 16px;
  font-weight: 600;
  color: var(--ion-color-dark);
  margin: 0 0 12px 0;
}

// Common info containers
.info-container {
  padding: 12px;
  background: var(--ion-color-light-shade);
  border-radius: 8px;
}

// Common icon styles
ion-icon {
  &.primary {
    color: var(--ion-color-primary);
  }

  &.success {
    color: var(--ion-color-success);
  }

  &.medium {
    color: var(--ion-color-medium);
  }
}
