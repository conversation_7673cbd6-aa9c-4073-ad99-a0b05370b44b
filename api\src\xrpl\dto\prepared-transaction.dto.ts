import { IsString, <PERSON>N<PERSON><PERSON>, IsOptional } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class PreparedTransactionDto {
  @ApiProperty({
    description: 'Unsigned transaction blob for client-side signing',
    example:
      '12000022800000002400000001201B0000000068400000000000000A732102A8A44DB3D4C73FEEA4519A6BFB4E3B8A9353AA9E3D68EF8D783C531DDFB8EC5A74473045022100F9A63F6C3F094E2BB1BABE4F5C8E805CCE331F1EBE9866F58F506079F86E63E5802206026D4E4E6A4C9C2B12CDFBFC15DD44BAE4F5217115652C49F310573A05BF5F6F8114B5F762798A53D543A014CAF8B297CFF8F2F937E88314D31252CF9024588E9B10B4DBC4C63D6D92CF798287',
  })
  @IsString()
  transactionBlob!: string;

  @ApiProperty({
    description: 'Source account address',
    example: 'rPT1Sjq2YGrBMTttX4GZHjKu9yfEVRox89',
  })
  @IsString()
  account!: string;

  @ApiProperty({
    description: 'Destination account address',
    example: 'rPT1Sjq2YGrBMTttX4GZHjKu9yfEVRox89',
  })
  @IsString()
  destination!: string;

  @ApiProperty({
    description: 'Amount to send in XRP',
    example: 10.5,
  })
  @IsNumber()
  amount!: number;

  @ApiProperty({
    description: 'Transaction fee in drops',
    example: 10,
  })
  @IsNumber()
  fee!: number;

  @ApiProperty({
    description: 'Account sequence number',
    example: 123,
  })
  @IsNumber()
  sequence!: number;

  @ApiProperty({
    description: 'Last ledger sequence for transaction validity',
    example: ********,
  })
  @IsNumber()
  lastLedgerSequence!: number;

  @ApiProperty({
    description: 'Optional memo for the transaction',
    example: 'Payment for services',
    required: false,
  })
  @IsOptional()
  @IsString()
  memo?: string | undefined;

  @ApiProperty({
    description: 'Human-readable message about the transaction',
    example:
      'Transaction prepared successfully. Sign and submit on the client side.',
  })
  @IsString()
  message!: string;
}
