import {
  Injectable,
  Logger,
  OnM<PERSON>ule<PERSON><PERSON>roy,
  OnModuleInit,
  HttpStatus,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Client, Payment, Wallet, encode } from 'xrpl';
import { Subject } from 'rxjs';
import { BalanceDto } from './dto/balance.dto';
import { TransactionDto } from './dto/transaction.dto';
import { PreparedTransactionDto } from './dto/prepared-transaction.dto';
import { createXrplException } from './filters/xrpl-exception.filter';

export interface XrplConnectionStatus {
  isConnected: boolean;
  lastConnected?: Date;
  lastError?: string;
  connectionAttempts: number;
  reconnectDelay: number;
}

export interface SaleContributionEvent {
  saleId: string;
  collectionAddress: string;
  destinationTag: number;
  transactionHash: string;
  sender: string;
  amount: number;
  timestamp: Date;
}

export interface SaleProgressUpdate {
  saleId: string;
  totalRaised: number;
  progressPercent: number;
  totalContributors: number;
  lastContribution?:
    | {
        amount: number;
        sender: string;
        timestamp: Date;
      }
    | undefined;
}

@Injectable()
export class XrplService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(XrplService.name);
  private client: Client | null = null;
  private connectionStatus: XrplConnectionStatus = {
    isConnected: false,
    connectionAttempts: 0,
    reconnectDelay: 1000,
  };
  private readonly destroy$ = new Subject<void>();
  private reconnectTimer: any;
  private healthCheckInterval: any;
  private accountSubscriptions = new Map<string, Subject<any>>();

  // Sale monitoring
  private saleSubscriptions = new Map<string, Subject<SaleContributionEvent>>();
  private ledgerSubscription: Subject<any> | null = null;
  private activeSales = new Map<
    string,
    { collectionAddress: string; destinationTag: number }
  >();

  constructor(private readonly configService: ConfigService) {}

  async onModuleInit() {
    await this.connect();
    this.startHealthCheck();
  }

  async onModuleDestroy() {
    this.logger.log('Shutting down XRPL service...');
    this.destroy$.next();
    this.destroy$.complete();

    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
    }

    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
    }

    await this.disconnect();
  }

  private async connect(): Promise<void> {
    try {
      const wsUrl = this.configService.get<string>('xrpl.testnetUrl');
      if (!wsUrl) {
        throw new Error('XRPL WebSocket URL not configured');
      }

      this.logger.log(`Connecting to XRPL Testnet: ${wsUrl}`);

      this.client = new Client(wsUrl);

      // Set up event listeners
      this.client.on('connected', () => {
        this.logger.log('Connected to XRPL Testnet');
        this.connectionStatus.isConnected = true;
        this.connectionStatus.lastConnected = new Date();
        this.connectionStatus.connectionAttempts = 0;
        this.connectionStatus.reconnectDelay = 1000;
        this.connectionStatus.lastError = '';
      });

      this.client.on('disconnected', (code: number) => {
        this.logger.warn(`Disconnected from XRPL Testnet with code: ${code}`);
        this.connectionStatus.isConnected = false;
        this.handleDisconnection();
      });

      this.client.on('error', (error: Error) => {
        this.logger.error(`XRPL connection error: ${error.message}`);
        this.connectionStatus.lastError = error.message;
        this.connectionStatus.isConnected = false;
        this.handleDisconnection();
      });

      await this.client.connect();
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      this.logger.error(`Failed to connect to XRPL: ${errorMessage}`);
      this.connectionStatus.lastError = errorMessage;
      this.handleDisconnection();
    }
  }

  private handleDisconnection(): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
    }

    this.connectionStatus.connectionAttempts++;

    // Exponential backoff with max delay of 30 seconds
    this.connectionStatus.reconnectDelay = Math.min(
      this.connectionStatus.reconnectDelay * 2,
      30000,
    );

    this.logger.log(
      `Scheduling reconnection attempt ${this.connectionStatus.connectionAttempts} in ${this.connectionStatus.reconnectDelay}ms`,
    );

    this.reconnectTimer = setTimeout(() => {
      void this.connect();
    }, this.connectionStatus.reconnectDelay);
  }

  private async disconnect(): Promise<void> {
    if (this.client && this.connectionStatus.isConnected) {
      try {
        await this.client.disconnect();
        this.logger.log('Disconnected from XRPL Testnet');
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : 'Unknown error';
        this.logger.error(`Error during disconnect: ${errorMessage}`);
      }
    }
    this.connectionStatus.isConnected = false;
  }

  private startHealthCheck(): void {
    this.healthCheckInterval = setInterval(() => {
      if (this.client && this.connectionStatus.isConnected) {
        // Send a simple ping to check connection health
        this.client.request({ command: 'ping' }).catch((error) => {
          const errorMessage =
            error instanceof Error ? error.message : 'Unknown error';
          this.logger.warn(`Health check failed: ${errorMessage}`);
          this.connectionStatus.isConnected = false;
          this.handleDisconnection();
        });
      }
    }, 30000); // Check every 30 seconds
  }

  public getConnectionStatus(): XrplConnectionStatus {
    return { ...this.connectionStatus };
  }

  public isConnected(): boolean {
    return this.connectionStatus.isConnected;
  }

  public getClient(): Client | null {
    return this.client;
  }

  public async request(command: any): Promise<any> {
    if (!this.client || !this.connectionStatus.isConnected) {
      throw createXrplException(
        'XRPL client not connected',
        'noNetwork',
        HttpStatus.SERVICE_UNAVAILABLE,
      );
    }
    return this.client.request(command);
  }

  public async submit(transaction: any, opts?: any): Promise<any> {
    if (!this.client || !this.connectionStatus.isConnected) {
      throw createXrplException(
        'XRPL client not connected',
        'noNetwork',
        HttpStatus.SERVICE_UNAVAILABLE,
      );
    }
    return this.client.submit(transaction, opts);
  }

  public async submitAndWait(transaction: any, opts?: any): Promise<any> {
    if (!this.client || !this.connectionStatus.isConnected) {
      throw createXrplException(
        'XRPL client not connected',
        'noNetwork',
        HttpStatus.SERVICE_UNAVAILABLE,
      );
    }
    return this.client.submitAndWait(transaction, opts);
  }

  public async getBalance(address: string): Promise<BalanceDto> {
    if (!this.client || !this.connectionStatus.isConnected) {
      throw createXrplException(
        'XRPL client not connected',
        'noNetwork',
        HttpStatus.SERVICE_UNAVAILABLE,
      );
    }

    try {
      const accountInfo = await this.client.request({
        command: 'account_info',
        account: address,
        ledger_index: 'validated',
      });

      if (!accountInfo.result.account_data) {
        throw createXrplException(
          'Account not found',
          'actNotFound',
          HttpStatus.BAD_REQUEST,
        );
      }

      const balance =
        parseFloat(accountInfo.result.account_data.Balance) / 1000000; // Convert drops to XRP

      return {
        address,
        xrp: balance,
        currency: 'XRP',
        ledgerIndex: accountInfo.result.ledger_index || 0,
        validated: true,
      };
    } catch (error) {
      this.logger.error(
        `Failed to get balance for ${address}: ${error as string}`,
      );

      // If it's already an XrplException, re-throw it
      if (error instanceof Error && 'code' in error) {
        throw error;
      }

      // Otherwise, create a generic XRPL error
      throw createXrplException(
        'Failed to fetch account balance',
        'xrplOperationFailed',
        HttpStatus.BAD_REQUEST,
        error,
      );
    }
  }

  public async getTransactions(
    address: string,
    limit: number = 10,
  ): Promise<TransactionDto[]> {
    if (!this.client || !this.connectionStatus.isConnected) {
      throw new Error('XRPL client not connected');
    }

    try {
      const response = await this.client.request({
        command: 'account_tx',
        account: address,
        limit,
        ledger_index_min: -1,
        ledger_index_max: -1,
      });
      this.logger.log(response.result.transactions);

      const transactions: TransactionDto[] = response.result.transactions
        .sort((a: any, b: any) => b.ledger_index - a.ledger_index)
        .slice(0, limit)
        .map((tx: any) => {
          const amount =
            tx.tx_json.TransactionType === 'Payment'
              ? parseFloat(tx.meta.delivered_amount) / 1000000
              : 0;

          return {
            hash: tx.hash,
            account: tx.tx_json.Account,
            destination: tx.tx_json.Destination || tx.tx_json.Account,
            amount,
            currency: 'XRP',
            fee: parseFloat(tx.tx_json.Fee) / 1000000,
            ledgerIndex: tx.ledger_index,
            date: new Date((tx.tx_json.date + *********) * 1000), // Convert Ripple epoch to JS Date
            type: tx.tx_json.TransactionType,
            validated: true,
          };
        });

      return transactions;
    } catch (error) {
      this.logger.error(
        `Failed to get transactions for ${address}: ${error as string}`,
      );
      throw new Error(
        `Failed to fetch transactions: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
    }
  }

  public subscribeToAccount(address: string): Subject<any> {
    if (!this.client || !this.connectionStatus.isConnected) {
      throw new Error('XRPL client not connected');
    }

    // Check if already subscribed
    if (this.accountSubscriptions.has(address)) {
      return this.accountSubscriptions.get(address)!;
    }

    const subject = new Subject<any>();
    this.accountSubscriptions.set(address, subject);

    try {
      // Subscribe to account transactions
      this.client
        .request({
          command: 'subscribe',
          accounts: [address],
        })
        .then(() => {
          this.logger.log(`Subscribed to account: ${address}`);
        })
        .catch((error) => {
          this.logger.error(
            `Failed to subscribe to account ${address}: ${error}`,
          );
          subject.error(error);
          this.accountSubscriptions.delete(address);
        });

      // Listen for transaction notifications
      this.client.on('transaction', (tx: any) => {
        if (
          tx.transaction.Account === address ||
          tx.transaction.Destination === address
        ) {
          subject.next(tx);
        }
      });
    } catch (error) {
      this.logger.error(
        `Failed to subscribe to account ${address}: ${error as string}`,
      );
      subject.error(error);
      this.accountSubscriptions.delete(address);
    }

    return subject;
  }

  public unsubscribeFromAccount(address: string): void {
    if (this.accountSubscriptions.has(address)) {
      const subject = this.accountSubscriptions.get(address)!;
      subject.complete();
      this.accountSubscriptions.delete(address);

      if (this.client && this.connectionStatus.isConnected) {
        this.client
          .request({
            command: 'unsubscribe',
            accounts: [address],
          })
          .catch((error) => {
            this.logger.error(
              `Failed to unsubscribe from account ${address}: ${error}`,
            );
          });
      }
    }
  }

  public async prepareSendTransaction(
    sourceAddress: string,
    destinationAddress: string,
    amount: number,
    memo?: string,
  ): Promise<PreparedTransactionDto> {
    if (!this.client || !this.connectionStatus.isConnected) {
      throw createXrplException(
        'XRPL client not connected',
        'noNetwork',
        HttpStatus.SERVICE_UNAVAILABLE,
      );
    }

    try {
      // Get account info to get the sequence number
      const accountInfo = await this.client.request({
        command: 'account_info',
        account: sourceAddress,
        ledger_index: 'validated',
      });

      if (!accountInfo.result.account_data) {
        throw createXrplException(
          'Source account not found',
          'actNotFound',
          HttpStatus.BAD_REQUEST,
        );
      }

      const sequence = accountInfo.result.account_data.Sequence;
      const balance =
        parseFloat(accountInfo.result.account_data.Balance) / 1000000; // Convert drops to XRP

      // Check if user has sufficient balance (including fee)
      const estimatedFee = 0.00001; // Standard XRP fee in XRP
      if (balance < amount + estimatedFee) {
        throw createXrplException(
          'Insufficient balance to complete the transaction',
          'tecUNFUNDED_PAYMENT',
          HttpStatus.BAD_REQUEST,
        );
      }

      // Get the latest ledger index for LastLedgerSequence
      const serverInfo = await this.client.request({
        command: 'server_info',
      });

      const lastLedgerSequence =
        serverInfo.result.info.complete_ledgers.split('-')[1];

      // Prepare the transaction
      const transaction: Payment = {
        TransactionType: 'Payment',
        Account: sourceAddress,
        Destination: destinationAddress,
        Amount: Math.floor(amount * 1000000).toString(), // Convert XRP to drops
        Fee: Math.floor(estimatedFee * 1000000).toString(), // Convert XRP to drops
        Sequence: sequence,
        LastLedgerSequence: parseInt(lastLedgerSequence) + 4, // Add buffer for confirmation
        ...(memo && {
          Memos: [
            {
              Memo: {
                MemoData: Buffer.from(memo, 'utf8').toString('hex'),
                MemoType: Buffer.from('text/plain', 'utf8').toString('hex'),
              },
            },
          ],
        }),
      };

      // Get the transaction blob (unsigned)
      const prepared = await this.client.autofill(transaction);
      const txBlob = encode(prepared);

      return {
        transactionBlob: txBlob,
        account: sourceAddress,
        destination: destinationAddress,
        amount,
        fee: estimatedFee,
        sequence,
        lastLedgerSequence: parseInt(lastLedgerSequence) + 4,
        memo: memo || undefined,
        message:
          'Transaction prepared successfully. Sign and submit using your XRPL wallet (e.g., Xaman).',
      };
    } catch (error) {
      this.logger.error(
        `Failed to prepare send transaction: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );

      // Check if it's already an XrplException
      if (error instanceof Error && 'code' in error) {
        throw error;
      }

      // Map common XRPL errors to appropriate exceptions
      if (error instanceof Error) {
        if (
          error.message.includes('insufficient balance') ||
          error.message.includes('tecUNFUNDED')
        ) {
          throw createXrplException(
            'Insufficient balance to complete the transaction',
            'tecUNFUNDED_PAYMENT',
            HttpStatus.BAD_REQUEST,
          );
        }
        if (
          error.message.includes('account not found') ||
          error.message.includes('actNotFound')
        ) {
          throw createXrplException(
            'Destination account not found',
            'actNotFound',
            HttpStatus.BAD_REQUEST,
          );
        }
        if (
          error.message.includes('timeout') ||
          error.message.includes('network')
        ) {
          throw createXrplException(
            'Network connection issue',
            'noNetwork',
            HttpStatus.SERVICE_UNAVAILABLE,
          );
        }
      }

      // Generic error
      throw createXrplException(
        `Failed to prepare transaction: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'invalidRequest',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  // Sale monitoring methods
  public subscribeToSale(
    saleId: string,
    collectionAddress: string,
    destinationTag: number,
  ): Subject<SaleContributionEvent> {
    if (!this.client || !this.connectionStatus.isConnected) {
      throw new Error('XRPL client not connected');
    }

    // Check if already subscribed to this sale
    if (this.saleSubscriptions.has(saleId)) {
      return this.saleSubscriptions.get(saleId)!;
    }

    // Store sale info
    this.activeSales.set(saleId, { collectionAddress, destinationTag });

    // Create subject for this sale
    const subject = new Subject<SaleContributionEvent>();
    this.saleSubscriptions.set(saleId, subject);

    // Start ledger subscription if not already active
    this.startLedgerSubscription();

    this.logger.log(
      `Subscribed to sale ${saleId} with collection address ${collectionAddress} and destination tag ${destinationTag}`,
    );

    return subject;
  }

  public unsubscribeFromSale(saleId: string): void {
    if (this.saleSubscriptions.has(saleId)) {
      const subject = this.saleSubscriptions.get(saleId)!;
      subject.complete();
      this.saleSubscriptions.delete(saleId);
      this.activeSales.delete(saleId);

      this.logger.log(`Unsubscribed from sale ${saleId}`);

      // If no more sales, stop ledger subscription
      if (this.saleSubscriptions.size === 0) {
        this.stopLedgerSubscription();
      }
    }
  }

  private startLedgerSubscription(): void {
    if (
      this.ledgerSubscription ||
      !this.client ||
      !this.connectionStatus.isConnected
    ) {
      return;
    }

    try {
      // Subscribe to ledger events
      this.client
        .request({
          command: 'subscribe',
          streams: ['ledger'],
        })
        .then(() => {
          this.logger.log('Subscribed to ledger events');
        })
        .catch((error) => {
          this.logger.error(
            `Failed to subscribe to ledger events: ${error as string}`,
          );
          return;
        });

      // Listen for ledger events
      this.client.on('ledgerClosed', async (ledger: any) => {
        await this.processLedgerTransactions(ledger);
      });

      this.ledgerSubscription = new Subject();
    } catch (error) {
      this.logger.error(
        `Failed to start ledger subscription: ${error as string}`,
      );
    }
  }

  private stopLedgerSubscription(): void {
    if (
      this.ledgerSubscription &&
      this.client &&
      this.connectionStatus.isConnected
    ) {
      this.client
        .request({
          command: 'unsubscribe',
          streams: ['ledger'],
        })
        .catch((error) => {
          this.logger.error(
            `Failed to unsubscribe from ledger events: ${error}`,
          );
        });

      this.ledgerSubscription?.complete();
      this.ledgerSubscription = null;
      this.logger.log('Unsubscribed from ledger events');
    }
  }

  private async processLedgerTransactions(ledger: any): Promise<void> {
    if (this.activeSales.size === 0) {
      return;
    }

    try {
      // Get transactions for this ledger
      const ledgerIndex = ledger.ledger_index;

      for (const [saleId, saleInfo] of this.activeSales) {
        try {
          // Get transactions for the collection address in this ledger
          const response = await this.client!.request({
            command: 'account_tx',
            account: saleInfo.collectionAddress,
            ledger_index_min: ledgerIndex,
            ledger_index_max: ledgerIndex,
          });

          if (response.result.transactions) {
            for (const tx of response.result.transactions) {
              const transaction = tx.tx as any;
              if (
                transaction &&
                transaction.TransactionType === 'Payment' &&
                transaction.Destination === saleInfo.collectionAddress &&
                transaction.DestinationTag === saleInfo.destinationTag
              ) {
                // Process contribution
                const contributionEvent: SaleContributionEvent = {
                  saleId,
                  collectionAddress: saleInfo.collectionAddress,
                  destinationTag: saleInfo.destinationTag,
                  transactionHash: transaction.hash,
                  sender: transaction.Account,
                  amount: parseFloat(transaction.Amount) / 1000000, // Convert drops to XRP
                  timestamp: new Date((transaction.date + *********) * 1000), // Convert Ripple epoch to JS Date
                };

                // Emit to sale subscribers
                const subject = this.saleSubscriptions.get(saleId);
                if (subject) {
                  subject.next(contributionEvent);
                }

                this.logger.log(
                  `Processed contribution for sale ${saleId}: ${contributionEvent.amount} XRP from ${contributionEvent.sender}`,
                );
              }
            }
          }
        } catch (error) {
          this.logger.error(
            `Error processing transactions for sale ${saleId}: ${error as string}`,
          );
        }
      }
    } catch (error) {
      this.logger.error(
        `Error processing ledger transactions: ${error as string}`,
      );
    }
  }

  public getActiveSales(): Map<
    string,
    { collectionAddress: string; destinationTag: number }
  > {
    return new Map(this.activeSales);
  }

  public getSaleSubscriptionCount(): number {
    return this.saleSubscriptions.size;
  }

  /**
   * Check if a user has a trustline to a specific issuer
   */
  async checkTrustline(
    userAddress: string,
    issuerAddress: string,
  ): Promise<boolean> {
    try {
      if (!this.client || !this.client.isConnected()) {
        await this.connect();
      }

      if (!this.client) {
        throw new Error('Failed to connect to XRPL');
      }

      const response = await this.client.request({
        command: 'account_lines',
        account: userAddress,
        ledger_index: 'validated',
      });

      if (response.result.lines) {
        return response.result.lines.some(
          (line: any) => line.account === issuerAddress && line.limit_peer > 0,
        );
      }

      return false;
    } catch (error) {
      this.logger.error(
        `Error checking trustline for ${userAddress} to ${issuerAddress}: ${error as string}`,
      );
      throw new Error('Failed to check trustline');
    }
  }

  /**
   * Issue IOU tokens to a user
   */
  async issueTokens(
    userAddress: string,
    issuerAddress: string,
    amount: number,
    currency: string,
  ): Promise<{ success: boolean; transactionHash: string }> {
    try {
      if (!this.client || !this.client.isConnected()) {
        await this.connect();
      }

      const issuerSeed = this.configService.get<string>('xrpl.issuerSeed');
      if (!issuerSeed) {
        throw new Error('Issuer seed not configured');
      }

      // Create wallet from issuer seed
      const issuerWallet = Wallet.fromSeed(issuerSeed);

      // Prepare payment transaction
      const paymentTx: Payment = {
        TransactionType: 'Payment',
        Account: issuerWallet.address,
        Destination: userAddress,
        Amount: {
          currency: currency,
          issuer: issuerAddress,
          value: amount.toString(),
        },
      };

      // Submit and sign transaction
      const prepared = await this.client!.autofill(paymentTx);
      const signed = issuerWallet.sign(prepared);
      const result = await this.client!.submitAndWait(signed.tx_blob);

      if (
        result.result.meta &&
        typeof result.result.meta === 'object' &&
        'TransactionResult' in result.result.meta
      ) {
        const transactionResult = (result.result.meta as any).TransactionResult;
        if (transactionResult === 'tesSUCCESS') {
          this.logger.log(
            `Successfully issued ${amount} ${currency} to ${userAddress}`,
          );
          return {
            success: true,
            transactionHash: result.result.hash,
          };
        } else {
          throw new Error(`Transaction failed: ${transactionResult}`);
        }
      } else {
        throw new Error('Transaction result metadata not found');
      }
    } catch (error) {
      this.logger.error(
        `Error issuing tokens to ${userAddress}: ${error as string}`,
      );
      throw new Error('Failed to issue tokens');
    }
  }
}
