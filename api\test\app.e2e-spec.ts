import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import request from 'supertest';
import { AppController } from '../src/app.controller';
import { AppService } from '../src/app.service';
import { MockLoggingInterceptor } from './mock-logging.interceptor';
import { XrplService } from '../src/xrpl/xrpl.service';

// Mock XrplService
const mockXrplService = {
  getConnectionStatus: jest.fn().mockReturnValue({
    connected: true,
    url: 'wss://test.ripple.com',
    lastConnected: new Date().toISOString(),
  }),
};

describe('AppController (e2e)', () => {
  let app: INestApplication;

  beforeEach(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      controllers: [AppController],
      providers: [
        AppService,
        {
          provide: XrplService,
          useValue: mockXrplService,
        },
      ],
    })
      .overrideProvider('APP_INTERCEPTOR')
      .useValue(new MockLoggingInterceptor())
      .compile();

    app = moduleFixture.createNestApplication();
    await app.init();
  });

  afterEach(async () => {
    if (app) {
      await app.close();
    }
  });

  it('/ (GET)', () => {
    return request(app.getHttpServer())
      .get('/')
      .expect(200)
      .expect((res) => {
        const body = JSON.parse(res.text);
        expect(body.success).toBe(true);
        expect(body.data.message).toBe('Hello World!');
        expect(body.data.version).toBe('0.0.1');
      });
  });

  it('/health (GET)', () => {
    return request(app.getHttpServer())
      .get('/health')
      .expect(200)
      .expect((res) => {
        const body = JSON.parse(res.text);
        expect(body.success).toBe(true);
        expect(body.data.status).toBe('ok');
        expect(typeof body.data.timestamp).toBe('string');
        expect(body.data.xrpl.connected).toBe(true);
      });
  });
});
