import { Injectable, signal, computed, effect } from '@angular/core';

export type ThemeMode = 'light' | 'dark' | 'system';

@Injectable({
  providedIn: 'root',
})
export class ThemeService {
  // Signal for current theme mode
  private _themeMode = signal<ThemeMode>('system');

  // Computed signal for actual theme (resolves 'system' to actual theme)
  public currentTheme = computed(() => {
    const mode = this._themeMode();
    if (mode === 'system') {
      return this.getSystemTheme();
    }
    return mode;
  });

  // Public readonly signal for theme mode
  public themeMode = this._themeMode.asReadonly();

  constructor() {
    this.initializeTheme();

    // Effect to apply theme changes
    effect(() => {
      const theme = this.currentTheme();
      this.applyTheme(theme);
    });

    // Listen for system theme changes
    if (window.matchMedia) {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
      mediaQuery.addEventListener('change', () => {
        if (this._themeMode() === 'system') {
          // Trigger effect by updating the signal
          this._themeMode.set('system');
        }
      });
    }
  }

  private initializeTheme() {
    // Load saved theme preference from localStorage
    const savedTheme = localStorage.getItem('theme-mode');
    if (savedTheme && ['light', 'dark', 'system'].includes(savedTheme)) {
      this._themeMode.set(savedTheme as ThemeMode);
    }
  }

  async setThemeMode(mode: ThemeMode): Promise<void> {
    this._themeMode.set(mode);
    localStorage.setItem('theme-mode', mode);
  }

  private getSystemTheme(): 'light' | 'dark' {
    if (
      window.matchMedia &&
      window.matchMedia('(prefers-color-scheme: dark)').matches
    ) {
      return 'dark';
    }
    return 'light';
  }

  private applyTheme(theme: 'light' | 'dark'): void {
    const body = document.body;

    // Apply Ionic's dark mode class
    document.documentElement.classList.toggle(
      'ion-palette-dark',
      theme === 'dark'
    );
  }

  // Helper method to get theme icon
  getThemeIcon(): string {
    const mode = this._themeMode();
    switch (mode) {
      case 'light':
        return 'sunny';
      case 'dark':
        return 'moon';
      case 'system':
        return 'contrast';
      default:
        return 'contrast';
    }
  }

  // Helper method to get next theme mode
  getNextThemeMode(): ThemeMode {
    const current = this._themeMode();
    switch (current) {
      case 'light':
        return 'dark';
      case 'dark':
        return 'system';
      case 'system':
        return 'light';
      default:
        return 'light';
    }
  }
}
