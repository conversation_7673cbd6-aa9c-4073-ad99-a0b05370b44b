<ion-header>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-menu-button aria-label="Open navigation menu"></ion-menu-button>
    </ion-buttons>
    <ion-title>Admin Sales</ion-title>
    <ion-buttons slot="end">
      <ion-button (click)="loadSales()" [disabled]="isLoading()">
        <ion-icon name="refresh" slot="icon-only"></ion-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content class="admin-sales-content">
  <!-- Create Sale Form Component -->
  <app-sale-form
    #formCard
    [isLoading]="isLoading"
    (saleCreated)="onSaleCreated($event)"
  ></app-sale-form>

  <!-- Sales List Component -->
  <app-admin-sales-list
    [sales]="(sales$ | async) || []"
    [isLoading]="isLoading()"
    (editSale)="onEditSale($event)"
    (deleteSale)="onDeleteSale($event)"
    (openLink)="onOpenLink($event)"
    (scrollToForm)="onScrollToForm()"
  ></app-admin-sales-list>
</ion-content>
