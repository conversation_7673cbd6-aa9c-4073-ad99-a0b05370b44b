<ion-header [translucent]="true">
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button defaultHref="/launchpad"></ion-back-button>
    </ion-buttons>
    <ion-title>Sale Details</ion-title>
    <ion-buttons slot="end">
      <ion-button (click)="onRefreshData()" [disabled]="isLoading()">
        <ion-icon name="refresh" [class.spin]="isLoading()"></ion-icon>
      </ion-button>
      <ion-button [color]="isConnected() ? 'success' : 'danger'" fill="clear">
        <ion-icon [name]="isConnected() ? 'wifi' : 'wifi-off'"></ion-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true">
  <ion-refresher slot="fixed" (ionRefresh)="onRefresh($event)">
    <ion-refresher-content></ion-refresher-content>
  </ion-refresher>

  @if (sale$ | async; as sale) {
  <div class="detail-container">
    <!-- Sale Header -->
    <div class="sale-header">
      <div class="sale-basic-info">
        <h1 class="sale-symbol">{{ sale.symbol }}</h1>
        <p class="sale-description">{{ sale.description }}</p>
      </div>
      <ion-badge [color]="getStatusColor(sale.status)" class="status-badge">
        <ion-icon [name]="getStatusIcon(sale.status)" slot="start"></ion-icon>
        {{ sale.status | titlecase }}
      </ion-badge>
    </div>

    <!-- Progress Section -->
    <ion-card class="progress-card">
      <ion-card-content>
        <div class="progress-header">
          <h3>Funding Progress</h3>
          <span class="time-remaining" *ngIf="sale.status === 'active'">
            {{ getTimeRemaining(sale) }}
          </span>
        </div>

        <div class="progress-bar-container">
          <!-- Real-time progress bar using WebSocket data -->
          <ion-progress-bar
            [value]="progressPercentage() / 100"
            [color]="getStatusColor(sale.status)"
            class="main-progress-bar"
          ></ion-progress-bar>
          <div class="progress-stats">
            <span class="raised"
              >{{ formatCurrency(totalRaised() || getRaisedAmount(sale)) }} {{
              getCurrency() }}</span
            >
            <span class="percentage"
              >{{ (progressPercentage() ||
              getProgressPercentage(getRaisedAmount(sale),
              getTargetAmount(sale))) | number:'1.0-1' }}%</span
            >
            <span class="target"
              >of {{ formatCurrency(getTargetAmount(sale)) }} {{ getCurrency()
              }}</span
            >
          </div>
        </div>

        <!-- Real-time user allocation display -->
        @if (userAllocationAmount() > 0) {
        <div class="user-allocation">
          <ion-icon name="person-outline"></ion-icon>
          <span
            >Your Allocation: {{ formatCurrency(userAllocationAmount()) }} {{
            getCurrency() }}</span
          >
        </div>
        }

        <div class="contribution-limits">
          <div class="limit-item">
            <ion-icon name="trending-down-outline"></ion-icon>
            <span
              >Min: {{ formatCurrency(getMinContribution()) }} {{ getCurrency()
              }}</span
            >
          </div>
          <div class="limit-item">
            <ion-icon name="trending-up-outline"></ion-icon>
            <span
              >Max: {{ formatCurrency(getMaxContribution(sale)) }} {{
              getCurrency() }}</span
            >
          </div>
        </div>
      </ion-card-content>
    </ion-card>

    <!-- Sale Details -->
    <ion-card class="details-card">
      <ion-card-header>
        <ion-card-title>Sale Details</ion-card-title>
      </ion-card-header>
      <ion-card-content>
        <div class="detail-grid">
          <div class="detail-item">
            <ion-icon name="calendar-outline"></ion-icon>
            <div class="detail-content">
              <span class="label">Start Date</span>
              <span class="value">{{ formatDate(sale.start) }}</span>
            </div>
          </div>
          <div class="detail-item">
            <ion-icon name="calendar-outline"></ion-icon>
            <div class="detail-content">
              <span class="label">End Date</span>
              <span class="value">{{ formatDate(sale.end) }}</span>
            </div>
          </div>
          <div class="detail-item">
            <ion-icon name="wallet-outline"></ion-icon>
            <div class="detail-content">
              <span class="label">Collection Address</span>
              <span class="value">{{ sale.collectionAddress }}</span>
            </div>
          </div>
          <div class="detail-item">
            <ion-icon name="pricetag-outline"></ion-icon>
            <div class="detail-content">
              <span class="label">Price per Token</span>
              <span class="value"
                >{{ formatCurrency(sale.price || 0) }} {{ getCurrency() }}</span
              >
            </div>
          </div>
          <div class="detail-item">
            <ion-icon name="trending-down-outline"></ion-icon>
            <div class="detail-content">
              <span class="label">Soft Cap</span>
              <span class="value"
                >{{ formatCurrency(sale.softCap || 0) }} {{ getCurrency()
                }}</span
              >
            </div>
          </div>
          <div class="detail-item">
            <ion-icon name="trending-up-outline"></ion-icon>
            <div class="detail-content">
              <span class="label">Hard Cap</span>
              <span class="value"
                >{{ formatCurrency(sale.hardCap || 0) }} {{ getCurrency()
                }}</span
              >
            </div>
          </div>
        </div>
      </ion-card-content>
    </ion-card>

    <!-- Real-time Balance and Transactions -->
    <ion-card class="realtime-card" *ngIf="isConnected()">
      <ion-card-header>
        <ion-card-title>
          <ion-icon name="pulse-outline"></ion-icon>
          Real-time Updates
        </ion-card-title>
      </ion-card-header>
      <ion-card-content>
        <!-- Balance Section -->
        @if (balance(); as balanceData) {
        <div class="balance-section">
          <h4>Your XRPL Balance</h4>
          <div class="balance-info">
            <span class="balance-amount"
              >{{ formatCurrency(balanceData.xrp) }} {{ balanceData.currency
              }}</span
            >
            <span class="balance-address">{{ balanceData.address }}</span>
          </div>
        </div>
        }

        <!-- Recent Transactions Section -->
        @if (transactions().length > 0) {
        <div class="transactions-section">
          <h4>Recent Transactions</h4>
          <div class="transactions-list">
            @for (tx of transactions().slice(0, 3); track tx.hash) {
            <div class="transaction-item">
              <div class="transaction-info">
                <span class="transaction-amount"
                  >{{ formatCurrency(tx.amount) }} {{ tx.currency }}</span
                >
                <span class="transaction-date">{{ formatDate(tx.date) }}</span>
              </div>
              <div class="transaction-hash">
                {{ tx.hash.substring(0, 8) }}...
              </div>
            </div>
            }
          </div>
        </div>
        }
      </ion-card-content>
    </ion-card>

    <!-- Trustline Instructions -->
    <ion-card class="trustline-card" *ngIf="sale.status === 'active'">
      <ion-card-header>
        <ion-card-title>
          <ion-icon name="link-outline"></ion-icon>
          Trustline Setup Required
        </ion-card-title>
      </ion-card-header>
      <ion-card-content>
        <div class="trustline-instructions">
          <p class="instruction-text">
            Before contributing, you need to set up a trustline for this token
            in your XRPL wallet.
          </p>

          <div class="issuer-info">
            <h4>Issuer Address:</h4>
            <div class="address-container">
              <ion-text class="issuer-address"
                >{{ sale.collectionAddress }}</ion-text
              >
              <ion-button
                fill="clear"
                size="small"
                (click)="copyToClipboard(sale.collectionAddress)"
                class="copy-button"
              >
                <ion-icon name="copy-outline"></ion-icon>
              </ion-button>
            </div>
          </div>

          <div class="qr-section">
            <h4>QR Code for Xaman Wallet:</h4>
            <div class="qr-container">
              @if (qrCodeDataUrl()) {
              <div class="qr-code-display">
                <img
                  [src]="qrCodeDataUrl()"
                  alt="QR Code for {{ sale.symbol }} trustline setup"
                  class="qr-image"
                />
                <p class="qr-description">
                  Scan with Xaman to set up trustline
                </p>
              </div>
              } @else {
              <div class="qr-placeholder">
                <ion-spinner name="crescent" size="small"></ion-spinner>
                <p>Generating QR Code...</p>
              </div>
              }
            </div>
          </div>

          <div class="wallet-instructions">
            <h4>How to set up trustline:</h4>
            <ol>
              <li>Open your XRPL wallet (Xaman, XUMM, etc.)</li>
              <li>Go to "Add Asset" or "Trustline" section</li>
              <li>Enter the issuer address above</li>
              <li>
                Set the currency code to: <strong>{{ sale.symbol }}</strong>
              </li>
              <li>Set the limit to your desired amount</li>
              <li>Confirm the trustline creation</li>
            </ol>
          </div>
        </div>
      </ion-card-content>
    </ion-card>

    <!-- Login Prompt for Non-Authenticated Users -->
    <ion-card
      class="login-prompt-card"
      *ngIf="sale.status === 'active' && !isAuthenticated()"
    >
      <ion-card-header>
        <ion-card-title>
          <ion-icon name="lock-closed-outline" slot="start"></ion-icon>
          Login Required to Contribute
        </ion-card-title>
      </ion-card-header>
      <ion-card-content>
        <div class="login-prompt-content">
          <ion-icon name="person-circle-outline" class="login-icon"></ion-icon>
          <p class="login-message">
            You need to be logged in to contribute to this sale. Please log in
            to your account to participate in the token sale.
          </p>
          <ion-button
            expand="block"
            fill="solid"
            color="primary"
            (click)="navigateToLogin()"
            class="login-button"
          >
            <ion-icon name="log-in-outline" slot="start"></ion-icon>
            Log In to Contribute
          </ion-button>
        </div>
      </ion-card-content>
    </ion-card>

    <!-- Contribution Form for Authenticated Users -->
    <ion-card
      class="contribution-card"
      *ngIf="sale.status === 'active' && isAuthenticated()"
    >
      <ion-card-header>
        <ion-card-title>Contribute to Sale</ion-card-title>
      </ion-card-header>
      <ion-card-content>
        <form [formGroup]="contributionForm" (ngSubmit)="onContribute()">
          <ion-item>
            <ion-label position="stacked"
              >Amount ({{ getCurrency() }})</ion-label
            >
            <ion-input
              type="number"
              formControlName="amount"
              placeholder="Enter amount"
              [min]="getMinContribution()"
              [max]="getMaxContribution(sale)"
              [clearInput]="true"
              step="0.000001"
            ></ion-input>
          </ion-item>
          <div
            class="error-message"
            *ngIf="contributionForm.get('amount')?.invalid && contributionForm.get('amount')?.touched"
          >
            <span *ngIf="contributionForm.get('amount')?.errors?.['required']"
              >Amount is required</span
            >
            <span *ngIf="contributionForm.get('amount')?.errors?.['min']"
              >Amount must be at least {{ getMinContribution() }} {{ 'XRP'
              }}</span
            >
          </div>

          <ion-item>
            <ion-label position="stacked">Destination Tag</ion-label>
            <ion-input
              type="text"
              formControlName="destinationTag"
              placeholder="Enter destination tag"
              [clearInput]="true"
            ></ion-input>
          </ion-item>
          <div
            class="error-message"
            *ngIf="contributionForm.get('destinationTag')?.invalid && contributionForm.get('destinationTag')?.touched"
          >
            <span
              *ngIf="contributionForm.get('destinationTag')?.errors?.['required']"
              >Destination tag is required</span
            >
            <span
              *ngIf="contributionForm.get('destinationTag')?.errors?.['pattern']"
              >Destination tag must be numeric</span
            >
          </div>

          <ion-item>
            <ion-checkbox
              formControlName="acceptTerms"
              id="acceptTerms"
              labelPlacement="end"
            >
              I accept the terms and conditions of this sale
            </ion-checkbox>
          </ion-item>

          <ion-button
            expand="block"
            type="submit"
            [disabled]="!isFormValid() || isLoading()"
            class="contribute-button"
          >
            <ion-spinner
              *ngIf="isLoading()"
              name="crescent"
              size="small"
            ></ion-spinner>
            <ion-icon
              *ngIf="!isLoading()"
              name="rocket-outline"
              slot="start"
            ></ion-icon>
            {{ isLoading() ? 'Preparing Transaction...' : 'Contribute Now' }}
          </ion-button>
        </form>
      </ion-card-content>
    </ion-card>

    <!-- Transaction Blob Display -->
    <ion-card
      class="transaction-blob-card"
      *ngIf="isTransactionBlobVisible() && preparedTransaction"
    >
      <ion-card-header>
        <ion-card-title>
          <ion-icon name="document-text-outline"></ion-icon>
          Transaction Ready for Signing
        </ion-card-title>
        <ion-button
          fill="clear"
          size="small"
          (click)="closeTransactionBlob()"
          class="close-button"
        >
          <ion-icon name="close-circle-outline"></ion-icon>
        </ion-button>
      </ion-card-header>
      <ion-card-content>
        <div class="transaction-info">
          <p class="instruction-text">
            Your transaction has been prepared. Sign it with your XRPL wallet to
            complete the contribution.
          </p>

          <div class="transaction-details">
            <div class="detail-row">
              <span class="label">Amount:</span>
              <span class="value"
                >{{ formatCurrency(preparedTransaction.amount) }} XRP</span
              >
            </div>
            <div class="detail-row">
              <span class="label">Fee:</span>
              <span class="value">{{ preparedTransaction.fee }} drops</span>
            </div>
            <div class="detail-row">
              <span class="label">Sequence:</span>
              <span class="value">{{ preparedTransaction.sequence }}</span>
            </div>
          </div>

          <div class="transaction-blob-section">
            <h4>Transaction Blob:</h4>
            <div class="blob-container">
              <ion-text class="transaction-blob"
                >{{ preparedTransaction.transactionBlob }}</ion-text
              >
            </div>
            <div class="blob-actions">
              <ion-button
                fill="outline"
                size="small"
                (click)="copyTransactionBlob()"
                class="copy-blob-button"
              >
                <ion-icon name="copy-outline" slot="start"></ion-icon>
                Copy Blob
              </ion-button>
              <ion-button
                fill="solid"
                size="small"
                (click)="openXamanWallet()"
                class="xaman-button"
              >
                <ion-icon name="phone-portrait-outline" slot="start"></ion-icon>
                Open Xaman
              </ion-button>
            </div>
          </div>
        </div>
      </ion-card-content>
    </ion-card>

    <!-- Claim Button -->
    @if (userAllocation$ | async; as allocation) { @if
    (canUserClaim(allocation)) {
    <ion-card class="claim-card">
      <ion-card-content>
        <ion-button
          expand="block"
          color="success"
          (click)="onClaim()"
          [disabled]="isLoading()"
          class="claim-button"
        >
          <ion-spinner
            *ngIf="isLoading()"
            name="crescent"
            size="small"
          ></ion-spinner>
          <ion-icon
            *ngIf="!isLoading()"
            name="gift-outline"
            slot="start"
          ></ion-icon>
          {{ isLoading() ? 'Claiming...' : 'Claim Tokens' }}
        </ion-button>
        <p class="allocation-info">
          You can claim {{ allocation.allocationAmount }} tokens
        </p>
      </ion-card-content>
    </ion-card>
    } }

    <!-- Links Section -->
    <ion-card class="links-card" *ngIf="sale.website || sale.whitepaper">
      <ion-card-header>
        <ion-card-title>Project Links</ion-card-title>
      </ion-card-header>
      <ion-card-content>
        <div class="links-grid">
          <ion-button
            *ngIf="sale.website"
            fill="outline"
            expand="block"
            (click)="onOpenWebsite(sale.website || '')"
            class="link-button"
          >
            <ion-icon name="globe-outline" slot="start"></ion-icon>
            Website
          </ion-button>

          <ion-button
            *ngIf="sale.whitepaper"
            fill="outline"
            expand="block"
            (click)="onOpenWhitepaper(sale.whitepaper || '')"
            class="link-button"
          >
            <ion-icon name="document-text-outline" slot="start"></ion-icon>
            Whitepaper
          </ion-button>
        </div>
      </ion-card-content>
    </ion-card>
  </div>
  } @else {
  <div class="loading-state">
    <ion-spinner name="crescent"></ion-spinner>
    <p>Loading sale details...</p>
  </div>
  }
</ion-content>
