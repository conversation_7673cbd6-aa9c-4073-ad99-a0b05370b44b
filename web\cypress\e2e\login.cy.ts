/// <reference types="cypress" />

describe('Login Flow', () => {
  beforeEach(() => {
    // Clear localStorage and cookies before each test
    cy.clearLocalStorage();
    cy.clearCookies();
  });

  it('should display login page correctly', () => {
    cy.visit('/login');

    // Check that the login form is visible
    cy.get('ion-content').should('be.visible');
    cy.getIonInput('username').should('be.visible');
    cy.getIonInput('password').should('be.visible');
    cy.getIonButton('submit').should('be.visible');

    // Check that the form has proper labels/placeholders
    cy.getIonInput('username').should('have.attr', 'placeholder');
    cy.getIonInput('password').should('have.attr', 'placeholder');
  });

  it('should show validation errors for empty form', () => {
    cy.visit('/login');

    // Try to submit empty form
    cy.getIonButton('submit').click();

    // Check that form validation prevents submission
    cy.getIonInput('username').should('have.attr', 'aria-invalid', 'true');
    cy.getIonInput('password').should('have.attr', 'aria-invalid', 'true');
  });

  it('should show validation error for short password', () => {
    cy.visit('/login');

    cy.getIonInput('username').type('<EMAIL>');
    cy.getIonInput('password').type('123');
    cy.getIonButton('submit').click();

    // Check that password validation works
    cy.getIonInput('password').should('have.attr', 'aria-invalid', 'true');
  });

  it('should handle successful login', () => {
    // Mock successful login response
    cy.intercept('POST', '**/auth/login', {
      statusCode: 200,
      body: {
        accessToken: 'mock-access-token',
        refreshToken: 'mock-refresh-token',
        user: {
          id: '1',
          username: 'testuser',
          email: '<EMAIL>',
          role: 'user',
          xrplAddress: 'rValidTestnetAddress123456789',
        },
      },
    }).as('loginRequest');

    cy.visit('/login');

    cy.getIonInput('username').type('<EMAIL>');
    cy.getIonInput('password').type('password123');
    cy.getIonButton('submit').click();

    // Wait for login request
    cy.wait('@loginRequest');

    // Check that user is redirected to dashboard
    cy.url().should('include', '/dashboard');
    cy.waitForIonicPage();
  });

  it('should handle login failure with invalid credentials', () => {
    // Mock failed login response
    cy.intercept('POST', '**/auth/login', {
      statusCode: 401,
      body: {
        message: 'Invalid credentials',
      },
    }).as('loginRequest');

    cy.visit('/login');

    cy.getIonInput('username').type('<EMAIL>');
    cy.getIonInput('password').type('wrongpassword');
    cy.getIonButton('submit').click();

    // Wait for login request
    cy.wait('@loginRequest');

    // Check that error toast is shown
    cy.getIonToast().should('be.visible');
    cy.getIonToast().should('contain', 'Invalid credentials');

    // Check that user stays on login page
    cy.url().should('include', '/login');
  });

  it('should handle network error during login', () => {
    // Mock network error
    cy.intercept('POST', '**/auth/login', {
      forceNetworkError: true,
    }).as('loginRequest');

    cy.visit('/login');

    cy.getIonInput('username').type('<EMAIL>');
    cy.getIonInput('password').type('password123');
    cy.getIonButton('submit').click();

    // Wait for login request
    cy.wait('@loginRequest');

    // Check that error toast is shown
    cy.getIonToast().should('be.visible');
    cy.getIonToast().should('contain', 'Server error');

    // Check that user stays on login page
    cy.url().should('include', '/login');
  });

  it('should navigate to register page', () => {
    cy.visit('/login');

    // Click on register link (if it exists)
    cy.get('a').contains('Register').click();

    // Check that user is redirected to register page
    cy.url().should('include', '/register');
  });

  it('should show forgot password option', () => {
    cy.visit('/login');

    // Check that forgot password link exists
    cy.get('a').contains('Forgot Password').should('be.visible');

    // Click on forgot password
    cy.get('a').contains('Forgot Password').click();

    // This would typically show a modal or navigate to forgot password page
    // For now, we'll just verify the link exists and is clickable
  });
});
