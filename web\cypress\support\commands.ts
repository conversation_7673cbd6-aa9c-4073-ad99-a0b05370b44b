/// <reference types="cypress" />

// Custom commands for Ionic components and app-specific functionality

Cypress.Commands.add('login', (email: string, password: string) => {
  cy.visit('/login');
  cy.getIonInput('username').type(email);
  cy.getIonInput('password').type(password);
  cy.getIonButton('submit').click();
  cy.waitForIonicPage();
});

Cypress.Commands.add('logout', () => {
  // Click on menu button
  cy.get('ion-menu-button').click();
  // Click on logout item
  cy.get('ion-item').contains('Logout').click();
  cy.waitForIonicPage();
});

Cypress.Commands.add('waitForIonicPage', () => {
  // Wait for Ionic page to be fully loaded
  cy.get('ion-content').should('be.visible');
  cy.get('ion-content').should('not.have.class', 'loading');
});

Cypress.Commands.add('getIonInput', (formControlName: string) => {
  return cy.get(`ion-input[formControlName="${formControlName}"] input`);
});

Cypress.Commands.add('getIonButton', (type?: string) => {
  if (type) {
    return cy.get(`ion-button[type="${type}"]`) as unknown as Cypress.Chainable<
      JQuery<HTMLElement>
    >;
  }
  return cy.get('ion-button') as unknown as Cypress.Chainable<
    JQuery<HTMLElement>
  >;
});

Cypress.Commands.add('getIonToast', () => {
  return cy.get('ion-toast') as unknown as Cypress.Chainable<
    JQuery<HTMLElement>
  >;
});

Cypress.Commands.add('getIonCard', () => {
  return cy.get('ion-card') as unknown as Cypress.Chainable<
    JQuery<HTMLElement>
  >;
});

Cypress.Commands.add('getIonList', () => {
  return cy.get('ion-list') as unknown as Cypress.Chainable<
    JQuery<HTMLElement>
  >;
});

Cypress.Commands.add('getIonItem', () => {
  return cy.get('ion-item') as unknown as Cypress.Chainable<
    JQuery<HTMLElement>
  >;
});

Cypress.Commands.add('getIonProgressBar', () => {
  return cy.get('ion-progress-bar') as unknown as Cypress.Chainable<
    JQuery<HTMLElement>
  >;
});

Cypress.Commands.add('getIonSpinner', () => {
  return cy.get('ion-spinner') as unknown as Cypress.Chainable<
    JQuery<HTMLElement>
  >;
});

Cypress.Commands.add('getIonRefresher', () => {
  return cy.get('ion-refresher') as unknown as Cypress.Chainable<
    JQuery<HTMLElement>
  >;
});
