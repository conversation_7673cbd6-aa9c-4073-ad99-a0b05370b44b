#!/bin/bash

# Local CI Script for XRPL Launchpad
# This script runs the same checks locally that the CI pipeline runs

set -e  # Exit on any error

echo "🚀 Starting local CI checks..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "package.json" ] || [ ! -f "api/package.json" ] || [ ! -f "web/package.json" ]; then
    print_error "Please run this script from the root of the xrpl-launchpad repository"
    exit 1
fi

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    print_error "Node.js is not installed. Please install Node.js 18+ and try again."
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    print_warning "Node.js version $(node -v) detected. Version 18+ is recommended."
fi

print_status "Node.js version: $(node -v)"
print_status "npm version: $(npm -v)"

# Install dependencies
print_status "Installing root dependencies..."
npm ci

print_status "Installing API dependencies..."
npm run -w api ci

print_status "Installing Web dependencies..."
npm run -w web ci

# Run linting checks
print_status "Running linting checks..."
npm run lint:check

# Check formatting
print_status "Checking code formatting..."
npm run format:check

# Run tests
print_status "Running API tests..."
npm run api:test

print_status "Running Web tests..."
npm run web:test

# Build everything
print_status "Building applications..."
npm run build

print_success "🎉 All local CI checks passed!"
print_status "Your code is ready to commit and push!"

# Optional: Check for outdated dependencies
print_status "Checking for outdated dependencies..."
npm outdated || print_warning "Some dependencies may be outdated. Consider updating them."

print_status "Local CI checks completed successfully!"
