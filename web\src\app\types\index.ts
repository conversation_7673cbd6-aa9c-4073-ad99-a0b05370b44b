// Type definitions based on backend DTOs
export interface LoginRequest {
  usernameOrEmail: string;
  password: string;
}

export interface RegisterRequest {
  username: string;
  email: string;
  password: string;
}

export interface RefreshTokenRequest {
  refreshToken: string;
}

export interface AuthResponse {
  accessToken: string;
  refreshToken: string;
  user: {
    id: string;
    username: string;
    email: string;
    role: string;
  };
}

export interface RefreshResponse {
  accessToken: string;
}

export interface SuccessResponse {
  success: boolean;
  message: string;
}

export interface User {
  id: string;
  username: string;
  email: string;
  role: string;
  xrplAddress?: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface BalanceResponse {
  address: string;
  xrp: number;
  currency: string;
  ledgerIndex: number;
  validated: boolean;
}

export interface Transaction {
  hash: string;
  account: string;
  destination: string;
  amount: number;
  currency: string;
  fee: number;
  ledgerIndex: number;
  date: string;
  type: string;
  memo?: string;
  validated: boolean;
}

export interface SendTransactionRequest {
  destination: string;
  amount: number;
}

export interface PreparedTransaction {
  transactionBlob: string;
  hash: string;
  amount: number;
  fee: number;
  sequence: number;
  lastLedgerSequence: number;
  message: string;
}

export interface Sale {
  id: string;
  symbol: string;
  price: number;
  start: string;
  end: string;
  softCap: number;
  hardCap: number;
  collectionAddress: string;
  status: 'active' | 'ended' | 'canceled';
  description?: string;
  website?: string;
  whitepaper?: string;
  createdAt: string;
  updatedAt: string;
}

export interface CreateSaleRequest {
  symbol: string;
  price: number;
  start: string;
  end: string;
  softCap: number;
  hardCap: number;
  collectionAddress: string;
  description?: string;
  website?: string;
  whitepaper?: string;
}

export interface UpdateSaleRequest {
  symbol?: string;
  price?: number;
  start?: string;
  end?: string;
  softCap?: number;
  hardCap?: number;
  collectionAddress?: string;
  description?: string;
  website?: string;
  whitepaper?: string;
}

export interface CreateContributionRequest {
  saleId: string;
  amount: number;
  txHash: string;
  status?: string;
}

export interface UserAllocation {
  saleId: string;
  userId: string;
  contributionAmount: number;
  allocationAmount: number;
  allocationPercentage: number;
  saleStatus: 'active' | 'ended' | 'canceled';
  saleEnded: boolean;
}

export interface ClaimResponse {
  success: boolean;
  message: string;
  transactionHash?: string;
}

// WebSocket related types
export interface SaleProgress {
  totalRaised: number;
  progressPercent: number;
  userAllocation: number;
  saleId: string;
}

export interface WebSocketMessage {
  type: 'balance' | 'transactions' | 'saleProgress' | 'error' | 'connected';
  data?: any;
  error?: string;
}
