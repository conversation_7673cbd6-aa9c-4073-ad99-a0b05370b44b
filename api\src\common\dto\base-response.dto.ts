import { ApiProperty } from '@nestjs/swagger';

export class BaseResponseDto<T> {
  @ApiProperty({
    description: 'Whether the operation was successful',
    example: true,
  })
  success: boolean;

  @ApiProperty({
    description: 'Human-readable message about the operation result',
    example: 'Operation completed successfully',
  })
  message: string;

  @ApiProperty({
    description: 'Optional data payload',
    required: false,
  })
  data?: T;

  @ApiProperty({
    description: 'Optional error details',
    required: false,
  })
  error?: string;

  constructor(success: boolean, message: string, data?: T, error?: string) {
    this.success = success;
    this.message = message;
    if (data !== undefined) {
      this.data = data;
    }
    if (error !== undefined) {
      this.error = error;
    }
  }

  static success<T>(data: T, message = 'Success'): BaseResponseDto<T> {
    return new BaseResponseDto(true, message, data);
  }

  static error(message: string, error?: string): BaseResponseDto<never> {
    return new BaseResponseDto<never>(false, message, undefined, error);
  }
}
