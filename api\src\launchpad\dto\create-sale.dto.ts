import {
  IsString,
  IsN<PERSON>ber,
  IsDateString,
  IsO<PERSON>al,
  Min,
  <PERSON><PERSON>ength,
  IsUrl,
  Valida<PERSON>,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';
import { IsDateAfter, IsCapsValid, IsFutureDate } from '../validators';

export class CreateSaleDto {
  @ApiProperty({
    description: 'Token symbol (e.g., "TOKEN")',
    example: 'TOKEN',
    maxLength: 100,
  })
  @IsString()
  @MaxLength(100)
  symbol!: string;

  @ApiProperty({
    description: 'Token price in XRP (minimum 0.000001)',
    example: 0.1,
    minimum: 0.000001,
  })
  @IsNumber()
  @Min(0.000001) // Minimum price: 1 drop (0.000001 XRP)
  @Type(() => Number)
  price!: number;

  @ApiProperty({
    description: 'Sale start date (ISO 8601 format)',
    example: '2025-01-15T10:00:00Z',
  })
  @IsDateString()
  @Validate(IsFutureDate)
  start!: string;

  @ApiProperty({
    description: 'Sale end date (must be after start date)',
    example: '2025-02-15T10:00:00Z',
  })
  @IsDateString()
  @Validate(IsDateAfter, ['start'])
  end!: string;

  @ApiProperty({
    description: 'Soft cap in XRP (minimum 0.000001)',
    example: 1000,
    minimum: 0.000001,
  })
  @IsNumber()
  @Min(0.000001) // Minimum cap: 1 drop (0.000001 XRP)
  @Type(() => Number)
  softCap!: number;

  @ApiProperty({
    description: 'Hard cap in XRP (must be greater than soft cap)',
    example: 5000,
    minimum: 0.000001,
  })
  @IsNumber()
  @Min(0.000001) // Minimum cap: 1 drop (0.000001 XRP)
  @Type(() => Number)
  @Validate(IsCapsValid)
  hardCap!: number;

  @ApiProperty({
    description: 'XRPL collection address for the token',
    example: 'rPT1Sjq2YGrBMTttX4GZHjKu9yfEVRox89',
    maxLength: 255,
  })
  @IsString()
  @MaxLength(255)
  collectionAddress!: string;

  @ApiProperty({
    description: 'Optional description of the token sale',
    example: 'A revolutionary DeFi token built on XRPL',
    required: false,
    maxLength: 1000,
  })
  @IsOptional()
  @IsString()
  @MaxLength(1000)
  description?: string;

  @ApiProperty({
    description: 'Optional website URL for the project',
    example: 'https://example.com',
    required: false,
    maxLength: 255,
  })
  @IsOptional()
  @IsUrl()
  @MaxLength(255)
  website?: string;

  @ApiProperty({
    description: 'Optional whitepaper URL',
    example: 'https://example.com/whitepaper.pdf',
    required: false,
    maxLength: 255,
  })
  @IsOptional()
  @IsUrl()
  @MaxLength(255)
  whitepaper?: string;
}
