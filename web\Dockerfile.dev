# ---- Development Dockerfile for Ionic ----
FROM node:20-alpine

WORKDIR /app

# Install OS deps and Ionic CLI
RUN apk add --no-cache bash git
RUN npm install -g @ionic/cli

# Copy package files
COPY web/package*.json ./

# Install dependencies
RUN npm install --legacy-peer-deps

# Copy source code
COPY web/ .

# Expose Ionic development server port
EXPOSE 8100

# Start Ionic development server with live reload
CMD ["ionic", "serve", "--host", "0.0.0.0", "--port", "8100", "--external"]
