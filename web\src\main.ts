import { bootstrapApplication } from '@angular/platform-browser';
import { provideRouter } from '@angular/router';
import { provideIonicAngular } from '@ionic/angular/standalone';
import { provideHttpClient } from '@angular/common/http';
import { addIcons } from 'ionicons';
import {
  home,
  rocket,
  link,
  settings,
  logIn,
  logOut,
  logOutOutline,
  logOutSharp,
  walletOutline,
  globeOutline,
  codeOutline,
  informationCircle,
  informationCircleOutline,
  refresh,
  playCircle,
  checkmarkCircle,
  closeCircle,
  closeCircleOutline,
  helpCircle,
  add,
  trash,
  personOutline,
  trendingDownOutline,
  trendingUpOutline,
  calendarOutline,
  pricetagOutline,
  pulseOutline,
  linkOutline,
  copyOutline,
  qrCodeOutline,
  documentTextOutline,
  closeOutline,
  phonePortraitOutline,
  bugOutline,
  swapHorizontalOutline,
  rocketOutline,
  rocketSharp,
  locationOutline,
  homeSharp,
  linkSharp,
  informationCircleSharp,
  lockClosedOutline,
  personCircleOutline,
  logInOutline,
  wifi,
  cloudOffline,
  settingsSharp,
  close,
  arrowForward,
  arrowForwardOutline,
  addCircle,
  time,
  trendingUp,
  wallet,
  list,
  alertCircle,
  calendar,
  documentText,
  trashOutline,
  arrowBack,
  colorPaletteOutline,
  contrast,
  moon,
  sunny,
} from 'ionicons/icons';
import { AppComponent } from './app/app.component';
import { routes } from './app/app.routes';

// Register all required icons
addIcons({
  home: home,
  'home-sharp': homeSharp,
  rocket: rocket,
  link: link,
  'link-sharp': linkSharp,
  settings: settings,
  'log-in': logIn,
  'log-in-outline': logInOutline,
  'log-out': logOut,
  'log-out-outline': logOutOutline,
  'log-out-sharp': logOutSharp,
  'wallet-outline': walletOutline,
  'globe-outline': globeOutline,
  'code-outline': codeOutline,
  'information-circle': informationCircle,
  'information-circle-outline': informationCircleOutline,
  'information-circle-sharp': informationCircleSharp,
  refresh: refresh,
  'play-circle': playCircle,
  'checkmark-circle': checkmarkCircle,
  'close-circle': closeCircle,
  'help-circle': helpCircle,
  add: add,
  trash: trash,
  'person-outline': personOutline,
  'trending-down-outline': trendingDownOutline,
  'trending-up-outline': trendingUpOutline,
  'calendar-outline': calendarOutline,
  'pricetag-outline': pricetagOutline,
  'pulse-outline': pulseOutline,
  'link-outline': linkOutline,
  'copy-outline': copyOutline,
  'qr-code-outline': qrCodeOutline,
  'document-text-outline': documentTextOutline,
  'close-outline': closeOutline,
  'phone-portrait-outline': phonePortraitOutline,
  'bug-outline': bugOutline,
  'swap-horizontal-outline': swapHorizontalOutline,
  'rocket-outline': rocketOutline,
  'rocket-sharp': rocketSharp,
  'location-outline': locationOutline,
  'lock-closed-outline': lockClosedOutline,
  'person-circle-outline': personCircleOutline,
  wifi: wifi,
  'wifi-off': cloudOffline,
  'settings-sharp': settingsSharp,
  close: close,
  'arrow-forward': arrowForward,
  'arrow-forward-outline': arrowForwardOutline,
  'add-circle': addCircle,
  time: time,
  'trending-up': trendingUp,
  wallet: wallet,
  list: list,
  'alert-circle': alertCircle,
  calendar: calendar,
  'document-text': documentText,
  'trash-outline': trashOutline,
  'close-circle-outline': closeCircleOutline,
  'arrow-back': arrowBack,
  'color-palette-outline': colorPaletteOutline,
  contrast: contrast,
  moon: moon,
  sunny: sunny,
});

bootstrapApplication(AppComponent, {
  providers: [
    provideRouter(routes),
    provideIonicAngular({}),
    provideHttpClient(),
  ],
}).catch(err => console.log(err));
