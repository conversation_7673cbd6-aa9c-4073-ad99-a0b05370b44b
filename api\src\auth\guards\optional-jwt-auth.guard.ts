import { Injectable, ExecutionContext, CanActivate } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { JwtService } from '@nestjs/jwt';
import { UsersService } from '../../users/users.service';

@Injectable()
export class OptionalJwtAuthGuard implements CanActivate {
  constructor(
    private reflector: Reflector,
    private jwtService: JwtService,
    private usersService: UsersService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    // Check if the route is marked as public
    const isPublic = this.reflector.getAllAndOverride<boolean>('isPublic', [
      context.getHandler(),
      context.getClass(),
    ]);

    if (isPublic) {
      // For public routes, try to extract and validate the token if present
      const request = context.switchToHttp().getRequest();
      const token = this.extractTokenFromHeader(request);

      if (token) {
        try {
          const payload = await this.jwtService.verifyAsync(token, {
            secret: process.env.JWT_SECRET || 'fallback-secret',
          });

          const user = await this.usersService.findOne(payload.sub);
          if (user) {
            request.user = {
              id: user.id,
              username: user.username,
              email: user.email,
              role: user.role,
              xrplAddress: user.xrplAddress,
            };
          }
        } catch (error) {
          // Token is invalid, but that's okay for public routes
          // Just don't set the user
          console.log(error);
        }
      }

      return true; // Always allow public routes
    }

    // For protected routes, require valid authentication
    const request = context.switchToHttp().getRequest();
    const token = this.extractTokenFromHeader(request);

    if (!token) {
      return false;
    }

    try {
      const payload = await this.jwtService.verifyAsync(token, {
        secret: process.env.JWT_SECRET || 'fallback-secret',
      });

      const user = await this.usersService.findOne(payload.sub);
      if (!user) {
        return false;
      }

      request.user = {
        id: user.id,
        username: user.username,
        email: user.email,
        role: user.role,
        xrplAddress: user.xrplAddress,
      };

      return true;
    } catch (error) {
      console.log(error);
      return false;
    }
  }

  private extractTokenFromHeader(request: any): string | undefined {
    const [type, token] = request.headers.authorization?.split(' ') ?? [];
    return type === 'Bearer' ? token : undefined;
  }
}
