import {
  Component,
  ChangeDetectionStrategy,
  inject,
  signal,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { Router, RouterModule } from '@angular/router';
import { IonicModule } from '@ionic/angular';
import { finalize } from 'rxjs/operators';
import { ApiService } from '../../services/api/api.service';
import { LoginRequest } from '../../types';

@Component({
  selector: 'app-login',
  templateUrl: './login.page.html',
  styleUrls: ['./login.page.scss'],
  standalone: true,
  imports: [CommonModule, IonicModule, ReactiveFormsModule, RouterModule],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class LoginPage {
  loginForm: FormGroup;
  isLoading = signal<boolean>(false);
  errorMessage = signal<string>('');

  private fb = inject(FormBuilder);
  private apiService = inject(ApiService);
  private router = inject(Router);

  constructor() {
    this.loginForm = this.fb.group({
      username: ['', [Validators.required]],
      password: ['', [Validators.required, Validators.minLength(6)]],
    });
  }

  onSubmit(): void {
    if (this.loginForm.valid && !this.isLoading()) {
      this.isLoading.set(true);
      this.errorMessage.set('');

      const loginData: LoginRequest = {
        usernameOrEmail: this.loginForm.value.username,
        password: this.loginForm.value.password,
      };

      this.apiService
        .login(loginData)
        .pipe(finalize(() => this.isLoading.set(false)))
        .subscribe({
          next: response => {
            // Navigation will be handled by the auth guard
            this.router.navigate(['/dashboard']);
          },
          error: error => {
            // Error handling is done in ApiService with toast notifications
            this.errorMessage.set(
              'Login failed. Please check your credentials.'
            );
          },
        });
    }
  }

  onForgotPassword(): void {
    // TODO: Implement forgot password functionality
    console.log('Forgot password clicked');
  }
}
