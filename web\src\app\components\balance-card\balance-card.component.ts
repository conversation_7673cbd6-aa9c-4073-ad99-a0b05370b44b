import { Component, Input, ChangeDetectionStrategy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';
import { BalanceResponse } from '../../types';

/**
 * Component that displays XRPL balance information in a card format.
 *
 * Features:
 * - Shows XRP balance with proper formatting
 * - Displays wallet address and ledger information
 * - Loading state with skeleton text
 * - Responsive design
 *
 * @example
 * ```html
 * <app-balance-card [balance]="balance$ | async"></app-balance-card>
 * ```
 */
@Component({
  selector: 'app-balance-card',
  templateUrl: './balance-card.component.html',
  styleUrls: ['./balance-card.component.scss'],
  standalone: true,
  imports: [CommonModule, IonicModule],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BalanceCardComponent {
  @Input() balance: BalanceResponse | null = null;
}
