// ***********************************************************
// This example support/e2e.ts is processed and
// loaded automatically before your test files.
//
// This is a great place to put global configuration and
// behavior that modifies Cypress.
//
// You can change the location of this file or turn off
// automatically serving support files with the
// 'supportFile' configuration option.
//
// You can read more here:
// https://on.cypress.io/configuration
// ***********************************************************

// Import commands.js using ES2015 syntax:
import './commands';

// Alternatively you can use CommonJS syntax:
// require('./commands')

// Hide fetch/XHR requests from command log
Cypress.on('window:before:load', win => {
  // Disable service workers
  Object.defineProperty(win.navigator, 'serviceWorker', {
    value: undefined,
    writable: true,
    configurable: true,
  });
});

// Add custom commands for Ionic components
declare global {
  namespace Cypress {
    interface Chainable {
      login(email: string, password: string): Chainable<void>;
      logout(): Chainable<void>;
      waitForIonicPage(): Chainable<void>;
      getIonInput(formControlName: string): Chainable<JQuery<HTMLElement>>;
      getIonButton(type?: string): Chainable<JQuery<HTMLElement>>;
      getIonToast(): Chainable<JQuery<HTMLElement>>;
      getIonCard(): Chainable<JQuery<HTMLElement>>;
      getIonList(): Chainable<JQuery<HTMLElement>>;
      getIonItem(): Chainable<JQuery<HTMLElement>>;
      getIonProgressBar(): Chainable<JQuery<HTMLElement>>;
      getIonSpinner(): Chainable<JQuery<HTMLElement>>;
      getIonRefresher(): Chainable<JQuery<HTMLElement>>;
    }
  }
}
