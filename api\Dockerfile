# ---- Base build image ----
FROM node:20-alpine AS base
WORKDIR /app
ENV CI=true
RUN apk add --no-cache python3 make g++ bash

# ---- Dependencies ----
FROM base AS deps
COPY api/package*.json ./
RUN npm install --legacy-peer-deps

# ---- Build ----
FROM base AS build
COPY --from=deps /app/node_modules ./node_modules
COPY api/ .
# Ensure Nest CLI is available if used by build scripts
RUN npm run build

# ---- Production runtime ----
FROM node:20-alpine AS prod
WORKDIR /app
ENV NODE_ENV=development

# Install health check tools
RUN apk add --no-cache dumb-init wget

# Copy only prod deps
COPY api/package*.json ./
RUN npm install --omit=dev --legacy-peer-deps && npm cache clean --force

# Copy built dist and any runtime assets
COPY --from=build /app/dist ./dist
COPY --from=build /app/scripts ./scripts

EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD wget -qO- http://localhost:3000/api/health || exit 1

CMD ["dumb-init", "node", "dist/src/main.js"]
