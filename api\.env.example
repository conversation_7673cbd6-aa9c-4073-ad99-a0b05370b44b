# Database Configuration
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=xrpl_launchpad
DATABASE_USERNAME=postgres
DATABASE_PASSWORD=your_password_here

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_REFRESH_SECRET=your-super-secret-refresh-key-change-this-in-production
JWT_EXPIRES_IN=15m
JWT_REFRESH_EXPIRES_IN=7d

# XRPL Configuration
XRPL_WS_URL=wss://s.altnet.rippletest.net:51233
XRPL_ISSUER_SEED=your_issuer_seed_here

# App Configuration
PORT=3000
NODE_ENV=development

# Throttle Configuration
THROTTLE_TTL=60
THROTTLE_LIMIT=100
