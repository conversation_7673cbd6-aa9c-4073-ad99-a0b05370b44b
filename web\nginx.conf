server {
  listen 80;
  server_name _;

  # Security headers
  add_header X-Frame-Options "SAMEORIGIN" always;
  add_header X-Content-Type-Options "nosniff" always;
  add_header X-XSS-Protection "1; mode=block" always;
  add_header Referrer-Policy "strict-origin-when-cross-origin" always;

  # Serve Ionic Angular build
  root /usr/share/nginx/html;
  index index.html;

  # Gzip compression for better performance
  gzip on;
  gzip_vary on;
  gzip_min_length 1024;
  gzip_proxied expired no-cache no-store private must-revalidate auth;
  gzip_types text/plain text/css text/xml text/javascript application/x-javascript application/xml+rss application/javascript application/json;

  # Proxy API requests to NestJS
  location /api/ {
    proxy_pass http://api:3000/api/;
    proxy_http_version 1.1;
    proxy_set_header   Host $host;
    proxy_set_header   X-Real-IP $remote_addr;
    proxy_set_header   X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header   X-Forwarded-Proto $scheme;
    proxy_read_timeout 300s;
    proxy_connect_timeout 75s;
  }

  # Health for container checks
  location = /healthz {
    return 200 "ok";
    add_header Content-Type text/plain;
  }

  # SPA fallback
  location / {
    try_files $uri $uri/ /index.html;
  }

  # Error pages
  error_page 404 /index.html;
  error_page 500 502 503 504 /50x.html;
}
