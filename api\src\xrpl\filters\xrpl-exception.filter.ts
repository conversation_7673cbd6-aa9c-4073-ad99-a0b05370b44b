import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  HttpStatus,
  Logger,
  BadRequestException,
  ServiceUnavailableException,
  RequestTimeoutException,
  InternalServerErrorException,
  UnprocessableEntityException,
  UnauthorizedException,
  ForbiddenException,
  ConflictException,
  NotFoundException,
  HttpException,
} from '@nestjs/common';
import { Response } from 'express';

export class XrplException extends Error {
  constructor(
    message: string,
    public readonly code: string,
    public readonly httpStatus: HttpStatus = HttpStatus.BAD_REQUEST,
    public readonly originalError?: any,
  ) {
    super(message);
    this.name = 'XrplException';
  }
}

@Catch()
export class XrplExceptionFilter implements ExceptionFilter {
  private readonly logger = new Logger(XrplExceptionFilter.name);

  catch(exception: any, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest();

    if (request.url === '/favicon.ico') {
      response.status(204).end();
      return;
    }

    // Log the original error for debugging
    this.logger.error(
      `XRPL Exception: ${exception.message || 'Unknown error'} (Code: ${exception.code || 'NO_CODE'})`,
      {
        exception: exception.stack || exception.message,
        url: request.url,
        method: request.method,
        timestamp: new Date().toISOString(),
      },
    );

    // Handle different types of exceptions
    let httpException: HttpException;
    let errorResponse: {
      statusCode: number;
      message: string;
      error: string;
      code: string;
      timestamp: string;
      path: string;
    };

    if (exception instanceof XrplException) {
      // Handle custom XRPL exceptions
      httpException = this.mapXrplExceptionToHttpException(exception);
      errorResponse = this.createErrorResponse(
        httpException,
        exception.code,
        request,
      );
    } else if (this.isXrplSdkError(exception)) {
      // Handle XRPL SDK errors
      const mappedException = this.mapXrplSdkErrorToHttpException(exception);
      httpException = mappedException.exception;
      errorResponse = this.createErrorResponse(
        httpException,
        mappedException.code,
        request,
      );
    } else if (
      exception instanceof BadRequestException ||
      exception instanceof ServiceUnavailableException ||
      exception instanceof RequestTimeoutException ||
      exception instanceof InternalServerErrorException ||
      exception instanceof UnprocessableEntityException ||
      exception instanceof UnauthorizedException ||
      exception instanceof ForbiddenException ||
      exception instanceof ConflictException ||
      exception instanceof NotFoundException
    ) {
      // Handle already mapped NestJS exceptions (including auth exceptions)
      httpException = exception;
      const errorCode = this.getErrorCodeFromHttpException(exception);
      errorResponse = this.createErrorResponse(
        httpException,
        errorCode,
        request,
      );
    } else {
      // Handle unknown errors
      httpException = new InternalServerErrorException(
        'An unexpected error occurred',
      );
      errorResponse = this.createErrorResponse(
        httpException,
        'INTERNAL_ERROR',
        request,
      );
    }

    response.status(httpException.getStatus()).json(errorResponse);
  }

  private isXrplSdkError(error: any): boolean {
    // Check if this is an XRPL SDK error
    return (
      error &&
      (error.name === 'RippledError' ||
        error.name === 'XrplError' ||
        error.type === 'response' ||
        error.error === 'disconnected' ||
        error.error === 'timeout' ||
        error.error === 'network' ||
        error.error === 'rippled' ||
        error.data?.engine_result ||
        error.data?.engine_result_message ||
        error.result?.engine_result ||
        error.result?.engine_result_message ||
        error.message?.includes('XRPL') ||
        error.message?.includes('rippled') ||
        error.message?.includes('disconnected') ||
        error.message?.includes('timeout') ||
        error.message?.includes('network'))
    );
  }

  private mapXrplSdkErrorToHttpException(error: any): {
    exception: any;
    code: string;
  } {
    // Extract XRPL error details
    const engineResult =
      error.data?.engine_result || error.result?.engine_result;

    // Map XRPL engine results to appropriate HTTP exceptions
    if (engineResult && engineResult in XRPL_ERROR_MAPPINGS) {
      const mapping =
        XRPL_ERROR_MAPPINGS[engineResult as keyof typeof XRPL_ERROR_MAPPINGS];
      if (mapping) {
        return {
          exception: new mapping.exception(mapping.message),
          code: engineResult,
        };
      }
    }

    // Map based on error type/name
    if (error.name === 'RippledError' || error.name === 'XrplError') {
      if (
        error.error === 'disconnected' ||
        error.message?.includes('disconnected')
      ) {
        return {
          exception: new ServiceUnavailableException(
            'XRPL network connection lost. Please try again.',
          ),
          code: 'NETWORK_DISCONNECTED',
        };
      }
      if (error.error === 'timeout' || error.message?.includes('timeout')) {
        return {
          exception: new RequestTimeoutException(
            'XRPL request timed out. Please try again.',
          ),
          code: 'REQUEST_TIMEOUT',
        };
      }
      if (error.error === 'network' || error.message?.includes('network')) {
        return {
          exception: new ServiceUnavailableException(
            'XRPL network issue. Please try again later.',
          ),
          code: 'NETWORK_ERROR',
        };
      }
    }

    // Default mapping for unknown XRPL errors
    return {
      exception: new BadRequestException(
        'Invalid XRPL operation. Please check your request and try again.',
      ),
      code: 'XRPL_OPERATION_FAILED',
    };
  }

  private mapXrplExceptionToHttpException(exception: XrplException): any {
    if (exception.code in XRPL_ERROR_MAPPINGS) {
      const mapping =
        XRPL_ERROR_MAPPINGS[exception.code as keyof typeof XRPL_ERROR_MAPPINGS];
      return new mapping.exception(mapping.message);
    }

    // Fallback to the exception's defined HTTP status
    switch (exception.httpStatus) {
      case HttpStatus.BAD_REQUEST:
        return new BadRequestException(exception.message);
      case HttpStatus.SERVICE_UNAVAILABLE:
        return new ServiceUnavailableException(exception.message);
      case HttpStatus.REQUEST_TIMEOUT:
        return new RequestTimeoutException(exception.message);
      case HttpStatus.UNPROCESSABLE_ENTITY:
        return new UnprocessableEntityException(exception.message);
      default:
        return new BadRequestException(exception.message);
    }
  }

  private getErrorCodeFromHttpException(exception: HttpException): string {
    const message = exception.message.toLowerCase();

    // Map common error messages to specific codes
    if (message.includes('no xrpl address linked')) {
      return 'NO_XRPL_ADDRESS_LINKED';
    }
    if (message.includes('failed to fetch balance')) {
      return 'BALANCE_FETCH_FAILED';
    }
    if (message.includes('failed to fetch transactions')) {
      return 'TRANSACTIONS_FETCH_FAILED';
    }
    if (message.includes('failed to prepare transaction')) {
      return 'TRANSACTION_PREPARE_FAILED';
    }
    if (message.includes('invalid transaction')) {
      return 'INVALID_TRANSACTION';
    }
    if (message.includes('insufficient balance')) {
      return 'INSUFFICIENT_BALANCE';
    }
    if (message.includes('unauthorized') || message.includes('forbidden')) {
      return 'AUTHORIZATION_ERROR';
    }
    if (message.includes('not found')) {
      return 'RESOURCE_NOT_FOUND';
    }
    if (message.includes('timeout')) {
      return 'REQUEST_TIMEOUT';
    }
    if (message.includes('network') || message.includes('connection')) {
      return 'NETWORK_ERROR';
    }

    // Default based on HTTP status
    const status = exception.getStatus();
    switch (status) {
      case 400:
        return 'BAD_REQUEST';
      case 401:
        return 'UNAUTHORIZED';
      case 403:
        return 'FORBIDDEN';
      case 404:
        return 'NOT_FOUND';
      case 409:
        return 'CONFLICT';
      case 422:
        return 'UNPROCESSABLE_ENTITY';
      case 408:
        return 'REQUEST_TIMEOUT';
      case 503:
        return 'SERVICE_UNAVAILABLE';
      case 500:
        return 'INTERNAL_SERVER_ERROR';
      default:
        return 'HTTP_EXCEPTION';
    }
  }

  private createErrorResponse(
    exception: HttpException,
    code: string,
    request?: any,
  ): {
    statusCode: number;
    message: string;
    error: string;
    code: string;
    timestamp: string;
    path: string;
  } {
    return {
      statusCode: exception.getStatus(),
      message: exception.message,
      error: exception.getResponse() as string,
      code,
      timestamp: new Date().toISOString(),
      path: request?.url || '/api/xrpl',
    };
  }
}

// Helper function to create XRPL exceptions
export const createXrplException = (
  message: string,
  code: string,
  httpStatus?: HttpStatus,
  originalError?: any,
): XrplException => {
  return new XrplException(message, code, httpStatus, originalError);
};

// Comprehensive XRPL error codes and their HTTP mappings
export const XRPL_ERROR_MAPPINGS = {
  // Transaction Engine Results (tec*)
  tecUNFUNDED_PAYMENT: {
    message: 'Insufficient balance to complete the transaction',
    exception: BadRequestException,
  },
  tecPATH_DRY: {
    message: 'Insufficient balance to complete the transaction',
    exception: BadRequestException,
  },
  tecPATH_PARTIAL: {
    message: 'Insufficient balance to complete the transaction',
    exception: BadRequestException,
  },
  tecUNFUNDED_ADD: {
    message: 'Insufficient balance to add trust line',
    exception: BadRequestException,
  },
  tecUNFUNDED_OFFER: {
    message: 'Insufficient balance to place offer',
    exception: BadRequestException,
  },
  tecINSUF_RESERVE_OFFER: {
    message: 'Insufficient reserve to place offer',
    exception: BadRequestException,
  },
  tecINSUF_RESERVE: {
    message: 'Insufficient reserve to complete operation',
    exception: BadRequestException,
  },
  tecOWNERS: {
    message: 'Account has existing trust lines or offers',
    exception: UnprocessableEntityException,
  },
  tecNO_LINE: {
    message: 'Trust line does not exist',
    exception: BadRequestException,
  },
  tecNO_ISSUER: {
    message: 'Issuer account not found',
    exception: BadRequestException,
  },
  tecNO_AUTH: {
    message: 'Not authorized to perform this operation',
    exception: UnprocessableEntityException,
  },
  tecNO_LINE_REDUNDANT: {
    message: 'Trust line already exists',
    exception: BadRequestException,
  },
  tecFROZEN: {
    message: 'Account is frozen',
    exception: UnprocessableEntityException,
  },
  tecTOO_SOON: {
    message: 'Transaction submitted too early',
    exception: BadRequestException,
  },
  tecTOO_LATE: {
    message: 'Transaction submitted too late',
    exception: BadRequestException,
  },
  tecINSUFF_FEE: {
    message: 'Transaction fee too low',
    exception: BadRequestException,
  },
  tecINTERNAL: {
    message: 'Internal XRPL error occurred',
    exception: InternalServerErrorException,
  },

  // Transaction Engine Results (tes*)
  tesSUCCESS: {
    message: 'Transaction successful',
    exception: BadRequestException, // Should not normally reach here
  },

  // Account Errors (act*)
  actNotFound: {
    message: 'Account not found',
    exception: BadRequestException,
  },
  actInsufficientBalance: {
    message: 'Insufficient account balance',
    exception: BadRequestException,
  },
  actInvalid: {
    message: 'Invalid account information',
    exception: BadRequestException,
  },

  // Network and Connection Errors
  noNetwork: {
    message: 'XRPL network connection issue. Please try again later.',
    exception: ServiceUnavailableException,
  },
  timeout: {
    message: 'XRPL request timed out. Please try again.',
    exception: RequestTimeoutException,
  },
  disconnected: {
    message: 'XRPL connection lost. Please try again.',
    exception: ServiceUnavailableException,
  },
  networkError: {
    message: 'XRPL network error. Please try again later.',
    exception: ServiceUnavailableException,
  },

  // Validation Errors
  invalidRequest: {
    message: 'Invalid transaction request',
    exception: BadRequestException,
  },
  invalidParams: {
    message: 'Invalid transaction parameters',
    exception: BadRequestException,
  },
  invalidAddress: {
    message: 'Invalid XRPL address format',
    exception: BadRequestException,
  },
  invalidAmount: {
    message: 'Invalid transaction amount',
    exception: BadRequestException,
  },
  invalidMemo: {
    message: 'Invalid transaction memo',
    exception: BadRequestException,
  },

  // Trust Line Errors
  trustLineExists: {
    message: 'Trust line already exists',
    exception: BadRequestException,
  },
  trustLineNotFound: {
    message: 'Trust line not found',
    exception: BadRequestException,
  },

  // Offer Errors
  offerNotFound: {
    message: 'Offer not found',
    exception: BadRequestException,
  },
  offerExpired: {
    message: 'Offer has expired',
    exception: BadRequestException,
  },

  // Generic XRPL Errors
  xrplOperationFailed: {
    message: 'XRPL operation failed. Please try again.',
    exception: BadRequestException,
  },
  xrplServiceUnavailable: {
    message: 'XRPL service temporarily unavailable. Please try again later.',
    exception: ServiceUnavailableException,
  },

  // Controller-specific error codes
  NO_XRPL_ADDRESS_LINKED: {
    message: 'No XRPL address linked to this account',
    exception: BadRequestException,
  },
  BALANCE_FETCH_FAILED: {
    message: 'Failed to fetch balance from XRPL network',
    exception: InternalServerErrorException,
  },
  TRANSACTIONS_FETCH_FAILED: {
    message: 'Failed to fetch transactions from XRPL network',
    exception: InternalServerErrorException,
  },
  TRANSACTION_PREPARE_FAILED: {
    message: 'Failed to prepare transaction',
    exception: InternalServerErrorException,
  },
  INVALID_TRANSACTION: {
    message: 'Invalid transaction parameters',
    exception: BadRequestException,
  },
  INSUFFICIENT_BALANCE: {
    message: 'Insufficient balance to complete the transaction',
    exception: BadRequestException,
  },
  AUTHORIZATION_ERROR: {
    message: 'Authorization error',
    exception: UnauthorizedException,
  },
  RESOURCE_NOT_FOUND: {
    message: 'Resource not found',
    exception: NotFoundException,
  },
  BAD_REQUEST: {
    message: 'Bad request',
    exception: BadRequestException,
  },
  UNAUTHORIZED: {
    message: 'Unauthorized access',
    exception: UnauthorizedException,
  },
  FORBIDDEN: {
    message: 'Access forbidden',
    exception: ForbiddenException,
  },
  NOT_FOUND: {
    message: 'Resource not found',
    exception: NotFoundException,
  },
  CONFLICT: {
    message: 'Resource conflict',
    exception: ConflictException,
  },
  UNPROCESSABLE_ENTITY: {
    message: 'Unprocessable entity',
    exception: UnprocessableEntityException,
  },
  REQUEST_TIMEOUT: {
    message: 'Request timeout',
    exception: RequestTimeoutException,
  },
  SERVICE_UNAVAILABLE: {
    message: 'Service unavailable',
    exception: ServiceUnavailableException,
  },
  INTERNAL_SERVER_ERROR: {
    message: 'Internal server error',
    exception: InternalServerErrorException,
  },
} as const;
