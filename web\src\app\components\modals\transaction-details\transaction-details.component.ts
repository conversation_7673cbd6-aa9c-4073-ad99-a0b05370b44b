import { Component, Input, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule, ModalController } from '@ionic/angular';
import { Transaction } from '../../../types';

@Component({
  selector: 'app-transaction-details-modal',
  template: `
    <ion-header>
      <ion-toolbar>
        <ion-title>Transaction Details</ion-title>
        <ion-buttons slot="end">
          <ion-button (click)="dismiss()">
            <ion-icon name="close"></ion-icon>
          </ion-button>
        </ion-buttons>
      </ion-toolbar>
    </ion-header>

    <ion-content class="transaction-details-content">
      <div class="transaction-details-container">
        <div class="detail-row">
          <span class="detail-label">Type:</span>
          <span class="detail-value">{{ transaction.type }}</span>
        </div>

        <div class="detail-row">
          <span class="detail-label">Amount:</span>
          <span class="detail-value"
            >{{ transaction.amount }} {{ transaction.currency }}</span
          >
        </div>

        <div class="detail-row">
          <span class="detail-label">From:</span>
          <span class="detail-value">{{
            formatAddress(transaction.account)
          }}</span>
        </div>

        <div class="detail-row">
          <span class="detail-label">To:</span>
          <span class="detail-value">{{
            formatAddress(transaction.destination)
          }}</span>
        </div>

        <div class="detail-row">
          <span class="detail-label">Fee:</span>
          <span class="detail-value">{{ transaction.fee }} XRP</span>
        </div>

        <div class="detail-row">
          <span class="detail-label">Ledger:</span>
          <span class="detail-value">{{ transaction.ledgerIndex }}</span>
        </div>

        <div class="detail-row">
          <span class="detail-label">Date:</span>
          <span class="detail-value">{{ formatDate(transaction.date) }}</span>
        </div>

        <div class="detail-row" *ngIf="transaction.memo">
          <span class="detail-label">Memo:</span>
          <span class="detail-value">{{ transaction.memo }}</span>
        </div>

        <div class="detail-row hash-row">
          <span class="detail-label">Hash:</span>
          <span class="detail-value hash-value">{{ transaction.hash }}</span>
        </div>
      </div>
    </ion-content>

    <ion-footer>
      <ion-toolbar>
        <ion-button expand="block" (click)="copyHash()">
          <ion-icon name="copy-outline" slot="start"></ion-icon>
          Copy Hash
        </ion-button>
      </ion-toolbar>
    </ion-footer>
  `,
  styles: [
    `
      .transaction-details-content {
        --padding-start: 16px;
        --padding-end: 16px;
        --padding-top: 16px;
        --padding-bottom: 16px;
      }

      .transaction-details-container {
        background: var(--ion-color-light);
        border-radius: 12px;
        padding: 16px;
        margin: 16px 0;
      }

      .detail-row {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        padding: 8px 0;
        border-bottom: 1px solid var(--ion-color-light-shade);
      }

      .detail-row:last-child {
        border-bottom: none;
      }

      .detail-label {
        font-weight: 600;
        color: var(--ion-color-dark);
        min-width: 80px;
        margin-right: 16px;
      }

      .detail-value {
        color: var(--ion-color-medium);
        text-align: right;
        word-break: break-all;
        flex: 1;
      }

      .hash-row {
        flex-direction: column;
        align-items: flex-start;
      }

      .hash-row .detail-label {
        margin-bottom: 8px;
      }

      .hash-value {
        font-family: 'Courier New', monospace;
        font-size: 12px;
        background: var(--ion-color-light-shade);
        padding: 8px;
        border-radius: 4px;
        word-break: break-all;
        text-align: left;
      }
    `,
  ],
  standalone: true,
  imports: [CommonModule, IonicModule],
})
export class TransactionDetailsModalComponent {
  @Input() transaction!: Transaction;

  private modalController = inject(ModalController);

  formatAddress(address: string): string {
    if (!address) return '';
    // Show first 6 and last 4 characters of the address
    if (address.length > 10) {
      return `${address.substring(0, 6)}...${address.substring(address.length - 4)}`;
    }
    return address;
  }

  formatDate(dateString: string): string {
    return new Date(dateString).toLocaleString();
  }

  async copyHash(): Promise<void> {
    if (navigator.clipboard) {
      try {
        await navigator.clipboard.writeText(this.transaction.hash);
        // You could show a toast here if needed
      } catch (err) {
        console.error('Failed to copy hash:', err);
      }
    }
  }

  dismiss(): void {
    this.modalController.dismiss();
  }
}
