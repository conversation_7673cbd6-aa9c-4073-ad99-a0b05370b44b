<!-- Create Sale Form -->
<ion-card class="create-sale-card" #formCard>
  <ion-card-header>
    <ion-card-title color="white">
      <ion-icon name="add-circle" class="title-icon"></ion-icon>
      Create New Token Sale
    </ion-card-title>
    <ion-card-subtitle
      >Set up a new token sale for your project</ion-card-subtitle
    >
  </ion-card-header>
  <ion-card-content>
    <form [formGroup]="saleForm" (ngSubmit)="onSubmit()" class="sale-form">
      <!-- Form-level error -->
      <div *ngIf="getFormError()" class="form-alert">
        <ion-icon name="warning" slot="start"></ion-icon>
        <ion-text color="danger">{{ getFormError() }}</ion-text>
      </div>

      <!-- Basic Information Section -->
      <div class="form-section">
        <h3 class="section-title">
          <ion-icon name="information-circle" class="section-icon"></ion-icon>
          Basic Information
        </h3>

        <div class="form-row">
          <ion-item class="form-item" [class.error]="getFieldError('symbol')">
            <ion-label position="stacked">
              Token Symbol <span class="required">*</span>
            </ion-label>
            <ion-input
              formControlName="symbol"
              placeholder="e.g., BTC, ETH"
              type="text"
              [class.error]="getFieldError('symbol')"
            >
            </ion-input>
            <ion-note slot="helper"
              >2-10 characters, uppercase letters and numbers only</ion-note
            >
          </ion-item>

          <ion-item class="form-item" [class.error]="getFieldError('price')">
            <ion-label position="stacked">
              Token Price (XRP) <span class="required">*</span>
            </ion-label>
            <ion-input
              formControlName="price"
              placeholder="0.001"
              type="number"
              step="0.000001"
              [class.error]="getFieldError('price')"
            >
            </ion-input>
            <ion-note slot="helper">Minimum: 0.000001 XRP</ion-note>
          </ion-item>
        </div>

        <ion-item
          class="form-item"
          [class.error]="getFieldError('description')"
        >
          <ion-label position="stacked">Description</ion-label>
          <ion-textarea
            formControlName="description"
            placeholder="Brief description of the token sale..."
            rows="3"
            [class.error]="getFieldError('description')"
          >
          </ion-textarea>
          <ion-note slot="helper"
            >Optional: Describe your project and token sale</ion-note
          >
        </ion-item>
      </div>

      <!-- Sale Timeline Section -->
      <div class="form-section">
        <h3 class="section-title">
          <ion-icon name="time" class="section-icon"></ion-icon>
          Sale Timeline
        </h3>

        <div class="form-row">
          <ion-item class="form-item" [class.error]="getFieldError('start')">
            <ion-label position="stacked">
              Sale Start Date <span class="required">*</span>
            </ion-label>
            <ion-datetime
              formControlName="start"
              presentation="date-time"
              [class.error]="getFieldError('start')"
            >
            </ion-datetime>
          </ion-item>

          <ion-item class="form-item" [class.error]="getFieldError('end')">
            <ion-label position="stacked">
              Sale End Date <span class="required">*</span>
            </ion-label>
            <ion-datetime
              formControlName="end"
              presentation="date-time"
              [class.error]="getFieldError('end')"
            >
            </ion-datetime>
          </ion-item>
        </div>
      </div>

      <!-- Funding Goals Section -->
      <div class="form-section">
        <h3 class="section-title">
          <ion-icon name="trending-up" class="section-icon"></ion-icon>
          Funding Goals
        </h3>

        <div class="form-row">
          <ion-item class="form-item" [class.error]="getFieldError('softCap')">
            <ion-label position="stacked">
              Soft Cap (XRP) <span class="required">*</span>
            </ion-label>
            <ion-input
              formControlName="softCap"
              placeholder="1000"
              type="number"
              step="1"
              [class.error]="getFieldError('softCap')"
            >
            </ion-input>
            <ion-note slot="helper">Minimum funding target</ion-note>
          </ion-item>

          <ion-item class="form-item" [class.error]="getFieldError('hardCap')">
            <ion-label position="stacked">
              Hard Cap (XRP) <span class="required">*</span>
            </ion-label>
            <ion-input
              formControlName="hardCap"
              placeholder="10000"
              type="number"
              step="1"
              [class.error]="getFieldError('hardCap')"
            >
            </ion-input>
            <ion-note slot="helper">Maximum funding target</ion-note>
          </ion-item>
        </div>
      </div>

      <!-- Collection Details Section -->
      <div class="form-section">
        <h3 class="section-title">
          <ion-icon name="wallet" class="section-icon"></ion-icon>
          Collection Details
        </h3>

        <ion-item
          class="form-item"
          [class.error]="getFieldError('collectionAddress')"
        >
          <ion-label position="stacked">
            Collection Address (XRPL) <span class="required">*</span>
          </ion-label>
          <ion-input
            formControlName="collectionAddress"
            placeholder="rXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX"
            type="text"
            [class.error]="getFieldError('collectionAddress')"
          >
          </ion-input>
          <ion-note slot="helper"
            >XRPL address where funds will be collected</ion-note
          >
        </ion-item>
      </div>

      <!-- Additional Links Section -->
      <div class="form-section">
        <h3 class="section-title">
          <ion-icon name="link" class="section-icon"></ion-icon>
          Additional Links
        </h3>

        <div class="form-row">
          <ion-item class="form-item" [class.error]="getFieldError('website')">
            <ion-label position="stacked">Website URL</ion-label>
            <ion-input
              formControlName="website"
              placeholder="https://example.com"
              type="url"
              [class.error]="getFieldError('website')"
            >
            </ion-input>
          </ion-item>

          <ion-item
            class="form-item"
            [class.error]="getFieldError('whitepaper')"
          >
            <ion-label position="stacked">Whitepaper URL</ion-label>
            <ion-input
              formControlName="whitepaper"
              placeholder="https://example.com/whitepaper.pdf"
              type="url"
              [class.error]="getFieldError('whitepaper')"
            >
            </ion-input>
          </ion-item>
        </div>
      </div>

      <!-- Field Error Messages -->
      <div class="error-messages" *ngIf="hasFieldErrors()">
        <ion-item
          *ngFor="let field of getFieldsWithErrors()"
          color="danger"
          class="error-item"
        >
          <ion-icon name="alert-circle" slot="start"></ion-icon>
          <ion-label>
            <ion-text color="danger">{{ getFieldError(field) }}</ion-text>
          </ion-label>
        </ion-item>
      </div>

      <!-- Submit Button -->
      <div class="submit-section">
        <ion-button
          expand="block"
          type="submit"
          size="large"
          [disabled]="saleForm.invalid || isSubmitting()"
          [fill]="saleForm.valid ? 'solid' : 'outline'"
          class="submit-button"
        >
          <ion-spinner *ngIf="isSubmitting()" name="crescent"></ion-spinner>
          <ion-icon
            *ngIf="!isSubmitting()"
            name="add-circle"
            slot="start"
          ></ion-icon>
          {{ isSubmitting() ? 'Creating Sale...' : 'Create Token Sale' }}
        </ion-button>
      </div>
    </form>
  </ion-card-content>
</ion-card>
