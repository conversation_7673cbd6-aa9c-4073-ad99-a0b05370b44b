import { Client } from 'pg';
import { hash } from 'bcryptjs';
import { config } from 'dotenv';

// Load environment variables
config();

async function seedDatabase(): Promise<void> {
  const client = new Client({
    host: process.env.DATABASE_HOST || 'localhost',
    port: parseInt(process.env.DATABASE_PORT || '5432', 10),
    database: process.env.DATABASE_NAME || 'xrpl_launchpad',
    user: process.env.DATABASE_USERNAME || 'postgres',
    password: process.env.DATABASE_PASSWORD || 'password',
  });

  try {
    await client.connect();
    console.log('🌱 Connected to PostgreSQL, starting database seeding...');

    // Check if admin user already exists
    const existingAdmin = await client.query(
      'SELECT * FROM users WHERE email = $1',
      ['<EMAIL>'],
    );

    if (existingAdmin.rows.length > 0) {
      console.log('✅ Admin user already exists, skipping...');
      return;
    }

    // Hash the password
    const hashedPassword = await hash('admin123', 10);

    // Create admin user
    const result = await client.query(
      `
      INSERT INTO users (username, email, password, role, "xrplAddress", "createdAt", "updatedAt")
      VALUES ($1, $2, $3, $4, $5, NOW(), NOW())
      RETURNING id, username, email, role
    `,
      [
        'admin',
        '<EMAIL>',
        hashedPassword,
        'admin',
        'rXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX',
      ],
    );

    console.log('✅ Admin user created successfully!');
    console.log('📧 Email: <EMAIL>');
    console.log('🔑 Password: admin123');
    console.log('👤 Username: admin');
    console.log('🔐 Role: admin');
    console.log('🆔 User ID:', (result.rows[0] as { id: string }).id);
    console.log('\n⚠️  IMPORTANT: Change these credentials in production!');
  } catch (error) {
    console.error(
      '❌ Failed to seed database:',
      error instanceof Error ? error.message : 'Unknown error',
    );

    if (error instanceof Error && 'code' in error) {
      const pgError = error as { code: string };
      if (pgError.code === '42P01') {
        console.log(
          '\n💡 The users table might not exist. Run migrations first:',
        );
        console.log('   npm run migration:run');
      } else if (pgError.code === '23505') {
        console.log(
          "\n💡 User already exists or there's a constraint violation.",
        );
      }
    }

    throw error;
  } finally {
    await client.end();
  }
}

// Run the seeder
seedDatabase()
  .then(() => {
    console.log('\n🎉 Database seeding completed!');
    process.exit(0);
  })
  .catch((error: Error) => {
    console.error('\n💥 Database seeding failed!', error);
    process.exit(1);
  });
