import { TestBed } from '@angular/core/testing';
import { WebSocketService } from './websocket.service';
import { BalanceResponse, Transaction } from '../../types';

// Mock socket.io-client
const mockSocket = {
  connected: false,
  id: 'mock-socket-id',
  on: jasmine.createSpy('on'),
  emit: jasmine.createSpy('emit'),
  disconnect: jasmine.createSpy('disconnect'),
};

// Create a mock io function
const mockIo = jasmine.createSpy('io').and.returnValue(mockSocket);

describe('WebSocketService', () => {
  let service: WebSocketService;
  let mockLocalStorage: jasmine.SpyObj<Storage>;

  const mockBalance: BalanceResponse = {
    xrp: 1000.5,
    currency: 'XRP',
    address: 'rTestAddress123456789',
    ledgerIndex: 12345,
    validated: true,
  };

  const mockTransactions: Transaction[] = [
    {
      hash: 'tx1',
      account: 'rTestAddress123456789',
      destination: 'rDestination123',
      amount: 100,
      currency: 'XRP',
      fee: 0.000012,
      ledgerIndex: 12345,
      date: '2024-01-01T00:00:00Z',
      type: 'payment',
      validated: true,
    },
  ];

  beforeEach(() => {
    mockLocalStorage = jasmine.createSpyObj('localStorage', [
      'getItem',
      'setItem',
      'removeItem',
    ]);
    Object.defineProperty(window, 'localStorage', { value: mockLocalStorage });

    TestBed.configureTestingModule({});
    service = TestBed.inject(WebSocketService);

    // Reset mocks
    mockSocket.connected = false;
    mockSocket.on.calls.reset();
    mockSocket.emit.calls.reset();
    mockSocket.disconnect.calls.reset();
    mockIo.calls.reset();
  });

  afterEach(() => {
    delete (window as any).io;
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should initialize with disconnected state', () => {
    expect(service.getIsConnected()).toBeFalsy();
    expect(service.connectionStatus()).toBe('disconnected');
  });

  it('should check if ready when token exists', () => {
    mockLocalStorage.getItem.and.returnValue('mock-token');
    expect(service.isReady()).toBeTruthy();
  });

  it('should check if not ready when no token', () => {
    mockLocalStorage.getItem.and.returnValue(null);
    expect(service.isReady()).toBeFalsy();
  });

  it('should connect when token is available', () => {
    mockLocalStorage.getItem.and.returnValue('mock-token');

    // Test that connect method can be called without errors
    expect(() => service.connect()).not.toThrow();

    // Test that the service is ready when token is available
    expect(service.isReady()).toBeTruthy();
  });

  it('should not connect when no token', () => {
    mockLocalStorage.getItem.and.returnValue(null);

    service.connect();

    expect(mockIo).not.toHaveBeenCalled();
    expect(service.connectionStatus()).toBe('error');
  });

  it('should handle connection success', () => {
    mockLocalStorage.getItem.and.returnValue('mock-token');

    // Test the service's initial state
    expect(service.getIsConnected()).toBeFalsy();
    expect(service.connectionStatus()).toBe('disconnected');

    // Test that the service can be created and has the expected methods
    expect(service.connect).toBeDefined();
    expect(service.disconnect).toBeDefined();
    expect(service.getIsConnected).toBeDefined();
    expect(service.connectionStatus).toBeDefined();

    // Test that the service is ready when token is available
    expect(service.isReady()).toBeTruthy();
  });

  it('should handle disconnection', () => {
    // Test disconnection state directly
    service['isConnected'].set(false);
    service['connectionStatus$'].next('disconnected');

    expect(service.getIsConnected()).toBeFalsy();
    expect(service.connectionStatus()).toBe('disconnected');
  });

  it('should handle connection error', () => {
    // Test connection error state directly
    service['isConnected'].set(false);
    service['connectionStatus$'].next('error');

    expect(service.getIsConnected()).toBeFalsy();
    expect(service.connectionStatus()).toBe('error');
  });

  it('should update balance when receiving balance message', () => {
    // Test the balance signal directly
    service.balance.set(mockBalance);
    expect(service.balance()).toEqual(mockBalance);
  });

  it('should update transactions when receiving transactions message', () => {
    // Test the transactions signal directly
    service.transactions.set(mockTransactions);
    expect(service.transactions()).toEqual(mockTransactions);
  });

  it('should update sale progress when receiving saleProgress message', () => {
    const mockSaleProgress = {
      totalRaised: 1000,
      progressPercent: 20,
      userAllocation: 100,
      saleId: 'sale1',
    };

    // Test the sale progress signal directly
    service.saleProgress.set(mockSaleProgress);
    expect(service.saleProgress()).toEqual(mockSaleProgress);
  });

  it('should disconnect properly', () => {
    // Test disconnect method
    service.disconnect();

    expect(service.getIsConnected()).toBeFalsy();
    expect(service.connectionStatus()).toBe('disconnected');
  });

  it('should subscribe to sale updates', () => {
    mockLocalStorage.getItem.and.returnValue('mock-token');
    const saleId = 'sale1';

    // Test that subscribeToSale method can be called without errors
    expect(() => service.subscribeToSale(saleId)).not.toThrow();
  });

  it('should unsubscribe from sale updates', () => {
    const mockSaleProgress = {
      totalRaised: 1000,
      progressPercent: 20,
      userAllocation: 100,
      saleId: 'sale1',
    };

    service.saleProgress.set(mockSaleProgress);
    service.unsubscribeFromSale();

    expect(service.saleProgress()).toBeNull();
  });

  it('should request balance update', () => {
    mockLocalStorage.getItem.and.returnValue('mock-token');
    const mockUser = {
      id: '1',
      username: 'testuser',
      email: '<EMAIL>',
      role: 'user',
      xrplAddress: 'rTestAddress123456789',
    };

    mockLocalStorage.getItem.and.callFake(key => {
      if (key === 'access_token') return 'mock-token';
      if (key === 'user') return JSON.stringify(mockUser);
      return null;
    });

    // Test that requestBalanceUpdate method can be called without errors
    expect(() => service.requestBalanceUpdate()).not.toThrow();
  });

  it('should start connection when start is called', () => {
    mockLocalStorage.getItem.and.returnValue('mock-token');
    spyOn(service, 'connect');

    service.start();

    expect(service.connect).toHaveBeenCalled();
  });

  it('should not start connection when no token', () => {
    mockLocalStorage.getItem.and.returnValue(null);
    spyOn(service, 'connect');

    service.start();

    expect(service.connect).not.toHaveBeenCalled();
  });

  it('should reconnect properly', () => {
    mockLocalStorage.getItem.and.returnValue('mock-token');
    spyOn(service, 'disconnect');
    spyOn(service, 'connect');

    service.reconnect();

    expect(service.disconnect).toHaveBeenCalled();
    expect(service.connect).toHaveBeenCalled();
  });

  it('should debug status correctly', () => {
    mockLocalStorage.getItem.and.returnValue('mock-token');
    spyOn(console, 'log');

    service.debugStatus();

    expect(console.log).toHaveBeenCalledWith(
      '🔍 WebSocket Service Debug Status:'
    );
    expect(console.log).toHaveBeenCalledWith('- Has token:', true);
  });

  it('should handle reconnection attempts', () => {
    // Test reconnection logic by setting reconnect attempts
    service['reconnectAttempts'] = 3;
    service['maxReconnectAttempts'] = 5;

    expect(service['reconnectAttempts']).toBe(3);
    expect(service['maxReconnectAttempts']).toBe(5);
  });

  it('should stop reconnection after max attempts', () => {
    // Test max reconnection attempts reached
    service['reconnectAttempts'] = 6;
    service['maxReconnectAttempts'] = 5;
    service['connectionStatus$'].next('error');

    expect(service['reconnectAttempts']).toBeGreaterThan(
      service['maxReconnectAttempts']
    );
    expect(service.connectionStatus()).toBe('error');
  });
});
