import {
  Controller,
  Get,
  Post,
  Body,
  UseGuards,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { CurrentUser } from '../auth/decorators/current-user.decorator';
import { User } from '../users/entities/user.entity';
import { XrplService } from './xrpl.service';
import { BalanceDto } from './dto/balance.dto';
import { TransactionDto } from './dto/transaction.dto';
import { SendTransactionDto } from './dto/send-transaction.dto';
import { PreparedTransactionDto } from './dto/prepared-transaction.dto';

@ApiTags('XRPL')
@Controller('xrpl')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class XrplController {
  constructor(private readonly xrplService: XrplService) {}

  @Get('balance')
  @ApiOperation({
    summary: 'Get XRPL balance',
    description:
      "Retrieve the current XRPL balance for the authenticated user's linked address",
  })
  @ApiResponse({
    status: 200,
    description: 'Balance retrieved successfully',
    schema: {
      $ref: '#/components/schemas/BalanceDto',
    },
  })
  @ApiResponse({
    status: 400,
    description: 'No XRPL address linked to user account',
  })
  @ApiResponse({
    status: 500,
    description: 'Failed to fetch balance from XRPL network',
  })
  async getBalance(@CurrentUser() user: User): Promise<BalanceDto> {
    if (!user.xrplAddress) {
      throw new HttpException(
        'No XRPL address linked to this account',
        HttpStatus.BAD_REQUEST,
      );
    }

    try {
      return await this.xrplService.getBalance(user.xrplAddress);
    } catch (error) {
      throw new HttpException(
        `Failed to fetch balance: ${error instanceof Error ? error.message : 'Unknown error'}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('transactions')
  @ApiOperation({
    summary: 'Get XRPL transactions',
    description:
      "Retrieve recent XRPL transactions for the authenticated user's linked address",
  })
  @ApiResponse({
    status: 200,
    description: 'Transactions retrieved successfully',
    schema: {
      type: 'array',
      items: {
        $ref: '#/components/schemas/TransactionDto',
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'No XRPL address linked to user account',
  })
  @ApiResponse({
    status: 500,
    description: 'Failed to fetch transactions from XRPL network',
  })
  async getTransactions(@CurrentUser() user: User): Promise<TransactionDto[]> {
    if (!user.xrplAddress) {
      throw new HttpException(
        'No XRPL address linked to this account',
        HttpStatus.BAD_REQUEST,
      );
    }

    try {
      return await this.xrplService.getTransactions(user.xrplAddress, 10);
    } catch (error) {
      throw new HttpException(
        `Failed to fetch transactions: ${error instanceof Error ? error.message : 'Unknown error'}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('send')
  @ApiOperation({
    summary: 'Prepare XRPL payment transaction',
    description:
      'Prepare an unsigned XRPL payment transaction for client-side signing',
  })
  @ApiResponse({
    status: 200,
    description: 'Transaction prepared successfully',
    schema: {
      $ref: '#/components/schemas/PreparedTransactionDto',
    },
  })
  @ApiResponse({
    status: 400,
    description:
      'No XRPL address linked to user account or invalid transaction parameters',
  })
  @ApiResponse({
    status: 500,
    description: 'Failed to prepare transaction or XRPL network error',
  })
  async prepareSendTransaction(
    @CurrentUser() user: User,
    @Body() sendDto: SendTransactionDto,
  ): Promise<PreparedTransactionDto> {
    if (!user.xrplAddress) {
      throw new HttpException(
        'No XRPL address linked to this account',
        HttpStatus.BAD_REQUEST,
      );
    }

    try {
      return await this.xrplService.prepareSendTransaction(
        user.xrplAddress,
        sendDto.destination,
        sendDto.amount,
      );
    } catch (error) {
      // If it's already an XrplException, re-throw it
      if (error instanceof Error && 'code' in error) {
        throw error;
      }

      throw new HttpException(
        `Failed to prepare transaction: ${error instanceof Error ? error.message : 'Unknown error'}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
