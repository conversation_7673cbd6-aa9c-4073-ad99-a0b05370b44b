<ion-card class="balance-card">
  <ion-card-header>
    <ion-card-title>
      <ion-icon name="wallet-outline"></ion-icon>
      XRPL Balance
    </ion-card-title>
  </ion-card-header>
  <ion-card-content>
    @if (balance) {
      <div class="balance-amount">
        <span class="currency">XRP</span>
        <span class="amount">{{ balance.xrp }}</span>
      </div>
      <div class="balance-details">
        <p class="address">{{ balance.address }}</p>
        <p class="ledger">Ledger: {{ balance.ledgerIndex }}</p>
      </div>
    } @else {
      <ion-skeleton-text animated></ion-skeleton-text>
    }
  </ion-card-content>
</ion-card>
