import {
  Controller,
  Post,
  Body,
  UseGuards,
  HttpCode,
  HttpStatus,
  BadRequestException,
  ConflictException,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { CurrentUser } from '../auth/decorators/current-user.decorator';
import { UsersService } from './users.service';
import { XrplAddressService } from '../xrpl/xrpl-address.service';
import { LinkXrplAddressDto } from './dto/link-xrpl-address.dto';
import { SuccessResponseDto } from './dto/success-response.dto';
import { User } from './entities/user.entity';

@ApiTags('users')
@Controller('users')
export class UsersController {
  constructor(
    private readonly usersService: UsersService,
    private readonly xrplAddressService: XrplAddressService,
  ) {}

  @Post('xrpl/link')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Link XRPL address to user account',
    description:
      'Associates an XRPL Testnet address with the authenticated user',
  })
  @ApiResponse({
    status: 200,
    description: 'XRPL address successfully linked',
    type: SuccessResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid XRPL address format',
  })
  @ApiResponse({
    status: 409,
    description: 'XRPL address already linked to another user',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing JWT token',
  })
  @HttpCode(HttpStatus.OK)
  async linkXrplAddress(
    @CurrentUser() user: User,
    @Body() linkXrplAddressDto: LinkXrplAddressDto,
  ): Promise<SuccessResponseDto> {
    const { xrplAddress } = linkXrplAddressDto;

    // Validate XRPL address format
    if (!this.xrplAddressService.validateAddress(xrplAddress)) {
      throw new BadRequestException('Invalid XRPL address format');
    }

    try {
      const updatedUser = await this.usersService.linkXrplAddress(
        user.id,
        xrplAddress,
      );

      if (!updatedUser) {
        throw new BadRequestException('User not found');
      }

      return {
        success: true,
        message: 'XRPL address successfully linked',
      };
    } catch (error) {
      if (error instanceof Error && error.message.includes('already linked')) {
        throw new ConflictException(error.message);
      }
      throw error;
    }
  }
}
