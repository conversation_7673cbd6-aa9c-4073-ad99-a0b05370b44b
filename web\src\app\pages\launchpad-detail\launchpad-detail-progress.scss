// Progress and details component styles

.progress-card {
  .progress-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    h3 {
      font-size: 18px;
      font-weight: 600;
      color: var(--ion-color-dark);
      margin: 0;
    }

    .time-remaining {
      font-size: 14px;
      color: var(--ion-color-warning);
      font-weight: 500;
    }
  }

  .progress-bar-container {
    margin-bottom: 20px;

    .main-progress-bar {
      margin-bottom: 12px;
      --progress-background: var(--ion-color-light-shade);
      --progress-bar-background: var(--ion-color-primary);
    }

    .progress-stats {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 14px;

      .raised {
        color: var(--ion-color-success);
        font-weight: 600;
      }

      .percentage {
        color: var(--ion-color-primary);
        font-weight: 700;
        font-size: 16px;
      }

      .target {
        color: var(--ion-color-medium);
      }
    }
  }

  .contribution-limits {
    display: flex;
    gap: 16px;

    .limit-item {
      flex: 1;
      display: flex;
      align-items: center;
      padding: 12px;
      background: var(--ion-color-light-shade);
      border-radius: 8px;

      ion-icon {
        margin-right: 8px;
        color: var(--ion-color-primary);
      }

      span {
        font-size: 14px;
        color: var(--ion-color-dark);
        font-weight: 500;
      }
    }
  }
}

.details-card {
  .detail-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;

    .detail-item {
      display: flex;
      align-items: center;
      padding: 12px;
      background: var(--ion-color-light-shade);
      border-radius: 8px;

      ion-icon {
        font-size: 20px;
        color: var(--ion-color-primary);
        margin-right: 12px;
      }

      .detail-content {
        display: flex;
        flex-direction: column;

        .label {
          font-size: 12px;
          color: var(--ion-color-medium);
          margin-bottom: 4px;
          font-weight: 500;
        }

        .value {
          font-size: 14px;
          color: var(--ion-color-dark);
          font-weight: 600;
          word-break: break-all;
        }
      }
    }
  }
}

.description-card {
  .long-description {
    font-size: 16px;
    line-height: 1.6;
    color: var(--ion-color-dark);
    margin: 0;
  }
}
