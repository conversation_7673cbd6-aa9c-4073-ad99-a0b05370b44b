# Local CI Script for XRPL Launchpad (PowerShell)
# This script runs the same checks locally that the CI pipeline runs

param(
    [switch]$SkipInstall
)

# Set error action preference
$ErrorActionPreference = "Stop"

Write-Host "🚀 Starting local CI checks..." -ForegroundColor Blue

# Function to print colored output
function Write-Status {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

# Check if we're in the right directory
if (-not (Test-Path "package.json") -or -not (Test-Path "api/package.json") -or -not (Test-Path "web/package.json")) {
    Write-Error "Please run this script from the root of the xrpl-launchpad repository"
    exit 1
}

# Check if Node.js is installed
try {
    $nodeVersion = node --version
    $npmVersion = npm --version
} catch {
    Write-Error "Node.js is not installed. Please install Node.js 18+ and try again."
    exit 1
}

# Check Node.js version
$nodeMajorVersion = [int]($nodeVersion -replace 'v', '' -split '\.' | Select-Object -First 1)
if ($nodeMajorVersion -lt 18) {
    Write-Warning "Node.js version $nodeVersion detected. Version 18+ is recommended."
}

Write-Status "Node.js version: $nodeVersion"
Write-Status "npm version: $npmVersion"

# Install dependencies (unless skipped)
if (-not $SkipInstall) {
    Write-Status "Installing root dependencies..."
    npm ci

    Write-Status "Installing API dependencies..."
    npm run -w api ci

    Write-Status "Installing Web dependencies..."
    npm run -w web ci
} else {
    Write-Warning "Skipping dependency installation due to -SkipInstall flag"
}

# Run linting checks
Write-Status "Running linting checks..."
npm run lint:check

# Check formatting
Write-Status "Checking code formatting..."
npm run format:check

# Run tests
Write-Status "Running API tests..."
npm run api:test

Write-Status "Running Web tests..."
npm run web:test

# Build everything
Write-Status "Building applications..."
npm run build

Write-Success "🎉 All local CI checks passed!"
Write-Status "Your code is ready to commit and push!"

# Optional: Check for outdated dependencies
Write-Status "Checking for outdated dependencies..."
try {
    npm outdated
} catch {
    Write-Warning "Some dependencies may be outdated. Consider updating them."
}

Write-Status "Local CI checks completed successfully!"
