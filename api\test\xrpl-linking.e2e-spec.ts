import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { JwtStrategy } from '../src/auth/strategies/jwt.strategy';
import { UsersModule } from '../src/users/users.module';
import { User, UserRole } from '../src/users/entities/user.entity';
import { Repository } from 'typeorm';
import { getRepositoryToken } from '@nestjs/typeorm';
import request from 'supertest';
import { JwtAuthGuard } from '../src/auth/guards/jwt-auth.guard';
import { testDatabaseConfig } from './test-database.config';
import { JwtService } from '@nestjs/jwt';

describe('XRPL Linking (e2e)', () => {
  let app: INestApplication;
  let userRepository: Repository<User>;
  let jwtService: JwtService;
  let jwtToken: string;
  let testUser: User;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({
          isGlobal: true,
          envFilePath: '.env.test',
        }),
        TypeOrmModule.forRoot({
          ...testDatabaseConfig,
          dropSchema: false,
          synchronize: true,
          logging: true,
        }),
        TypeOrmModule.forFeature([User]),
        UsersModule,
        PassportModule,
        JwtModule.register({
          secret: process.env.JWT_SECRET || 'test-secret',
          signOptions: { expiresIn: '1h' },
        }),
      ],
      providers: [
        JwtStrategy,
        {
          provide: JwtAuthGuard,
          useValue: {
            canActivate: jest.fn((context) => {
              const request = context.switchToHttp().getRequest();
              request.user = testUser;
              return true;
            }),
          },
        },
      ],
    }).compile();

    app = moduleFixture.createNestApplication();
    userRepository = moduleFixture.get<Repository<User>>(
      getRepositoryToken(User),
    );
    jwtService = moduleFixture.get<JwtService>(JwtService);

    await app.init();

    try {
      // Clean up db, delete any existing test users
      await userRepository.delete({ username: 'testuser' });
      await userRepository.delete({ username: 'otheruser' });
      await userRepository.delete({ email: '<EMAIL>' });
      await userRepository.delete({ email: '<EMAIL>' });
    } catch (error) {
      console.warn('Database cleanup failed:', error);
    }

    testUser = await userRepository.save({
      username: 'testuser',
      email: '<EMAIL>',
      password: 'hashedpassword',
      role: UserRole.USER,
      xrplAddress: '',
    });

    // Generate JWT token for testing
    jwtToken = jwtService.sign({
      sub: testUser.id,
      username: testUser.username,
    });
  });

  afterAll(async () => {
    // Clean up test data
    try {
      if (userRepository && testUser) {
        await userRepository.delete({ id: testUser.id });
      }

      await userRepository.delete({ username: 'testuser' });
      await userRepository.delete({ username: 'otheruser' });
      await userRepository.delete({ email: '<EMAIL>' });
      await userRepository.delete({ email: '<EMAIL>' });
    } catch (error) {
      console.warn('Final cleanup failed:', error);
    }
    await app.close();
  });

  beforeEach(async () => {
    // Reset user's XRPL address before each test
    if (testUser) {
      await userRepository.update(testUser.id, { xrplAddress: '' });
      // Refresh the testUser object to get the updated data
      const refreshedUser = await userRepository.findOne({
        where: { id: testUser.id },
      });
      if (refreshedUser) {
        testUser = refreshedUser;
      }
    }
  });

  describe('/users/xrpl/link (POST)', () => {
    it('should verify test setup is working', async () => {
      // Verify that the user starts with no XRPL address
      expect(testUser.xrplAddress).toBe('');

      // Verify we can link an address
      const response = await request(app.getHttpServer())
        .post('/users/xrpl/link')
        .set('Authorization', `Bearer ${jwtToken}`)
        .send({
          xrplAddress: 'rHb9CJAWyB4rj91VRWn96DkukG4bwdtyTh',
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe('XRPL address successfully linked');
    });

    it('should successfully link a valid XRPL address', async () => {
      const validAddress = 'rHb9CJAWyB4rj91VRWn96DkukG4bwdtyTh';

      const response = await request(app.getHttpServer())
        .post('/users/xrpl/link')
        .set('Authorization', `Bearer ${jwtToken}`)
        .send({ xrplAddress: validAddress })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe('XRPL address successfully linked');

      // Verify the address was saved in the database
      const updatedUser = await userRepository.findOne({
        where: { id: testUser.id },
      });
      expect(updatedUser?.xrplAddress).toBe(validAddress);
    });

    it('should reject invalid XRPL address format', async () => {
      const invalidAddress = 'invalid-address';

      await request(app.getHttpServer())
        .post('/users/xrpl/link')
        .set('Authorization', `Bearer ${jwtToken}`)
        .send({ xrplAddress: invalidAddress })
        .expect(400);
    });

    it('should reject empty XRPL address', async () => {
      await request(app.getHttpServer())
        .post('/users/xrpl/link')
        .set('Authorization', `Bearer ${jwtToken}`)
        .send({ xrplAddress: '' })
        .expect(400);
    });

    it('should reject request without XRPL address', async () => {
      await request(app.getHttpServer())
        .post('/users/xrpl/link')
        .set('Authorization', `Bearer ${jwtToken}`)
        .send({})
        .expect(400);
    });

    it('should reject request without authentication', async () => {
      const validAddress = 'rHb9CJAWyB4rj91VRWn96DkukG4bwdtyTh';

      await request(app.getHttpServer())
        .post('/users/xrpl/link')
        .send({ xrplAddress: validAddress })
        .expect(401);
    });

    it('should allow user to update their own XRPL address', async () => {
      const firstAddress = 'rHb9CJAWyB4rj91VRWn96DkukG4bwdtyTh';
      const secondAddress = 'r3kmLJN5D28dHuH8vZNUZpMC43pEHpaocV';

      await userRepository.update(testUser.id, { xrplAddress: '' });

      // Link first address
      const firstResponse = await request(app.getHttpServer())
        .post('/users/xrpl/link')
        .set('Authorization', `Bearer ${jwtToken}`)
        .send({ xrplAddress: firstAddress })
        .expect(200);

      // Verify first address was linked
      expect(firstResponse.body.success).toBe(true);
      expect(firstResponse.body.message).toBe(
        'XRPL address successfully linked',
      );

      // Now try to update to second address
      const response = await request(app.getHttpServer())
        .post('/users/xrpl/link')
        .set('Authorization', `Bearer ${jwtToken}`)
        .send({ xrplAddress: secondAddress });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe('XRPL address successfully linked');

      // Verify the address was updated in the database
      const updatedUser = await userRepository.findOne({
        where: { id: testUser.id },
      });
      expect(updatedUser?.xrplAddress).toBe(secondAddress);
    }, 30000);

    it('should reject XRPL address already linked to another user', async () => {
      const sharedAddress = 'rHb9CJAWyB4rj91VRWn96DkukG4bwdtyTh';

      // Create another user with the same address
      const otherUser = await userRepository.save({
        username: 'otheruser',
        email: '<EMAIL>',
        password: 'hashedpassword',
        role: UserRole.USER,
        xrplAddress: sharedAddress,
      });

      try {
        // Try to link the same address to the test user
        await request(app.getHttpServer())
          .post('/users/xrpl/link')
          .set('Authorization', `Bearer ${jwtToken}`)
          .send({ xrplAddress: sharedAddress })
          .expect(409); // Changed from 400 to 409 (Conflict) for duplicate addresses
      } finally {
        // Clean up the other user
        await userRepository.delete({ id: otherUser.id });
      }
    });
  });
});
