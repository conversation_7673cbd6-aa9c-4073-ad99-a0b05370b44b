// Layout styles for launchpad detail page
// Container, responsive design, and media queries

.detail-container {
  padding: 16px;
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;

  ion-spinner {
    margin-bottom: 16px;
  }

  p {
    color: var(--ion-color-medium);
    margin: 0;
    font-size: 16px;
  }
}

.sale-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding: 20px 0;

  .sale-basic-info {
    flex: 1;
    margin-right: 16px;

    .sale-symbol {
      font-size: 32px;
      font-weight: 700;
      color: var(--ion-color-primary);
      margin: 0 0 8px 0;
    }

    .sale-name {
      font-size: 20px;
      font-weight: 600;
      color: var(--ion-color-dark);
      margin: 0 0 12px 0;
    }

    .sale-description {
      font-size: 16px;
      color: var(--ion-color-medium);
      margin: 0;
      line-height: 1.5;
    }
  }

  .status-badge {
    font-size: 14px;
    font-weight: 600;
    padding: 8px 16px;
    border-radius: 20px;
    white-space: nowrap;

    ion-icon {
      margin-right: 6px;
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .detail-container {
    padding: 12px;
  }

  .sale-header {
    flex-direction: column;
    align-items: flex-start;

    .sale-basic-info {
      margin-right: 0;
      margin-bottom: 16px;
    }

    .sale-symbol {
      font-size: 28px;
    }

    .sale-name {
      font-size: 18px;
    }
  }

  .details-card .detail-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .progress-card .contribution-limits {
    flex-direction: column;
    gap: 12px;
  }

  .links-card .links-grid {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .transaction-blob-card
    .transaction-info
    .transaction-blob-section
    .blob-actions {
    flex-direction: column;
    gap: 8px;
  }
}

@media (max-width: 576px) {
  .sale-header {
    .sale-symbol {
      font-size: 24px;
    }

    .sale-name {
      font-size: 16px;
    }

    .sale-description {
      font-size: 14px;
    }
  }

  .progress-card .progress-stats {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;

    .percentage {
      align-self: center;
    }
  }
}
