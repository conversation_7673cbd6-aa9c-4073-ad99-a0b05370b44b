import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { XrplService } from './xrpl.service';
import { XrplModule } from './xrpl.module';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule } from '@nestjs/config';
import { User } from '../users/entities/user.entity';
import { UsersModule } from '../users/users.module';
import { AuthModule } from '../auth/auth.module';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { Client } from 'xrpl';
import {
  createMockXrplClient,
  createMockAccountInfo,
  createMockServerInfo,
  createMockAccountTransactions,
} from '../../test/xrpl-test-utils';

// Mock the xrpl module
jest.mock('xrpl', () => ({
  Client: jest.fn(),
  encode: jest.fn().mockReturnValue('mock_encoded_transaction_blob'),
}));

describe('XRPL Service Integration Tests', () => {
  let service: XrplService;
  let module: TestingModule;
  let mockClient: any;

  const mockConfigService = {
    get: jest.fn((key: string) => {
      const config: Record<string, string> = {
        XRPL_NETWORK: 'testnet',
        XRPL_SERVER: 'wss://s.altnet.rippletest.net:51233',
        XRPL_SEED: 'sEdTJMwj9Pe9EtJmGwU2ge5qZthA9',
        JWT_SECRET: 'test-secret-key-for-testing-only',
        JWT_EXPIRES_IN: '1h',
        REFRESH_TOKEN_EXPIRES_IN: '7d',
        'xrpl.testnetUrl': 'wss://s.altnet.rippletest.net:51233',
      };
      return config[key];
    }),
  };

  beforeAll(async () => {
    // Create mock client
    mockClient = createMockXrplClient();

    // Mock the Client constructor
    (Client as jest.MockedClass<typeof Client>).mockImplementation(
      () => mockClient,
    );

    module = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({
          isGlobal: true,
          load: [
            () => ({
              XRPL_NETWORK: 'testnet',
              XRPL_SERVER: 'wss://s.altnet.rippletest.net:51233',
              XRPL_SEED: 'sEdTJMwj9Pe9EtJmGwU2ge5qZthA9',
              JWT_SECRET: 'test-secret-key-for-testing-only',
              JWT_EXPIRES_IN: '1h',
              REFRESH_TOKEN_EXPIRES_IN: '7d',
              'xrpl.testnetUrl': 'wss://s.altnet.rippletest.net:51233',
            }),
          ],
        }),
        TypeOrmModule.forRoot({
          type: 'postgres',
          host: 'localhost',
          port: 5432,
          username: 'test',
          password: 'test',
          database: 'test_db',
          entities: [User],
          synchronize: true,
          logging: false,
        }),
        XrplModule,
        UsersModule,
        AuthModule,
        PassportModule,
        JwtModule.register({
          secret: 'test-secret-key-for-testing-only',
          signOptions: { expiresIn: '1h' },
        }),
      ],
      providers: [
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
      ],
    }).compile();

    service = module.get<XrplService>(XrplService);

    // Manually set the client and connection status for testing
    (service as any).client = mockClient;
    (service as any).connectionStatus.isConnected = true;
  });

  afterAll(async () => {
    await module?.close();
  });

  beforeEach(async () => {
    // Reset mocks before each test
    jest.clearAllMocks();

    // Ensure the service has the mock client
    (service as any).client = mockClient;
    (service as any).connectionStatus.isConnected = true;

    // Setup default mock responses
    mockClient.request.mockImplementation((command: any) => {
      if (command.command === 'account_info') {
        return Promise.resolve(createMockAccountInfo('**********', 123));
      }
      if (command.command === 'server_info') {
        return Promise.resolve(createMockServerInfo('1-1000'));
      }
      if (command.command === 'account_tx') {
        return Promise.resolve(createMockAccountTransactions());
      }
      if (command.command === 'ping') {
        return Promise.resolve({ result: {} });
      }
      return Promise.resolve({ result: {} });
    });

    mockClient.submitAndWait.mockResolvedValue({
      result: {
        engine_result: 'tesSUCCESS',
        tx_json: { hash: 'mock_transaction_hash' },
      },
    });

    mockClient.autofill.mockResolvedValue({
      tx_blob: 'mock_transaction_blob',
    });

    // Wait for service to be ready
    await new Promise((resolve) => setTimeout(resolve, 100));
  });

  describe('XRPL Connection Management', () => {
    it('should establish connection to XRPL network', () => {
      const isConnected = service.isConnected();
      expect(isConnected).toBe(true);
    });

    it('should handle connection status changes', () => {
      const status = service.getConnectionStatus();
      expect(status).toBeDefined();
      expect(status.isConnected).toBe(true);
    });

    it('should handle connection loss gracefully', () => {
      // Simulate connection loss by modifying the mock
      (service as any).connectionStatus.isConnected = false;

      // Should return false when disconnected
      expect(service.isConnected()).toBe(false);
    });
  });

  describe('Account Operations', () => {
    it('should get account balance', async () => {
      const address = 'rTestWalletAddress123456789';
      const balance = await service.getBalance(address);

      expect(balance).toBeDefined();
      expect(balance.xrp).toBe(1000); // ********** drops = 1000 XRP
      expect(balance.currency).toBe('XRP');
    });

    it('should get account info via request', async () => {
      const address = 'rTestWalletAddress123456789';
      const accountInfo = await service.request({
        command: 'account_info',
        account: address,
      });

      expect(accountInfo).toBeDefined();
      expect(accountInfo.result.account_data).toBeDefined();
      expect(accountInfo.result.account_data.Balance).toBe('**********');
      expect(accountInfo.result.account_data.Sequence).toBe(123);
    });

    it('should validate XRPL address format', async () => {
      const validAddress = 'rTestWalletAddress123456789';
      const invalidAddress = 'invalid-address';

      // Test with valid address
      const balance = await service.getBalance(validAddress);
      expect(balance.address).toBe(validAddress);

      // Test with invalid address - should throw error
      mockClient.request.mockRejectedValueOnce(new Error('Invalid address'));
      await expect(service.getBalance(invalidAddress)).rejects.toThrow();
    });
  });

  describe('Transaction Operations', () => {
    it('should prepare send transaction', async () => {
      const fromAddress = 'rTestSourceAddress123456789';
      const toAddress = 'rTestDestAddress123456789';
      const amount = 1.0; // 1 XRP

      // Mock the autofill method to return a proper transaction blob
      mockClient.autofill.mockResolvedValueOnce({
        TransactionType: 'Payment',
        Account: fromAddress,
        Destination: toAddress,
        Amount: '1000000', // 1 XRP in drops
        Fee: '10000',
        Sequence: 123,
        LastLedgerSequence: 1004,
        tx_blob: 'mock_transaction_blob_encoded',
      });

      const transaction = await service.prepareSendTransaction(
        fromAddress,
        toAddress,
        amount,
      );

      expect(transaction).toBeDefined();
      expect(transaction.account).toBe(fromAddress);
      expect(transaction.destination).toBe(toAddress);
      expect(transaction.amount).toBe(amount);
      expect(transaction.transactionBlob).toBeDefined();
      expect(transaction.fee).toBeDefined();
      expect(transaction.sequence).toBe(123);
      expect(transaction.lastLedgerSequence).toBe(1004);
      expect(transaction.message).toBeDefined();
    });

    it('should submit and wait for transaction', async () => {
      const transaction = {
        TransactionType: 'Payment',
        Account: 'rTestSourceAddress123456789',
        Destination: 'rTestDestAddress123456789',
        Amount: '1000000',
        Fee: '10000',
        Sequence: 123,
        LastLedgerSequence: 1000,
      };

      const result = await service.submitAndWait(transaction);

      expect(result).toBeDefined();
      expect(result.result.engine_result).toBe('tesSUCCESS');
      expect(result.result.tx_json.hash).toBe('mock_transaction_hash');
    });

    it('should handle transaction submission errors', async () => {
      // Mock a failed transaction
      mockClient.submitAndWait.mockRejectedValueOnce(
        new Error('Transaction failed'),
      );

      const transaction = {
        TransactionType: 'Payment',
        Account: 'rTestSourceAddress123456789',
        Destination: 'rTestDestAddress123456789',
        Amount: '1000000',
      };

      await expect(service.submitAndWait(transaction)).rejects.toThrow(
        'Transaction failed',
      );
    });
  });

  describe('Network Information', () => {
    it('should get server info via request', async () => {
      const serverInfo = await service.request({
        command: 'server_info',
      });

      expect(serverInfo).toBeDefined();
      expect(serverInfo.result.info).toBeDefined();
      expect(serverInfo.result.info.complete_ledgers).toBe('1-1000');
    });

    it('should get ledger index via request', async () => {
      const serverInfo = await service.request({
        command: 'server_info',
      });

      expect(serverInfo).toBeDefined();
    });
  });

  describe('Account History', () => {
    it('should get account transaction history', async () => {
      const address = 'rTestWalletAddress123456789';

      // Mock the account_tx response with proper structure
      mockClient.request.mockImplementationOnce((command: any) => {
        if (command.command === 'account_tx') {
          return Promise.resolve({
            result: {
              transactions: [
                {
                  hash: 'txHash123',
                  ledger_index: 12345,
                  tx_json: {
                    Account: 'rTestAddress123456789',
                    Destination: 'rDestAddress123456789',
                    Amount: '1000000',
                    Fee: '10000',
                    TransactionType: 'Payment',
                    date: *********, // Ripple epoch
                  },
                  meta: {
                    delivered_amount: '1000000',
                  },
                },
                {
                  hash: 'txHash456',
                  ledger_index: 12344,
                  tx_json: {
                    Account: 'rTestAddress123456789',
                    Destination: 'rDestAddress123456789',
                    Amount: '2000000',
                    Fee: '10000',
                    TransactionType: 'Payment',
                    date: *********,
                  },
                  meta: {
                    delivered_amount: '2000000',
                  },
                },
              ],
            },
          });
        }
        return Promise.resolve({ result: {} });
      });

      const history = await service.getTransactions(address, 5);

      expect(history).toBeDefined();
      expect(Array.isArray(history)).toBe(true);
      expect(history.length).toBe(2);
      expect(history[0].hash).toBe('txHash123');
      expect(history[0].account).toBe('rTestAddress123456789');
      expect(history[0].destination).toBe('rDestAddress123456789');
      expect(history[0].amount).toBe(1); // 1000000 drops = 1 XRP
      expect(history[0].currency).toBe('XRP');
      expect(history[0].type).toBe('Payment');
      expect(history[0].validated).toBe(true);
    });
  });

  describe('Error Handling', () => {
    it('should handle network timeouts gracefully', async () => {
      // Mock a timeout scenario
      mockClient.request.mockImplementationOnce(
        () =>
          new Promise((_, reject) =>
            setTimeout(() => reject(new Error('Request timeout')), 100),
          ),
      );

      await expect(service.getBalance('rTestAddress')).rejects.toThrow(
        'Failed to fetch account balance',
      );
    });

    it('should handle malformed responses', async () => {
      // Mock malformed response
      mockClient.request.mockResolvedValueOnce({
        result: null, // Malformed response
      });

      await expect(service.getBalance('rTestAddress')).rejects.toThrow();
    });
  });

  describe('Service Lifecycle', () => {
    it('should initialize properly', () => {
      expect(service).toBeDefined();
      expect(service.isConnected).toBeDefined();
      expect(service.getConnectionStatus).toBeDefined();
    });

    it('should cleanup resources on destroy', async () => {
      const destroySpy = jest.spyOn(service, 'onModuleDestroy');

      await service.onModuleDestroy();

      expect(destroySpy).toHaveBeenCalled();
    });
  });
});
