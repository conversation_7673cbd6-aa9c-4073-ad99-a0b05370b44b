# XRPL Launchpad API

A secure, production-grade NestJS backend for the XRPL Launchpad application, integrating with the XRPL JavaScript SDK.

## Features

- **Authentication & Authorization**: JWT-based auth with Passport, role-based access control
- **XRPL Integration**: WebSocket connection to XRPL Testnet, account management, payments
- **Launchpad Management**: Token sale creation, contribution tracking, allocation computation
- **Real-time Updates**: WebSocket-based real-time data push
- **Security**: Helmet headers, rate limiting, CORS, validation
- **Documentation**: Swagger/OpenAPI integration
- **Testing**: Unit, integration, and e2e test setup

## Tech Stack

- **Framework**: NestJS 11 with TypeScript strict mode
- **Database**: PostgreSQL
- **Authentication**: Passport with JWT (access + refresh tokens)
- **XRPL**: Official XRPL JavaScript SDK
- **Real-time**: WebSocket with Socket.io
- **Validation**: Class-validator with global validation pipe
- **Documentation**: Swagger/OpenAPI
- **Security**: Helmet, rate limiting, CORS

## Project Structure

```
src/
├── auth/           # Authentication module
├── users/          # User management
├── xrpl/           # XRPL integration
├── launchpad/      # Token sale management
├── config/         # Configuration management
├── common/         # Shared DTOs, guards, interceptors
├── app.controller.ts
├── app.module.ts
├── app.service.ts
└── main.ts
```

## Getting Started

### Prerequisites

- Node.js 22+
- npm or yarn
- PostgreSQL (for production)

### Installation

1. Clone the repository
2. Install dependencies:

   ```bash
   npm install
   ```

3. Copy environment file:

   ```bash
   cp .env.example .env
   ```

4. Configure environment variables in `.env`

### Development

```bash
# Start in development mode
npm run start:dev

# Build the project
npm run build

# Run tests
npm run test

# Run e2e tests
npm run test:e2e

# Lint and format
npm run lint
npm run format
```

### Environment Variables

```env
# Database
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=xrpl_launchpad
DATABASE_USERNAME=postgres
DATABASE_PASSWORD=password

# JWT
JWT_SECRET=your-super-secret-jwt-key
JWT_REFRESH_SECRET=your-super-secret-refresh-key
JWT_EXPIRES_IN=15m
JWT_REFRESH_EXPIRES_IN=7d

# XRPL
XRPL_WS_URL=wss://s.altnet.rippletest.net:51233
XRPL_ISSUER_SEED=your-testnet-issuer-seed

# App
PORT=3000
NODE_ENV=development
FRONTEND_URL=http://localhost:8100

# Rate Limiting
THROTTLE_TTL=60
THROTTLE_LIMIT=100
```

## XRPL Setup

### Network Configuration

The API supports both XRPL Testnet and Mainnet:

- **Testnet**: `wss://s.altnet.rippletest.net:51233` (default for development)
- **Mainnet**: `wss://xrplcluster.com` (production use)

### Account Management

- Users can link their XRPL addresses to their accounts
- The system validates XRPL address format using regex: `^r[1-9A-HJ-NP-Za-km-z]{25,34}$`
- Account balances and transaction history are fetched in real-time

### Payment Processing

- **Client-side Signing**: All transactions are prepared on the server but signed by the client
- **Security**: Private keys never leave the client, ensuring maximum security
- **Transaction Preparation**: Server prepares unsigned transactions with proper fees and sequence numbers
- **Validation**: Server validates transaction parameters before preparation

### WebSocket Connection Strategy

- **Automatic Reconnection**: Exponential backoff with jitter for network resilience
- **Connection Pooling**: Multiple WebSocket connections for high availability
- **Error Handling**: Graceful degradation when XRPL network is unavailable
- **Heartbeat**: Regular ping/pong to maintain connection health

## Real-time Features

### WebSocket Gateway

- **Real-time Updates**: Live updates for token sale status, contributions, and allocations
- **Room-based Broadcasting**: Efficient message distribution to relevant users
- **Authentication**: JWT-based WebSocket authentication
- **Event-driven Architecture**: Immediate updates for critical operations

### Supported Events

- `sale_created`: New token sale created
- `sale_updated`: Sale parameters modified
- `contribution_received`: New contribution added
- `sale_ended`: Sale completed or cancelled
- `allocation_computed`: Token allocation calculated

### Client Integration

```typescript
// Example client connection
const socket = io('http://localhost:3000', {
  auth: {
    token: 'your-jwt-token',
  },
});

// Listen for sale updates
socket.on('sale_updated', (data) => {
  console.log('Sale updated:', data);
});
```

## Architectural Decisions

### Why Client-side Signing?

1. **Security**: Private keys never leave the user's device
2. **Compliance**: Meets regulatory requirements for self-custody
3. **User Control**: Users maintain full control over their transactions
4. **Audit Trail**: Clear separation between preparation and execution

### WebSocket Reconnection Strategy

1. **Exponential Backoff**: Prevents overwhelming the server during outages
2. **Jitter**: Randomizes reconnection attempts to prevent thundering herd
3. **Max Retries**: Prevents infinite reconnection loops
4. **Graceful Degradation**: Falls back to polling when WebSocket unavailable

### Database Design Choices

1. **UUID Primary Keys**: Better distribution and security than auto-increment
2. **Soft Deletes**: Maintains referential integrity and audit trails
3. **Indexed Queries**: Optimized for common access patterns
4. **Transaction Support**: Ensures data consistency across operations

### API Design Principles

1. **RESTful Endpoints**: Consistent resource-based URL structure
2. **Standardized Responses**: Consistent error handling and success formats
3. **Input Validation**: Comprehensive validation using class-validator
4. **Rate Limiting**: Protection against abuse and DoS attacks

## API Documentation

Once the application is running, Swagger documentation is available at:

- **Swagger UI**: `http://localhost:3000/api/docs`
- **OpenAPI JSON**: `http://localhost:3000/api-json`

### API Endpoints

#### Authentication

- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `POST /api/auth/refresh` - Refresh access token

#### XRPL Integration

- `GET /api/xrpl/balance` - Get user's XRPL balance
- `GET /api/xrpl/transactions` - Get user's transaction history
- `POST /api/xrpl/send` - Prepare payment transaction

#### Launchpad Management

- `GET /api/launchpad/sales` - List all token sales
- `POST /api/launchpad/sales` - Create new token sale (Admin only)
- `POST /api/launchpad/contributions` - Contribute to a sale
- `GET /api/launchpad/sales/:id/allocation` - Get user's token allocation

## Architecture

The application follows NestJS best practices with:

- **Modular Design**: Clear separation of concerns with feature modules
- **Dependency Injection**: Proper use of NestJS DI container
- **Guards & Interceptors**: Authentication and request processing
- **DTOs & Validation**: Strong typing and input validation
- **Exception Filters**: Proper error handling and mapping

## Security Features

- **Authentication**: JWT-based with refresh token rotation
- **Authorization**: Role-based access control
- **Input Validation**: Global validation pipe with whitelist
- **Security Headers**: Helmet integration
- **Rate Limiting**: Configurable request throttling
- **CORS**: Proper cross-origin configuration

## Testing

The project includes comprehensive testing setup:

- **Unit Tests**: Jest-based unit testing
- **Integration Tests**: API endpoint testing
- **E2E Tests**: Full application flow testing
- **Coverage**: Code coverage reporting

## Contributing

1. Follow the existing code style
2. Write tests for new features
3. Ensure all tests pass
4. Update documentation as needed

## License

This project is part of the XRPL Launchpad.
