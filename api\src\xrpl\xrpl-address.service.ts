import { Injectable, Logger } from '@nestjs/common';
import { isValidClassicAddress } from 'xrpl';

@Injectable()
export class XrplAddressService {
  private readonly logger = new Logger(XrplAddressService.name);

  /**
   * Validates an XRPL address format
   * @param address - The XRPL address to validate
   * @returns true if the address is valid, false otherwise
   */
  validateAddress(address: string): boolean {
    try {
      return isValidClassicAddress(address);
    } catch (error) {
      this.logger.warn(`Address validation error: ${String(error)}`);
      return false;
    }
  }

  /**
   * Validates and normalizes an XRPL address
   * @param address - The XRPL address to validate and normalize
   * @returns The normalized address if valid, throws error if invalid
   */
  validateAndNormalizeAddress(address: string): string {
    if (!this.validateAddress(address)) {
      throw new Error('Invalid XRPL address format');
    }
    return address;
  }

  /**
   * Checks if an address is a valid XRPL testnet address
   * @param address - The XRPL address to check
   * @returns true if the address is valid for testnet
   */
  isValidTestnetAddress(address: string): boolean {
    // For testnet, we accept any valid classic address format
    // In production, we might want to add additional checks
    return this.validateAddress(address);
  }
}
