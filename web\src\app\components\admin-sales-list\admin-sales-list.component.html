<ion-card class="admin-sales-card">
  <ion-card-header>
    <ion-card-title>
      <ion-icon name="list-outline"></ion-icon>
      Sales Management
    </ion-card-title>
    <ion-button
      fill="clear"
      size="small"
      (click)="onScrollToFormClick()"
      aria-label="Scroll to create form"
    >
      <ion-icon name="add-outline" slot="start"></ion-icon>
      Create New
    </ion-button>
  </ion-card-header>
  <ion-card-content>
    @if (isLoading) {
      <div class="loading-state">
        <ion-skeleton-text animated></ion-skeleton-text>
        <ion-skeleton-text animated></ion-skeleton-text>
        <ion-skeleton-text animated></ion-skeleton-text>
      </div>
    } @else if (sales.length > 0) {
      <ion-list>
        @for (sale of sales; track sale.id) {
          <ion-item class="admin-sale-item">
            <ion-icon
              [name]="sale.status === 'active' ? 'play-circle' : 'time'"
              slot="start"
              [color]="getSaleStatusColor(sale.status)"
              aria-hidden="true"
            ></ion-icon>
            <ion-label>
              <h3>{{ sale.symbol }}</h3>
              <p>{{ sale.description }}</p>
              <p class="sale-details">
                <span class="sale-price">Price: {{ sale.price }} XRP</span>
                <span class="sale-dates">
                  {{ sale.start | date: 'short' }} -
                  {{ sale.end | date: 'short' }}
                </span>
              </p>
              <p class="sale-stats">
                <span>Soft Cap: {{ sale.softCap }} XRP</span>
                <span>Hard Cap: {{ sale.hardCap }} XRP</span>
              </p>
            </ion-label>
            <div slot="end" class="sale-actions">
              <ion-badge
                [color]="getSaleStatusColor(sale.status)"
                class="status-badge"
                color="white"
              >
                {{ sale.status | titlecase }}
              </ion-badge>
              <div class="action-buttons">
                <ion-button
                  fill="clear"
                  size="small"
                  (click)="onEditSaleClick(sale)"
                  aria-label="Edit sale"
                >
                  <ion-icon name="create-outline" aria-hidden="true"></ion-icon>
                </ion-button>
                @if (sale.website) {
                  <ion-button
                    fill="clear"
                    size="small"
                    (click)="onOpenLinkClick(sale.website)"
                    aria-label="Open website"
                  >
                    <ion-icon name="open-outline" aria-hidden="true"></ion-icon>
                  </ion-button>
                }
                <ion-button
                  fill="clear"
                  size="small"
                  color="danger"
                  (click)="onDeleteSaleClick(sale.id)"
                  aria-label="Delete sale"
                >
                  <ion-icon name="trash-outline" aria-hidden="true"></ion-icon>
                </ion-button>
              </div>
            </div>
          </ion-item>
        }
      </ion-list>
    } @else {
      <div class="empty-state">
        <ion-icon name="list-outline" size="large"></ion-icon>
        <p>No sales found</p>
        <ion-button fill="outline" size="small" (click)="onScrollToFormClick()">
          Create Your First Sale
        </ion-button>
      </div>
    }
  </ion-card-content>
</ion-card>
