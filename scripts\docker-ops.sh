#!/bin/bash

# XRPL Launchpad Docker Operations Script
# Bash script for common Docker operations

set -e

ACTION=${1:-help}

show_help() {
    cat << EOF
XRPL Launchpad Docker Operations Script

Usage: ./docker-ops.sh [Action]

Actions:
  up          - Start all services (docker-compose up -d)
  down        - Stop all services (docker-compose down)
  build       - Build and start all services (docker-compose up --build -d)
  logs        - Show logs for all services (docker-compose logs -f)
  restart     - Restart all services
  clean       - Stop and remove containers, networks, and images
  reset-db    - Stop services, remove database volume, and restart
  help        - Show this help message (default)

Examples:
  ./docker-ops.sh up
  ./docker-ops.sh build
  ./docker-ops.sh logs
EOF
}

start_services() {
    echo -e "\033[32mStarting XRPL Launchpad services...\033[0m"
    docker-compose up -d
    if [ $? -eq 0 ]; then
            echo -e "\033[32mServices started successfully!\033[0m"
    echo -e "\033[36mFrontend: http://localhost:8080\033[0m"
    echo -e "\033[36mBackend:  http://localhost:3000\033[0m"
    echo -e "\033[36mDatabase: localhost:5432\033[0m"
    else
        echo -e "\033[31mFailed to start services!\033[0m"
        exit 1
    fi
}

stop_services() {
    echo -e "\033[33mStopping XRPL Launchpad services...\033[0m"
    docker-compose down
    if [ $? -eq 0 ]; then
        echo -e "\033[32mServices stopped successfully!\033[0m"
    else
        echo -e "\033[31mFailed to stop services!\033[0m"
        exit 1
    fi
}

build_and_start_services() {
    echo -e "\033[32mBuilding and starting XRPL Launchpad services...\033[0m"
    docker-compose up --build -d
    if [ $? -eq 0 ]; then
            echo -e "\033[32mServices built and started successfully!\033[0m"
    echo -e "\033[36mFrontend: http://localhost:8080\033[0m"
    echo -e "\033[36mBackend:  http://localhost:3000\033[0m"
    echo -e "\033[36mDatabase: localhost:5432\033[0m"
    else
        echo -e "\033[31mFailed to build and start services!\033[0m"
        exit 1
    fi
}

show_logs() {
    echo -e "\033[32mShowing logs for all services (Press Ctrl+C to exit)...\033[0m"
    docker-compose logs -f
}

restart_services() {
    echo -e "\033[33mRestarting XRPL Launchpad services...\033[0m"
    docker-compose restart
    if [ $? -eq 0 ]; then
        echo -e "\033[32mServices restarted successfully!\033[0m"
    else
        echo -e "\033[31mFailed to restart services!\033[0m"
        exit 1
    fi
}

clean_all() {
    echo -e "\033[33mCleaning up Docker resources...\033[0m"
    echo -e "\033[31mThis will remove all containers, networks, and images!\033[0m"
    
    read -p "Are you sure? (y/N): " confirmation
    if [[ $confirmation =~ ^[Yy]$ ]]; then
        docker-compose down --rmi all --volumes --remove-orphans
        if [ $? -eq 0 ]; then
            echo -e "\033[32mCleanup completed successfully!\033[0m"
        else
            echo -e "\033[31mCleanup failed!\033[0m"
            exit 1
        fi
    else
        echo -e "\033[33mCleanup cancelled.\033[0m"
    fi
}

reset_database() {
    echo -e "\033[33mResetting database...\033[0m"
    echo -e "\033[31mThis will delete all database data!\033[0m"
    
    read -p "Are you sure? (y/N): " confirmation
    if [[ $confirmation =~ ^[Yy]$ ]]; then
        echo -e "\033[33mStopping services...\033[0m"
        docker-compose down
        
        echo -e "\033[33mRemoving database volume...\033[0m"
        docker volume rm xrpl_db_data 2>/dev/null || true
        
        echo -e "\033[32mStarting services...\033[0m"
        docker-compose up -d
        
        if [ $? -eq 0 ]; then
            echo -e "\033[32mDatabase reset completed successfully!\033[0m"
        else
            echo -e "\033[31mDatabase reset failed!\033[0m"
            exit 1
        fi
    else
        echo -e "\033[33mDatabase reset cancelled.\033[0m"
    fi
}

# Main execution
case $ACTION in
    "up")
        start_services
        ;;
    "down")
        stop_services
        ;;
    "build")
        build_and_start_services
        ;;
    "logs")
        show_logs
        ;;
    "restart")
        restart_services
        ;;
    "clean")
        clean_all
        ;;
    "reset-db")
        reset_database
        ;;
    "help"|*)
        show_help
        ;;
esac
