/// <reference types="cypress" />

describe('Launchpad Flow', () => {
  beforeEach(() => {
    // Clear localStorage and cookies before each test
    cy.clearLocalStorage();
    cy.clearCookies();

    // Mock authenticated user with XRPL address
    cy.window().then(win => {
      win.localStorage.setItem('access_token', 'mock-access-token');
      win.localStorage.setItem(
        'user',
        JSON.stringify({
          id: '1',
          username: 'testuser',
          email: '<EMAIL>',
          role: 'user',
          xrplAddress: 'rValidTestnetAddress123456789',
        })
      );
    });
  });

  describe('Launchpad Detail Page', () => {
    it('should display sale details correctly', () => {
      // Mock API responses
      cy.intercept('GET', '**/launchpad/sales/sale1', {
        statusCode: 200,
        body: {
          id: 'sale1',
          name: 'Test Token Sale',
          symbol: 'TEST',
          description: 'A test token sale for E2E testing',
          status: 'active',
          start: '2024-01-01T00:00:00Z',
          end: '2024-12-31T23:59:59Z',
          softCap: 1000,
          hardCap: 5000,
          pricePerToken: 0.1,
          totalSupply: 10000,
          collectionAddress: 'rCollectionAddress123456789',
          website: 'https://test.com',
          whitepaper: 'https://test.com/whitepaper',
          socialLinks: {
            twitter: 'https://twitter.com/test',
            telegram: 'https://t.me/test',
          },
        },
      }).as('saleRequest');

      cy.intercept('GET', '**/launchpad/sales/sale1/allocation', {
        statusCode: 200,
        body: {
          allocationAmount: 0,
          saleStatus: 'active',
          saleEnded: false,
          canClaim: false,
        },
      }).as('allocationRequest');

      cy.visit('/launchpad/sale1');
      cy.waitForIonicPage();

      // Wait for API requests
      cy.wait(['@saleRequest', '@allocationRequest']);

      // Check that sale details are displayed
      cy.get('ion-content').should('contain', 'Test Token Sale');
      cy.get('ion-content').should('contain', 'TEST');
      cy.get('ion-content').should(
        'contain',
        'A test token sale for E2E testing'
      );

      // Check that progress bar is visible
      cy.getIonProgressBar().should('be.visible');

      // Check that contribution form is visible
      cy.getIonInput('amount').should('be.visible');
      cy.getIonInput('destinationTag').should('be.visible');
      cy.getIonButton('submit').should('be.visible');
    });

    it('should handle contribution form validation', () => {
      // Mock API responses
      cy.intercept('GET', '**/launchpad/sales/sale1', {
        statusCode: 200,
        body: {
          id: 'sale1',
          name: 'Test Sale',
          symbol: 'TEST',
          description: 'Test description',
          status: 'active',
          start: '2024-01-01T00:00:00Z',
          end: '2024-12-31T23:59:59Z',
          softCap: 1000,
          hardCap: 5000,
          pricePerToken: 0.1,
          totalSupply: 10000,
          collectionAddress: 'rCollection123',
          website: 'https://test.com',
          whitepaper: 'https://test.com/whitepaper',
          socialLinks: {},
        },
      });

      cy.intercept('GET', '**/launchpad/sales/sale1/allocation', {
        statusCode: 200,
        body: {
          allocationAmount: 0,
          saleStatus: 'active',
          saleEnded: false,
          canClaim: false,
        },
      });

      cy.visit('/launchpad/sale1');
      cy.waitForIonicPage();

      // Try to submit empty form
      cy.getIonButton('submit').click();

      // Check that form validation prevents submission
      cy.getIonInput('amount').should('have.attr', 'aria-invalid', 'true');
      cy.getIonInput('destinationTag').should(
        'have.attr',
        'aria-invalid',
        'true'
      );
    });

    it('should handle successful contribution', () => {
      // Mock API responses
      cy.intercept('GET', '**/launchpad/sales/sale1', {
        statusCode: 200,
        body: {
          id: 'sale1',
          name: 'Test Sale',
          symbol: 'TEST',
          description: 'Test description',
          status: 'active',
          start: '2024-01-01T00:00:00Z',
          end: '2024-12-31T23:59:59Z',
          softCap: 1000,
          hardCap: 5000,
          pricePerToken: 0.1,
          totalSupply: 10000,
          collectionAddress: 'rCollection123',
          website: 'https://test.com',
          whitepaper: 'https://test.com/whitepaper',
          socialLinks: {},
        },
      });

      cy.intercept('GET', '**/launchpad/sales/sale1/allocation', {
        statusCode: 200,
        body: {
          allocationAmount: 0,
          saleStatus: 'active',
          saleEnded: false,
          canClaim: false,
        },
      });

      cy.intercept('POST', '**/xrpl/send', {
        statusCode: 200,
        body: {
          transactionBlob: 'mock-transaction-blob',
          fee: '0.000012',
          sequence: 123,
        },
      }).as('sendRequest');

      cy.visit('/launchpad/sale1');
      cy.waitForIonicPage();

      // Fill contribution form
      cy.getIonInput('amount').type('100');
      cy.getIonInput('destinationTag').should('have.value'); // Should be pre-filled
      cy.get('ion-checkbox').click(); // Accept terms

      // Submit form
      cy.getIonButton('submit').click();

      // Wait for send request
      cy.wait('@sendRequest');

      // Check that transaction blob is displayed
      cy.get('ion-content').should('contain', 'mock-transaction-blob');
    });

    it('should handle claim tokens for ended sale', () => {
      // Mock API responses for ended sale
      cy.intercept('GET', '**/launchpad/sales/sale1', {
        statusCode: 200,
        body: {
          id: 'sale1',
          name: 'Ended Sale',
          symbol: 'ENDED',
          description: 'An ended sale',
          status: 'ended',
          start: '2024-01-01T00:00:00Z',
          end: '2024-01-31T23:59:59Z',
          softCap: 1000,
          hardCap: 5000,
          pricePerToken: 0.1,
          totalSupply: 10000,
          collectionAddress: 'rCollection123',
          website: 'https://test.com',
          whitepaper: 'https://test.com/whitepaper',
          socialLinks: {},
        },
      });

      cy.intercept('GET', '**/launchpad/sales/sale1/allocation', {
        statusCode: 200,
        body: {
          allocationAmount: 100,
          saleStatus: 'ended',
          saleEnded: true,
          canClaim: true,
        },
      });

      cy.intercept('POST', '**/launchpad/sales/sale1/claim', {
        statusCode: 200,
        body: {
          success: true,
          message: 'Tokens claimed successfully',
        },
      }).as('claimRequest');

      cy.visit('/launchpad/sale1');
      cy.waitForIonicPage();

      // Check that claim button is visible and enabled
      cy.getIonButton().contains('Claim Tokens').should('be.visible');
      cy.getIonButton().contains('Claim Tokens').should('not.be.disabled');

      // Click claim button
      cy.getIonButton().contains('Claim Tokens').click();

      // Wait for claim request
      cy.wait('@claimRequest');

      // Check that success toast is shown
      cy.getIonToast().should('be.visible');
      cy.getIonToast().should('contain', 'Tokens claimed successfully');
    });

    it('should handle claim failure', () => {
      // Mock API responses
      cy.intercept('GET', '**/launchpad/sales/sale1', {
        statusCode: 200,
        body: {
          id: 'sale1',
          name: 'Ended Sale',
          symbol: 'ENDED',
          description: 'An ended sale',
          status: 'ended',
          start: '2024-01-01T00:00:00Z',
          end: '2024-01-31T23:59:59Z',
          softCap: 1000,
          hardCap: 5000,
          pricePerToken: 0.1,
          totalSupply: 10000,
          collectionAddress: 'rCollection123',
          website: 'https://test.com',
          whitepaper: 'https://test.com/whitepaper',
          socialLinks: {},
        },
      });

      cy.intercept('GET', '**/launchpad/sales/sale1/allocation', {
        statusCode: 200,
        body: {
          allocationAmount: 100,
          saleStatus: 'ended',
          saleEnded: true,
          canClaim: true,
        },
      });

      cy.intercept('POST', '**/launchpad/sales/sale1/claim', {
        statusCode: 400,
        body: {
          message: 'Trustline not set',
        },
      }).as('claimRequest');

      cy.visit('/launchpad/sale1');
      cy.waitForIonicPage();

      // Click claim button
      cy.getIonButton().contains('Claim Tokens').click();

      // Wait for claim request
      cy.wait('@claimRequest');

      // Check that error toast is shown
      cy.getIonToast().should('be.visible');
      cy.getIonToast().should('contain', 'Trustline not set');
    });

    it('should refresh data when refresh button is clicked', () => {
      // Mock API responses
      cy.intercept('GET', '**/launchpad/sales/sale1', {
        statusCode: 200,
        body: {
          id: 'sale1',
          name: 'Test Sale',
          symbol: 'TEST',
          description: 'Test description',
          status: 'active',
          start: '2024-01-01T00:00:00Z',
          end: '2024-12-31T23:59:59Z',
          softCap: 1000,
          hardCap: 5000,
          pricePerToken: 0.1,
          totalSupply: 10000,
          collectionAddress: 'rCollection123',
          website: 'https://test.com',
          whitepaper: 'https://test.com/whitepaper',
          socialLinks: {},
        },
      }).as('saleRequest');

      cy.intercept('GET', '**/launchpad/sales/sale1/allocation', {
        statusCode: 200,
        body: {
          allocationAmount: 0,
          saleStatus: 'active',
          saleEnded: false,
          canClaim: false,
        },
      }).as('allocationRequest');

      cy.visit('/launchpad/sale1');
      cy.waitForIonicPage();

      // Wait for initial requests
      cy.wait(['@saleRequest', '@allocationRequest']);

      // Click refresh button
      cy.getIonButton().contains('Refresh').click();

      // Wait for refresh requests
      cy.wait(['@saleRequest', '@allocationRequest']);
    });

    it('should open external links', () => {
      // Mock API responses
      cy.intercept('GET', '**/launchpad/sales/sale1', {
        statusCode: 200,
        body: {
          id: 'sale1',
          name: 'Test Sale',
          symbol: 'TEST',
          description: 'Test description',
          status: 'active',
          start: '2024-01-01T00:00:00Z',
          end: '2024-12-31T23:59:59Z',
          softCap: 1000,
          hardCap: 5000,
          pricePerToken: 0.1,
          totalSupply: 10000,
          collectionAddress: 'rCollection123',
          website: 'https://test.com',
          whitepaper: 'https://test.com/whitepaper',
          socialLinks: {
            twitter: 'https://twitter.com/test',
            telegram: 'https://t.me/test',
          },
        },
      });

      cy.intercept('GET', '**/launchpad/sales/sale1/allocation', {
        statusCode: 200,
        body: {
          allocationAmount: 0,
          saleStatus: 'active',
          saleEnded: false,
          canClaim: false,
        },
      });

      cy.visit('/launchpad/sale1');
      cy.waitForIonicPage();

      // Click on website link
      cy.get('a').contains('Website').click();

      // Check that external link opens (this would typically open in new tab)
      // In a real test, you might want to verify the URL or check for new window
    });

    it('should copy transaction blob to clipboard', () => {
      // Mock API responses
      cy.intercept('GET', '**/launchpad/sales/sale1', {
        statusCode: 200,
        body: {
          id: 'sale1',
          name: 'Test Sale',
          symbol: 'TEST',
          description: 'Test description',
          status: 'active',
          start: '2024-01-01T00:00:00Z',
          end: '2024-12-31T23:59:59Z',
          softCap: 1000,
          hardCap: 5000,
          pricePerToken: 0.1,
          totalSupply: 10000,
          collectionAddress: 'rCollection123',
          website: 'https://test.com',
          whitepaper: 'https://test.com/whitepaper',
          socialLinks: {},
        },
      });

      cy.intercept('GET', '**/launchpad/sales/sale1/allocation', {
        statusCode: 200,
        body: {
          allocationAmount: 0,
          saleStatus: 'active',
          saleEnded: false,
          canClaim: false,
        },
      });

      cy.intercept('POST', '**/xrpl/send', {
        statusCode: 200,
        body: {
          transactionBlob: 'mock-transaction-blob',
          fee: '0.000012',
          sequence: 123,
        },
      });

      cy.visit('/launchpad/sale1');
      cy.waitForIonicPage();

      // Fill and submit contribution form
      cy.getIonInput('amount').type('100');
      cy.get('ion-checkbox').click();
      cy.getIonButton('submit').click();

      // Click copy button
      cy.getIonButton().contains('Copy').click();

      // Check that clipboard API was called (this would be in console)
      // In a real test, you might want to verify the clipboard content
    });

    it('should open Xaman wallet', () => {
      // Mock API responses
      cy.intercept('GET', '**/launchpad/sales/sale1', {
        statusCode: 200,
        body: {
          id: 'sale1',
          name: 'Test Sale',
          symbol: 'TEST',
          description: 'Test description',
          status: 'active',
          start: '2024-01-01T00:00:00Z',
          end: '2024-12-31T23:59:59Z',
          softCap: 1000,
          hardCap: 5000,
          pricePerToken: 0.1,
          totalSupply: 10000,
          collectionAddress: 'rCollection123',
          website: 'https://test.com',
          whitepaper: 'https://test.com/whitepaper',
          socialLinks: {},
        },
      });

      cy.intercept('GET', '**/launchpad/sales/sale1/allocation', {
        statusCode: 200,
        body: {
          allocationAmount: 0,
          saleStatus: 'active',
          saleEnded: false,
          canClaim: false,
        },
      });

      cy.intercept('POST', '**/xrpl/send', {
        statusCode: 200,
        body: {
          transactionBlob: 'mock-transaction-blob',
          fee: '0.000012',
          sequence: 123,
        },
      });

      cy.visit('/launchpad/sale1');
      cy.waitForIonicPage();

      // Fill and submit contribution form
      cy.getIonInput('amount').type('100');
      cy.get('ion-checkbox').click();
      cy.getIonButton('submit').click();

      // Click Xaman button
      cy.getIonButton().contains('Open Xaman').click();

      // Check that Xaman URL scheme is opened (this would typically open external app)
      // In a real test, you might want to verify the URL or check for new window
    });
  });

  describe('End-to-End Flow', () => {
    it('should complete full contribution flow', () => {
      // Mock all necessary API responses
      cy.intercept('GET', '**/launchpad/sales/sale1', {
        statusCode: 200,
        body: {
          id: 'sale1',
          name: 'Test Sale',
          symbol: 'TEST',
          description: 'Test description',
          status: 'active',
          start: '2024-01-01T00:00:00Z',
          end: '2024-12-31T23:59:59Z',
          softCap: 1000,
          hardCap: 5000,
          pricePerToken: 0.1,
          totalSupply: 10000,
          collectionAddress: 'rCollection123',
          website: 'https://test.com',
          whitepaper: 'https://test.com/whitepaper',
          socialLinks: {},
        },
      });

      cy.intercept('GET', '**/launchpad/sales/sale1/allocation', {
        statusCode: 200,
        body: {
          allocationAmount: 0,
          saleStatus: 'active',
          saleEnded: false,
          canClaim: false,
        },
      });

      cy.intercept('POST', '**/xrpl/send', {
        statusCode: 200,
        body: {
          transactionBlob: 'mock-transaction-blob',
          fee: '0.000012',
          sequence: 123,
        },
      });

      // Navigate to sale detail
      cy.visit('/launchpad/sale1');
      cy.waitForIonicPage();

      // Fill contribution form
      cy.getIonInput('amount').type('100');
      cy.get('ion-checkbox').click(); // Accept terms

      // Submit form
      cy.getIonButton('submit').click();

      // Verify transaction blob is displayed
      cy.get('ion-content').should('contain', 'mock-transaction-blob');

      // Close transaction blob
      cy.getIonButton().contains('Close').click();

      // Verify form is reset or hidden
      cy.getIonInput('amount').should('have.value', '');
    });
  });
});
