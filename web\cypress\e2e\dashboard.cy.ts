/// <reference types="cypress" />

describe('Dashboard Flow', () => {
  beforeEach(() => {
    // Clear localStorage and cookies before each test
    cy.clearLocalStorage();
    cy.clearCookies();

    // Mock authenticated user with XRPL address
    cy.window().then(win => {
      win.localStorage.setItem('access_token', 'mock-access-token');
      win.localStorage.setItem(
        'user',
        JSON.stringify({
          id: '1',
          username: 'testuser',
          email: '<EMAIL>',
          role: 'user',
          xrplAddress: 'rValidTestnetAddress123456789',
        })
      );
    });
  });

  it('should display dashboard correctly', () => {
    // Mock API responses
    cy.intercept('GET', '**/xrpl/balance', {
      statusCode: 200,
      body: {
        balance: 1000.5,
        currency: 'XRP',
        address: 'rValidTestnetAddress123456789',
      },
    }).as('balanceRequest');

    cy.intercept('GET', '**/xrpl/transactions', {
      statusCode: 200,
      body: [
        {
          hash: 'tx1',
          amount: 100,
          date: '2024-01-01T00:00:00Z',
          type: 'payment',
          destination: 'rDestination123',
        },
        {
          hash: 'tx2',
          amount: 50,
          date: '2024-01-02T00:00:00Z',
          type: 'payment',
          destination: 'rDestination456',
        },
      ],
    }).as('transactionsRequest');

    cy.intercept('GET', '**/launchpad/sales/active', {
      statusCode: 200,
      body: [
        {
          id: 'sale1',
          name: 'Test Sale',
          symbol: 'TEST',
          description: 'Test description',
          status: 'active',
          start: '2024-01-01T00:00:00Z',
          end: '2024-12-31T23:59:59Z',
          softCap: 1000,
          hardCap: 5000,
          pricePerToken: 0.1,
          totalSupply: 10000,
          collectionAddress: 'rCollection123',
          website: 'https://test.com',
          whitepaper: 'https://test.com/whitepaper',
          socialLinks: {},
        },
      ],
    }).as('salesRequest');

    cy.visit('/dashboard');
    cy.waitForIonicPage();

    // Wait for API requests
    cy.wait(['@balanceRequest', '@transactionsRequest', '@salesRequest']);

    // Check that dashboard content is visible
    cy.get('ion-content').should('be.visible');

    // Check that balance is displayed
    cy.get('ion-content').should('contain', '1,000.5');
    cy.get('ion-content').should('contain', 'XRP');

    // Check that transactions are displayed
    cy.getIonList().should('be.visible');
    cy.getIonItem().should('have.length.at.least', 2);

    // Check that active sales are displayed
    cy.get('ion-content').should('contain', 'Test Sale');
    cy.get('ion-content').should('contain', 'TEST');
  });

  it('should handle API errors gracefully', () => {
    // Mock API errors
    cy.intercept('GET', '**/xrpl/balance', {
      statusCode: 500,
      body: { message: 'Server error' },
    });
    cy.intercept('GET', '**/xrpl/transactions', {
      statusCode: 500,
      body: { message: 'Server error' },
    });
    cy.intercept('GET', '**/launchpad/sales/active', {
      statusCode: 500,
      body: { message: 'Server error' },
    });

    cy.visit('/dashboard');
    cy.waitForIonicPage();

    // Check that dashboard still loads even with API errors
    cy.get('ion-content').should('be.visible');

    // Check that error toasts are shown
    cy.getIonToast().should('be.visible');
  });

  it('should refresh data when refresh button is clicked', () => {
    // Mock API responses
    cy.intercept('GET', '**/xrpl/balance', {
      statusCode: 200,
      body: { balance: 1000, currency: 'XRP', address: 'rTest123' },
    }).as('balanceRequest');

    cy.intercept('GET', '**/xrpl/transactions', {
      statusCode: 200,
      body: [],
    }).as('transactionsRequest');

    cy.intercept('GET', '**/launchpad/sales/active', {
      statusCode: 200,
      body: [],
    }).as('salesRequest');

    cy.visit('/dashboard');
    cy.waitForIonicPage();

    // Wait for initial requests
    cy.wait(['@balanceRequest', '@transactionsRequest', '@salesRequest']);

    // Click refresh button
    cy.getIonButton().contains('Refresh').click();

    // Wait for refresh requests
    cy.wait(['@balanceRequest', '@transactionsRequest', '@salesRequest']);
  });

  it('should navigate to sale detail when sale is clicked', () => {
    // Mock API responses
    cy.intercept('GET', '**/xrpl/balance', {
      statusCode: 200,
      body: { balance: 1000, currency: 'XRP' },
    });
    cy.intercept('GET', '**/xrpl/transactions', { statusCode: 200, body: [] });
    cy.intercept('GET', '**/launchpad/sales/active', {
      statusCode: 200,
      body: [
        {
          id: 'sale1',
          name: 'Test Sale',
          symbol: 'TEST',
          description: 'Test description',
          status: 'active',
          start: '2024-01-01T00:00:00Z',
          end: '2024-12-31T23:59:59Z',
          softCap: 1000,
          hardCap: 5000,
          pricePerToken: 0.1,
          totalSupply: 10000,
          collectionAddress: 'rCollection123',
          website: 'https://test.com',
          whitepaper: 'https://test.com/whitepaper',
          socialLinks: {},
        },
      ],
    });

    cy.visit('/dashboard');
    cy.waitForIonicPage();

    // Click on sale item
    cy.getIonItem().contains('Test Sale').click();

    // Check that user is redirected to sale detail
    cy.url().should('include', '/launchpad/sale1');
  });

  it('should navigate to launchpad list when view all sales is clicked', () => {
    // Mock API responses
    cy.intercept('GET', '**/xrpl/balance', {
      statusCode: 200,
      body: { balance: 1000, currency: 'XRP' },
    });
    cy.intercept('GET', '**/xrpl/transactions', { statusCode: 200, body: [] });
    cy.intercept('GET', '**/launchpad/sales/active', {
      statusCode: 200,
      body: [],
    });

    cy.visit('/dashboard');
    cy.waitForIonicPage();

    // Click on "View All Sales" button
    cy.getIonButton().contains('View All Sales').click();

    // Check that user is redirected to launchpad list
    cy.url().should('include', '/launchpad');
  });

  it('should navigate to transactions page when view all transactions is clicked', () => {
    // Mock API responses
    cy.intercept('GET', '**/xrpl/balance', {
      statusCode: 200,
      body: { balance: 1000, currency: 'XRP' },
    });
    cy.intercept('GET', '**/xrpl/transactions', { statusCode: 200, body: [] });
    cy.intercept('GET', '**/launchpad/sales/active', {
      statusCode: 200,
      body: [],
    });

    cy.visit('/dashboard');
    cy.waitForIonicPage();

    // Click on "View All Transactions" button
    cy.getIonButton().contains('View All Transactions').click();

    // This would typically navigate to a transactions page
    // For now, we'll just verify the button exists and is clickable
  });

  it('should show WebSocket connection status', () => {
    // Mock API responses
    cy.intercept('GET', '**/xrpl/balance', {
      statusCode: 200,
      body: { balance: 1000, currency: 'XRP' },
    });
    cy.intercept('GET', '**/xrpl/transactions', { statusCode: 200, body: [] });
    cy.intercept('GET', '**/launchpad/sales/active', {
      statusCode: 200,
      body: [],
    });

    cy.visit('/dashboard');
    cy.waitForIonicPage();

    // Check that WebSocket connection status is displayed
    cy.get('ion-content').should('contain', 'WebSocket');
  });

  it('should handle WebSocket debug functionality', () => {
    // Mock API responses
    cy.intercept('GET', '**/xrpl/balance', {
      statusCode: 200,
      body: { balance: 1000, currency: 'XRP' },
    });
    cy.intercept('GET', '**/xrpl/transactions', { statusCode: 200, body: [] });
    cy.intercept('GET', '**/launchpad/sales/active', {
      statusCode: 200,
      body: [],
    });

    cy.visit('/dashboard');
    cy.waitForIonicPage();

    // Click on WebSocket debug button
    cy.getIonButton().contains('Debug WebSocket').click();

    // Check that debug information is logged (this would be in console)
    // In a real test, you might want to check for specific debug output
  });

  it('should display empty state when no data is available', () => {
    // Mock empty API responses
    cy.intercept('GET', '**/xrpl/balance', {
      statusCode: 200,
      body: { balance: 0, currency: 'XRP' },
    });
    cy.intercept('GET', '**/xrpl/transactions', { statusCode: 200, body: [] });
    cy.intercept('GET', '**/launchpad/sales/active', {
      statusCode: 200,
      body: [],
    });

    cy.visit('/dashboard');
    cy.waitForIonicPage();

    // Check that empty states are displayed appropriately
    cy.get('ion-content').should('contain', '0');
    cy.getIonList().should('be.visible');
  });

  it('should show loading state while data is being fetched', () => {
    // Mock slow API responses
    cy.intercept('GET', '**/xrpl/balance', {
      delay: 2000,
      statusCode: 200,
      body: { balance: 1000, currency: 'XRP' },
    });
    cy.intercept('GET', '**/xrpl/transactions', {
      delay: 2000,
      statusCode: 200,
      body: [],
    });
    cy.intercept('GET', '**/launchpad/sales/active', {
      delay: 2000,
      statusCode: 200,
      body: [],
    });

    cy.visit('/dashboard');

    // Check that loading state is shown
    cy.getIonSpinner().should('be.visible');

    // Wait for loading to complete
    cy.waitForIonicPage();
  });
});
