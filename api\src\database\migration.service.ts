import { Injectable, Logger } from '@nestjs/common';
import { DataSource } from 'typeorm';

@Injectable()
export class MigrationService {
  private readonly logger = new Logger(MigrationService.name);

  constructor(private dataSource: DataSource) {}

  /**
   * Run all pending migrations
   */
  async runMigrations(): Promise<void> {
    try {
      this.logger.log('Running migrations...');
      const migrations = await this.dataSource.runMigrations();
      this.logger.log(`Successfully ran ${migrations.length} migrations`);
    } catch (error) {
      this.logger.error('Failed to run migrations:', error);
      throw error;
    }
  }

  /**
   * Revert the last migration
   */
  async revertLastMigration(): Promise<void> {
    try {
      this.logger.log('Reverting last migration...');
      await this.dataSource.undoLastMigration();
      this.logger.log('Successfully reverted last migration');
    } catch (error) {
      this.logger.error('Failed to revert migration:', error);
      throw error;
    }
  }

  /**
   * Show migration status
   */
  async showMigrations(): Promise<void> {
    try {
      const migrations = await this.dataSource.showMigrations();
      this.logger.log('Migration status:', migrations);
    } catch (error) {
      this.logger.error('Failed to show migrations:', error);
      throw error;
    }
  }

  /**
   * Check if migrations are pending
   */
  async hasUnappliedMigrations(): Promise<boolean> {
    try {
      return await this.dataSource.showMigrations();
    } catch (error) {
      this.logger.error('Failed to check migrations:', error);
      return false;
    }
  }
}
