import { DataSource } from 'typeorm';
import { config } from 'dotenv';

// Load environment variables
config();

async function runMigrations() {
  const dataSource = new DataSource({
    type: 'postgres',
    host: process.env.DATABASE_HOST || 'localhost',
    port: parseInt(process.env.DATABASE_PORT || '5432', 10),
    username: process.env.DATABASE_USERNAME || 'postgres',
    password: process.env.DATABASE_PASSWORD || 'password',
    database: process.env.DATABASE_NAME || 'xrpl_launchpad',
    entities: [__dirname + '/../src/**/*.entity{.ts,.js}'],
    migrations: [__dirname + '/../src/database/migrations/*{.ts,.js}'],
    synchronize: false,
    logging: true,
    ssl:
      process.env.NODE_ENV === 'production'
        ? { rejectUnauthorized: false }
        : false,
  });

  try {
    await dataSource.initialize();
    console.log('Database connection established');

    console.log('Running migrations...');
    const migrations = await dataSource.runMigrations();
    console.log(`Successfully ran ${migrations.length} migrations:`);

    migrations.forEach((migration) => {
      console.log(`- ${migration.name}`);
    });

    await dataSource.destroy();
    console.log('Database connection closed');
  } catch (error) {
    console.error('Migration failed:', error);
    process.exit(1);
  }
}

void runMigrations();
