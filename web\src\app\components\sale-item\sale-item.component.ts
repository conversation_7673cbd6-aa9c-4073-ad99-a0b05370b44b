import {
  Component,
  ChangeDetectionStrategy,
  Input,
  Output,
  EventEmitter,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';
import { Sale } from '../../types';

/**
 * Sale item component for displaying individual token sales.
 *
 * This component handles the display of a single sale with all its details
 * and provides actions for editing and deleting the sale.
 */
@Component({
  selector: 'app-sale-item',
  templateUrl: './sale-item.component.html',
  styleUrls: ['./sale-item.component.scss'],
  standalone: true,
  imports: [CommonModule, IonicModule],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SaleItemComponent {
  @Input() sale!: Sale;
  @Output() editSale = new EventEmitter<Sale>();
  @Output() deleteSale = new EventEmitter<string>();
  @Output() openLink = new EventEmitter<string>();

  formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString();
  }

  getStatusColor(status: string): string {
    switch (status) {
      case 'active':
        return 'success';
      case 'ended':
        return 'medium';
      case 'canceled':
        return 'danger';
      default:
        return 'medium';
    }
  }

  getStatusIcon(status: string): string {
    switch (status) {
      case 'active':
        return 'play-circle';
      case 'ended':
        return 'checkmark-circle';
      case 'canceled':
        return 'close-circle';
      default:
        return 'help-circle';
    }
  }

  onEditSale(): void {
    this.editSale.emit(this.sale);
  }

  onDeleteSale(): void {
    this.deleteSale.emit(this.sale.id);
  }

  onOpenLink(url: string): void {
    this.openLink.emit(url);
  }
}
