#!/bin/bash

# Bash script for Docker development operations

echo "XRPL Launchpad Docker Development Script"
echo "========================================"

# Function to show usage
show_usage() {
    echo "Usage: ./scripts/docker-dev.sh [command]"
    echo ""
    echo "Commands:"
    echo "  prod     - Run production build (API + Web + DB)"
    echo "  dev      - Run development build with live reload (API + Web-Dev + DB)"
    echo "  api      - Run only API and DB"
    echo "  web      - Run only Web and DB"
    echo "  db       - Run only database"
    echo "  stop     - Stop all services"
    echo "  clean    - Stop and remove all containers/volumes"
    echo "  logs     - Show logs for all services"
    echo "  status   - Show status of all services"
}

# Function to run production
start_production() {
    echo "Starting production environment..."
    docker-compose up -d
    echo "Production environment started!"
    echo "API: http://localhost:3000"
    echo "Web: http://localhost:80"
    echo "Web (alt): http://localhost:8100"
}

# Function to run development
start_development() {
    echo "Starting development environment..."
    docker-compose --profile dev up -d
    echo "Development environment started!"
    echo "API: http://localhost:3000"
    echo "Web Dev: http://localhost:8100"
}

# Function to run API only
start_api() {
    echo "Starting API and database..."
    docker-compose up -d db api
    echo "API and database started!"
    echo "API: http://localhost:3000"
}

# Function to run Web only
start_web() {
    echo "Starting Web and database..."
    docker-compose up -d db web
    echo "Web and database started!"
    echo "Web: http://localhost:80"
}

# Function to run DB only
start_db() {
    echo "Starting database only..."
    docker-compose up -d db
    echo "Database started!"
}

# Function to stop all services
stop_all() {
    echo "Stopping all services..."
    docker-compose down
    echo "All services stopped!"
}

# Function to clean everything
clean_all() {
    echo "Cleaning all containers and volumes..."
    docker-compose down -v --remove-orphans
    docker system prune -f
    echo "Cleanup completed!"
}

# Function to show logs
show_logs() {
    echo "Showing logs for all services..."
    docker-compose logs -f
}

# Function to show status
show_status() {
    echo "Service Status:"
    docker-compose ps
}

# Main script logic
COMMAND=${1:-""}

if [ -z "$COMMAND" ]; then
    show_usage
    exit 0
fi

case $COMMAND in
    "prod")
        start_production
        ;;
    "dev")
        start_development
        ;;
    "api")
        start_api
        ;;
    "web")
        start_web
        ;;
    "db")
        start_db
        ;;
    "stop")
        stop_all
        ;;
    "clean")
        clean_all
        ;;
    "logs")
        show_logs
        ;;
    "status")
        show_status
        ;;
    *)
        echo "Unknown command: $COMMAND"
        show_usage
        ;;
esac
