/// <reference types="cypress" />

describe('End-to-End Flow', () => {
  beforeEach(() => {
    // Clear localStorage and cookies before each test
    cy.clearLocalStorage();
    cy.clearCookies();
  });

  it('should complete full user journey: login → XRPL connect → dashboard → sale → contribute → claim', () => {
    // Step 1: Login
    cy.intercept('POST', '**/auth/login', {
      statusCode: 200,
      body: {
        accessToken: 'mock-access-token',
        refreshToken: 'mock-refresh-token',
        user: {
          id: '1',
          username: 'testuser',
          email: '<EMAIL>',
          role: 'user',
          xrplAddress: null, // User starts without XRPL address
        },
      },
    }).as('loginRequest');

    cy.visit('/login');
    cy.getIonInput('username').type('<EMAIL>');
    cy.getIonInput('password').type('password123');
    cy.getIonButton('submit').click();
    cy.wait('@loginRequest');

    // Step 2: XRPL Connect (should redirect automatically)
    cy.intercept('POST', '**/users/xrpl/link', {
      statusCode: 200,
      body: {
        success: true,
        message: 'XRPL address successfully linked',
      },
    }).as('linkRequest');

    cy.url().should('include', '/xrpl-connect');
    cy.getIonInput('xrplAddress').type('rValidTestnetAddress123456789');
    cy.getIonButton('submit').click();
    cy.wait('@linkRequest');

    // Step 3: Dashboard
    cy.intercept('GET', '**/xrpl/balance', {
      statusCode: 200,
      body: {
        balance: 1000.5,
        currency: 'XRP',
        address: 'rValidTestnetAddress123456789',
      },
    }).as('balanceRequest');

    cy.intercept('GET', '**/xrpl/transactions', {
      statusCode: 200,
      body: [
        {
          hash: 'tx1',
          amount: 100,
          date: '2024-01-01T00:00:00Z',
          type: 'payment',
          destination: 'rDestination123',
        },
      ],
    }).as('transactionsRequest');

    cy.intercept('GET', '**/launchpad/sales/active', {
      statusCode: 200,
      body: [
        {
          id: 'sale1',
          name: 'Test Token Sale',
          symbol: 'TEST',
          description: 'A test token sale',
          status: 'active',
          start: '2024-01-01T00:00:00Z',
          end: '2024-12-31T23:59:59Z',
          softCap: 1000,
          hardCap: 5000,
          pricePerToken: 0.1,
          totalSupply: 10000,
          collectionAddress: 'rCollection123',
          website: 'https://test.com',
          whitepaper: 'https://test.com/whitepaper',
          socialLinks: {},
        },
      ],
    }).as('salesRequest');

    cy.url().should('include', '/dashboard');
    cy.waitForIonicPage();
    cy.wait(['@balanceRequest', '@transactionsRequest', '@salesRequest']);

    // Verify dashboard content
    cy.get('ion-content').should('contain', '1,000.5');
    cy.get('ion-content').should('contain', 'Test Token Sale');

    // Step 4: Navigate to Sale Detail
    cy.intercept('GET', '**/launchpad/sales/sale1', {
      statusCode: 200,
      body: {
        id: 'sale1',
        name: 'Test Token Sale',
        symbol: 'TEST',
        description: 'A test token sale',
        status: 'active',
        start: '2024-01-01T00:00:00Z',
        end: '2024-12-31T23:59:59Z',
        softCap: 1000,
        hardCap: 5000,
        pricePerToken: 0.1,
        totalSupply: 10000,
        collectionAddress: 'rCollection123',
        website: 'https://test.com',
        whitepaper: 'https://test.com/whitepaper',
        socialLinks: {},
      },
    }).as('saleRequest');

    cy.intercept('GET', '**/launchpad/sales/sale1/allocation', {
      statusCode: 200,
      body: {
        allocationAmount: 0,
        saleStatus: 'active',
        saleEnded: false,
        canClaim: false,
      },
    }).as('allocationRequest');

    cy.getIonItem().contains('Test Token Sale').click();
    cy.url().should('include', '/launchpad/sale1');
    cy.waitForIonicPage();
    cy.wait(['@saleRequest', '@allocationRequest']);

    // Step 5: Contribute to Sale
    cy.intercept('POST', '**/xrpl/send', {
      statusCode: 200,
      body: {
        transactionBlob: 'mock-transaction-blob-for-contribution',
        fee: '0.000012',
        sequence: 123,
      },
    }).as('sendRequest');

    cy.getIonInput('amount').type('100');
    cy.get('ion-checkbox').click(); // Accept terms
    cy.getIonButton('submit').click();
    cy.wait('@sendRequest');

    // Verify transaction blob is displayed
    cy.get('ion-content').should(
      'contain',
      'mock-transaction-blob-for-contribution'
    );

    // Step 6: Close transaction blob and return to dashboard
    cy.getIonButton().contains('Close').click();
    cy.get('ion-back-button').click();
    cy.url().should('include', '/dashboard');

    // Step 7: Simulate sale ending and claim tokens
    // First, navigate back to sale detail
    cy.getIonItem().contains('Test Token Sale').click();
    cy.url().should('include', '/launchpad/sale1');

    // Mock ended sale with user allocation
    cy.intercept('GET', '**/launchpad/sales/sale1', {
      statusCode: 200,
      body: {
        id: 'sale1',
        name: 'Test Token Sale',
        symbol: 'TEST',
        description: 'A test token sale',
        status: 'ended',
        start: '2024-01-01T00:00:00Z',
        end: '2024-01-31T23:59:59Z',
        softCap: 1000,
        hardCap: 5000,
        pricePerToken: 0.1,
        totalSupply: 10000,
        collectionAddress: 'rCollection123',
        website: 'https://test.com',
        whitepaper: 'https://test.com/whitepaper',
        socialLinks: {},
      },
    }).as('endedSaleRequest');

    cy.intercept('GET', '**/launchpad/sales/sale1/allocation', {
      statusCode: 200,
      body: {
        allocationAmount: 1000,
        saleStatus: 'ended',
        saleEnded: true,
        canClaim: true,
      },
    }).as('endedAllocationRequest');

    cy.intercept('POST', '**/launchpad/sales/sale1/claim', {
      statusCode: 200,
      body: {
        success: true,
        message: 'Tokens claimed successfully',
      },
    }).as('claimRequest');

    // Refresh the page to get updated sale status
    cy.reload();
    cy.waitForIonicPage();
    cy.wait(['@endedSaleRequest', '@endedAllocationRequest']);

    // Step 8: Claim tokens
    cy.getIonButton().contains('Claim Tokens').should('be.visible');
    cy.getIonButton().contains('Claim Tokens').click();
    cy.wait('@claimRequest');

    // Verify success message
    cy.getIonToast().should('be.visible');
    cy.getIonToast().should('contain', 'Tokens claimed successfully');

    // Step 9: Logout
    cy.get('ion-menu-button').click();
    cy.getIonItem().contains('Logout').click();
    cy.url().should('include', '/login');
  });

  it('should handle error scenarios gracefully', () => {
    // Test login with invalid credentials
    cy.intercept('POST', '**/auth/login', {
      statusCode: 401,
      body: { message: 'Invalid credentials' },
    }).as('loginRequest');

    cy.visit('/login');
    cy.getIonInput('username').type('<EMAIL>');
    cy.getIonInput('password').type('wrongpassword');
    cy.getIonButton('submit').click();
    cy.wait('@loginRequest');

    // Verify error handling
    cy.getIonToast().should('be.visible');
    cy.getIonToast().should('contain', 'Invalid credentials');
    cy.url().should('include', '/login');

    // Test XRPL address linking with invalid address
    cy.window().then(win => {
      win.localStorage.setItem('access_token', 'mock-token');
      win.localStorage.setItem(
        'user',
        JSON.stringify({
          id: '1',
          username: 'testuser',
          email: '<EMAIL>',
          role: 'user',
          xrplAddress: null,
        })
      );
    });

    cy.intercept('POST', '**/users/xrpl/link', {
      statusCode: 400,
      body: { message: 'Invalid XRPL address format' },
    }).as('linkRequest');

    cy.visit('/xrpl-connect');
    cy.getIonInput('xrplAddress').type('invalid-address');
    cy.getIonButton('submit').click();
    cy.wait('@linkRequest');

    // Verify error handling
    cy.getIonToast().should('be.visible');
    cy.getIonToast().should('contain', 'Invalid XRPL address format');
  });

  it('should handle network connectivity issues', () => {
    // Test offline scenario
    cy.intercept('POST', '**/auth/login', {
      forceNetworkError: true,
    }).as('loginRequest');

    cy.visit('/login');
    cy.getIonInput('username').type('<EMAIL>');
    cy.getIonInput('password').type('password123');
    cy.getIonButton('submit').click();
    cy.wait('@loginRequest');

    // Verify network error handling
    cy.getIonToast().should('be.visible');
    cy.getIonToast().should('contain', 'Server error');
    cy.url().should('include', '/login');
  });

  it('should maintain user session across page refreshes', () => {
    // Login first
    cy.intercept('POST', '**/auth/login', {
      statusCode: 200,
      body: {
        accessToken: 'mock-access-token',
        refreshToken: 'mock-refresh-token',
        user: {
          id: '1',
          username: 'testuser',
          email: '<EMAIL>',
          role: 'user',
          xrplAddress: 'rValidTestnetAddress123456789',
        },
      },
    }).as('loginRequest');

    cy.visit('/login');
    cy.getIonInput('username').type('<EMAIL>');
    cy.getIonInput('password').type('password123');
    cy.getIonButton('submit').click();
    cy.wait('@loginRequest');

    // Mock dashboard data
    cy.intercept('GET', '**/xrpl/balance', {
      statusCode: 200,
      body: { balance: 1000, currency: 'XRP' },
    });
    cy.intercept('GET', '**/xrpl/transactions', {
      statusCode: 200,
      body: [],
    });
    cy.intercept('GET', '**/launchpad/sales/active', {
      statusCode: 200,
      body: [],
    });

    // Verify user is on dashboard
    cy.url().should('include', '/dashboard');
    cy.waitForIonicPage();

    // Refresh the page
    cy.reload();
    cy.waitForIonicPage();

    // Verify user is still authenticated and on dashboard
    cy.url().should('include', '/dashboard');
    cy.get('ion-content').should('be.visible');
  });
});
