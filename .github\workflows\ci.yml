name: CI-Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

env:
  NODE_VERSION: '20'

jobs:
  # API Backend CI
  api-ci:
    name: API Backend
    runs-on: ubuntu-latest

    services:
      postgres:
        image: postgres:17
        env:
          POSTGRES_USER: test
          POSTGRES_PASSWORD: test
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: package-lock.json

      - name: Install API dependencies
        working-directory: api
        run: npm ci --ignore-scripts

      - name: Run API linting
        working-directory: api
        run: npm run lint

      - name: Run API type checking
        working-directory: api
        run: npx tsc --noEmit

      - name: Run API unit tests with coverage
        working-directory: api
        env:
          DATABASE_HOST: localhost
          DATABASE_PORT: 5432
          DATABASE_NAME: test_db
          DATABASE_USERNAME: test
          DATABASE_PASSWORD: test
          NODE_ENV: test
          JWT_SECRET: test-secret-key-for-testing-only
          JWT_EXPIRES_IN: 1h
          REFRESH_TOKEN_EXPIRES_IN: 7d
          PORT: 3001
          LOG_LEVEL: error
        run: npm run test:cov

      - name: Run XRPL integration tests
        working-directory: api
        env:
          DATABASE_HOST: localhost
          DATABASE_PORT: 5432
          DATABASE_NAME: test_db
          DATABASE_USERNAME: test
          DATABASE_PASSWORD: test
          NODE_ENV: test
          JWT_SECRET: test-secret-key-for-testing-only
          JWT_EXPIRES_IN: 1h
          REFRESH_TOKEN_EXPIRES_IN: 7d
          PORT: 3001
          LOG_LEVEL: error
          XRPL_NETWORK: testnet
          XRPL_SERVER: wss://s.altnet.rippletest.net:51233
        run: npm run test:integration

      - name: Build API
        working-directory: api
        run: npm run build

      - name: Upload coverage reports
        uses: codecov/codecov-action@v4
        with:
          file: ./api/coverage/lcov.info
          flags: api
          name: api-coverage
          fail_ci_if_error: false

  # Web Frontend CI
  web-ci:
    name: Web Frontend
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: package-lock.json

      - name: Install Web dependencies
        working-directory: web
        run: npm ci --ignore-scripts

      - name: Run Web linting
        working-directory: web
        run: npm run lint

      - name: Run Web type checking
        working-directory: web
        run: npx tsc --noEmit

      - name: Run Web tests
        working-directory: web
        run: npm run test:ci

      - name: Build Web
        working-directory: web
        run: npm run build

      - name: Upload web coverage reports
        uses: codecov/codecov-action@v4
        with:
          file: ./web/coverage/app/lcov.info
          flags: web
          name: web-coverage
          fail_ci_if_error: false

  # Monorepo checks
  monorepo-checks:
    name: Monorepo Checks
    runs-on: ubuntu-latest
    needs: [api-ci, web-ci]

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: package-lock.json

      - name: Install root dependencies
        run: npm ci --ignore-scripts

      - name: Run root linting
        run: npm run lint:check

      - name: Run root formatting check
        run: npm run format:check

  # Security and dependency checks
  security-checks:
    name: Security Checks
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: package-lock.json

      - name: Install dependencies
        run: npm ci --ignore-scripts

      - name: Run security audit
        run: npm audit --audit-level=moderate

      - name: Check for outdated dependencies
        run: npm outdated || true

  # XRPL Integration Testing
  xrpl-integration:
    name: XRPL Integration Tests
    runs-on: ubuntu-latest
    needs: [api-ci, web-ci]
    if: github.event_name == 'push' && (github.ref == 'refs/heads/main' || github.ref == 'refs/heads/develop')

    services:
      postgres:
        image: postgres:17
        env:
          POSTGRES_USER: test
          POSTGRES_PASSWORD: test
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: package-lock.json

      - name: Install API dependencies
        working-directory: api
        run: npm ci --ignore-scripts

      - name: Run XRPL service integration tests
        working-directory: api
        env:
          DATABASE_HOST: localhost
          DATABASE_PORT: 5432
          DATABASE_NAME: test_db
          DATABASE_USERNAME: test
          DATABASE_PASSWORD: test
          NODE_ENV: test
          JWT_SECRET: test-secret-key-for-testing-only
          JWT_EXPIRES_IN: 1h
          REFRESH_TOKEN_EXPIRES_IN: 7d
          PORT: 3001
          LOG_LEVEL: error
          XRPL_NETWORK: testnet
          XRPL_SERVER: wss://s.altnet.rippletest.net:51233
        run: npm run test:xrpl-integration

      - name: Run Launchpad XRPL integration tests
        working-directory: api
        env:
          DATABASE_HOST: localhost
          DATABASE_PORT: 5432
          DATABASE_NAME: test_db
          DATABASE_USERNAME: test
          DATABASE_PASSWORD: test
          NODE_ENV: test
          JWT_SECRET: test-secret-key-for-testing-only
          JWT_EXPIRES_IN: 1h
          REFRESH_TOKEN_EXPIRES_IN: 7d
          PORT: 3001
          LOG_LEVEL: error
          XRPL_NETWORK: testnet
          XRPL_SERVER: wss://s.altnet.rippletest.net:51233
        run: npm run test:launchpad-integration

  # Build and test everything together
  integration:
    name: Full Integration Tests
    runs-on: ubuntu-latest
    needs: [api-ci, web-ci, monorepo-checks, xrpl-integration]
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'

    services:
      postgres:
        image: postgres:17
        env:
          POSTGRES_USER: test
          POSTGRES_PASSWORD: test
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: package-lock.json

      - name: Install all dependencies
        run: npm ci --ignore-scripts

      - name: Run full build
        run: npm run build

      - name: Run full test suite
        run: npm run test

      - name: Run e2e tests
        working-directory: api
        env:
          DATABASE_HOST: localhost
          DATABASE_PORT: 5432
          DATABASE_NAME: test_db
          DATABASE_USERNAME: test
          DATABASE_PASSWORD: test
          NODE_ENV: test
          JWT_SECRET: test-secret-key-for-testing-only
          JWT_EXPIRES_IN: 1h
          REFRESH_TOKEN_EXPIRES_IN: 7d
          PORT: 3001
          LOG_LEVEL: error
        run: npm run test:e2e

      - name: Generate combined coverage report
        run: |
          echo "Combining coverage reports..."
          npm run test:cov:combined

      - name: Upload combined coverage
        uses: codecov/codecov-action@v4
        with:
          file: ./coverage-combined/lcov.info
          flags: combined
          name: combined-coverage
          fail_ci_if_error: false
