import { IsString, IsBoolean, IsOptional } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class ClaimResponseDto {
  @ApiProperty({
    description: 'Whether the token claim was successful',
    example: true,
  })
  @IsBoolean()
  success!: boolean;

  @ApiProperty({
    description: 'Human-readable message about the claim result',
    example: 'Token<PERSON> claimed successfully',
  })
  @IsString()
  message!: string;

  @ApiProperty({
    description: 'XRPL transaction hash of the token transfer (if successful)',
    example: '1234567890ABCDEF1234567890ABCDEF1234567890ABCDEF1234567890ABCDEF',
    required: false,
  })
  @IsString()
  @IsOptional()
  transactionHash?: string;
}
