import {
  Injectable,
  BadRequestException,
  NotFoundException,
  Logger,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, LessThan, MoreThan } from 'typeorm';
import { <PERSON><PERSON>, CronExpression } from '@nestjs/schedule';
import { Sale, SaleStatus } from './entities/sale.entity';
import { Contribution } from './entities/contribution.entity';
import { CreateSaleDto } from './dto/create-sale.dto';
import { UpdateSaleDto } from './dto/update-sale.dto';
import { CreateContributionDto } from './dto/create-contribution.dto';
import { UserAllocationDto } from './dto/allocation.dto';
import { ClaimResponseDto } from './dto/claim-response.dto';
import { SaleContributionEvent } from '../xrpl/xrpl.service';
import { UsersService } from '../users/users.service';
import { XrplService } from '../xrpl/xrpl.service';

@Injectable()
export class LaunchpadService {
  private readonly logger = new Logger(LaunchpadService.name);

  constructor(
    @InjectRepository(Sale)
    private saleRepository: Repository<Sale>,
    @InjectRepository(Contribution)
    private contributionRepository: Repository<Contribution>,
    private readonly usersService: UsersService,
    private readonly xrplService: XrplService,
  ) {}

  async createSale(createSaleDto: CreateSaleDto): Promise<Sale> {
    // Validate schedule
    const startDate = new Date(createSaleDto.start);
    const endDate = new Date(createSaleDto.end);
    const now = new Date();

    if (startDate <= now) {
      throw new BadRequestException('Start date must be in the future');
    }

    if (endDate <= startDate) {
      throw new BadRequestException('End date must be after start date');
    }

    // Validate caps
    if (createSaleDto.softCap <= 0 || createSaleDto.hardCap <= 0) {
      throw new BadRequestException('Caps must be greater than 0');
    }

    if (createSaleDto.softCap >= createSaleDto.hardCap) {
      throw new BadRequestException('Soft cap must be less than hard cap');
    }

    // Validate caps
    if (createSaleDto.softCap >= createSaleDto.hardCap) {
      throw new BadRequestException('Soft cap must be less than hard cap');
    }

    const sale = this.saleRepository.create({
      ...createSaleDto,
      start: startDate,
      end: endDate,
      status: SaleStatus.ACTIVE,
    });

    const savedSale = await this.saleRepository.save(sale);

    this.logger.log(`Created sale: ${savedSale.symbol} (ID: ${savedSale.id})`);

    return savedSale;
  }

  async findAllSales(status?: SaleStatus): Promise<Sale[]> {
    const whereClause = status ? { status } : {};

    return this.saleRepository.find({
      where: whereClause,
      relations: ['contributions'],
      order: { createdAt: 'DESC' },
    });
  }

  async findActiveSales(): Promise<Sale[]> {
    const now = new Date();
    return this.saleRepository.find({
      where: {
        status: SaleStatus.ACTIVE,
        start: LessThan(now),
        end: MoreThan(now),
      },
      relations: ['contributions'],
    });
  }

  async findSaleById(id: string): Promise<Sale> {
    const sale = await this.saleRepository.findOne({
      where: { id },
      relations: ['contributions'],
    });

    if (!sale) {
      throw new NotFoundException(`Sale with ID ${id} not found`);
    }

    return sale;
  }

  async updateSale(id: string, updateSaleDto: UpdateSaleDto): Promise<Sale> {
    const sale = await this.findSaleById(id);

    // Prevent updates to ended or canceled sales
    if (
      sale.status === SaleStatus.ENDED ||
      sale.status === SaleStatus.CANCELED
    ) {
      throw new BadRequestException(`Cannot update ${sale.status} sale`);
    }

    // Validate schedule changes if dates are being updated
    if (updateSaleDto.start || updateSaleDto.end) {
      const startDate = updateSaleDto.start
        ? new Date(updateSaleDto.start)
        : sale.start;
      const endDate = updateSaleDto.end
        ? new Date(updateSaleDto.end)
        : sale.end;

      if (endDate <= startDate) {
        throw new BadRequestException('End date must be after start date');
      }

      // Check if new start date is in the past
      if (startDate <= new Date()) {
        throw new BadRequestException('Start date must be in the future');
      }
    }

    // Validate cap changes if caps are being updated
    if (updateSaleDto.softCap || updateSaleDto.hardCap) {
      const softCap = updateSaleDto.softCap ?? sale.softCap;
      const hardCap = updateSaleDto.hardCap ?? sale.hardCap;

      if (softCap >= hardCap) {
        throw new BadRequestException('Soft cap must be less than hard cap');
      }
    }

    Object.assign(sale, updateSaleDto);

    // Convert string dates to Date objects if they exist
    if (updateSaleDto.start) {
      sale.start = new Date(updateSaleDto.start);
    }
    if (updateSaleDto.end) {
      sale.end = new Date(updateSaleDto.end);
    }

    const updatedSale = await this.saleRepository.save(sale);

    this.logger.log(
      `Updated sale: ${updatedSale.symbol} (ID: ${updatedSale.id})`,
    );

    return updatedSale;
  }

  async deleteSale(id: string): Promise<void> {
    const sale = await this.findSaleById(id);

    if (sale.status === SaleStatus.ACTIVE) {
      throw new BadRequestException('Cannot delete active sale');
    }

    await this.saleRepository.remove(sale);

    this.logger.log(`Deleted sale: ${sale.symbol} (ID: ${sale.id})`);
  }

  async createContribution(
    userId: string,
    createContributionDto: CreateContributionDto,
  ): Promise<Contribution> {
    if (createContributionDto.amount <= 0.000001) {
      throw new BadRequestException('Amount must be greater than 0.000001');
    }

    const sale = await this.findSaleById(createContributionDto.saleId);

    if (sale.status !== SaleStatus.ACTIVE) {
      throw new BadRequestException('Sale is not active');
    }

    const now = new Date();
    if (now < sale.start || now > sale.end) {
      throw new BadRequestException('Sale is not open for contributions');
    }

    // Check if user has already contributed to this sale
    const existingContribution = await this.contributionRepository.findOne({
      where: { userId, saleId: createContributionDto.saleId },
    });

    if (existingContribution) {
      throw new BadRequestException(
        'User has already contributed to this sale',
      );
    }

    const contribution = this.contributionRepository.create({
      ...createContributionDto,
      userId,
      status: 'pending',
    });

    const savedContribution =
      await this.contributionRepository.save(contribution);

    this.logger.log(
      `Created contribution: ${savedContribution.amount} for sale ${sale.symbol} by user ${userId}`,
    );

    return savedContribution;
  }

  async findUserContributions(userId: string): Promise<Contribution[]> {
    return this.contributionRepository.find({
      where: { userId },
      relations: ['sale'],
      order: { createdAt: 'DESC' },
    });
  }

  async findSaleContributions(saleId: string): Promise<Contribution[]> {
    return this.contributionRepository.find({
      where: { saleId },
      relations: ['user'],
      order: { createdAt: 'DESC' },
    });
  }

  async getSaleStats(saleId: string): Promise<{
    totalContributions: number;
    totalAmount: number;
    contributorCount: number;
  }> {
    const contributions = await this.contributionRepository.find({
      where: { saleId },
    });

    if (!contributions) {
      return {
        totalContributions: 0,
        totalAmount: 0,
        contributorCount: 0,
      };
    }

    const totalAmount = contributions.reduce(
      (sum, c) => sum + Number(c.amount),
      0,
    );
    const contributorCount = new Set(contributions.map((c) => c.userId)).size;

    return {
      totalContributions: contributions.length,
      totalAmount,
      contributorCount,
    };
  }

  async endSale(id: string): Promise<Sale> {
    const sale = await this.findSaleById(id);

    if (sale.status !== SaleStatus.ACTIVE) {
      throw new BadRequestException('Sale is not active');
    }

    sale.status = SaleStatus.ENDED;
    const endedSale = await this.saleRepository.save(sale);

    this.logger.log(
      `Manually ended sale: ${endedSale.symbol} (ID: ${endedSale.id})`,
    );

    return endedSale;
  }

  async cancelSale(id: string): Promise<Sale> {
    const sale = await this.findSaleById(id);

    if (sale.status !== SaleStatus.ACTIVE) {
      throw new BadRequestException('Sale is not active');
    }

    sale.status = SaleStatus.CANCELED;
    const canceledSale = await this.saleRepository.save(sale);

    this.logger.log(
      `Canceled sale: ${canceledSale.symbol} (ID: ${canceledSale.id})`,
    );

    return canceledSale;
  }

  async processContributionEvent(event: SaleContributionEvent): Promise<void> {
    const sale = await this.findSaleById(event.saleId);

    if (sale.status !== SaleStatus.ACTIVE) {
      this.logger.warn(
        `Attempted to process contribution event for non-active sale: ${sale.symbol} (ID: ${sale.id})`,
      );
      return;
    }

    const now = new Date();
    if (now < sale.start || now > sale.end) {
      this.logger.warn(
        `Attempted to process contribution event outside sale time: ${sale.symbol} (ID: ${sale.id})`,
      );
      return;
    }

    // Find the user by XRPL address
    const user = await this.usersService.findByXrplAddress(event.sender);

    if (!user) {
      this.logger.warn(
        `User with XRPL address ${event.sender} not found. Cannot create contribution.`,
      );
      return;
    }

    const contribution = this.contributionRepository.create({
      userId: user.id,
      saleId: event.saleId,
      amount: event.amount,
      txHash: event.transactionHash,
      status: 'pending_verification',
    });

    await this.contributionRepository.save(contribution);

    this.logger.log(
      `Processed contribution event: ${event.amount} XRP for sale ${sale.symbol} from address ${event.sender} (ID: ${sale.id})`,
    );
  }

  // Auto-end expired sales every minute
  @Cron(CronExpression.EVERY_MINUTE)
  async autoEndExpiredSales(): Promise<void> {
    try {
      const now = new Date();
      const expiredSales = await this.saleRepository.find({
        where: {
          status: SaleStatus.ACTIVE,
          end: LessThan(now),
        },
      });

      for (const sale of expiredSales) {
        sale.status = SaleStatus.ENDED;
        await this.saleRepository.save(sale);

        this.logger.log(
          `Auto-ended expired sale: ${sale.symbol} (ID: ${sale.id})`,
        );
      }

      if (expiredSales.length > 0) {
        this.logger.log(`Auto-ended ${expiredSales.length} expired sales`);
      }
    } catch (error) {
      this.logger.error('Error in auto-ending expired sales:', error);
    }
  }

  // Auto-start sales that have reached their start time
  @Cron(CronExpression.EVERY_MINUTE)
  async autoStartSales(): Promise<void> {
    try {
      const now = new Date();
      const salesToStart = await this.saleRepository.find({
        where: {
          status: SaleStatus.ACTIVE,
          start: LessThan(now),
        },
      });

      for (const sale of salesToStart) {
        // Only update if the sale hasn't been started yet
        if (sale.start <= now && sale.end > now) {
          this.logger.log(`Sale started: ${sale.symbol} (ID: ${sale.id})`);
        }
      }
    } catch (error) {
      this.logger.error('Error in auto-starting sales:', error);
    }
  }

  async getUserAllocation(
    userId: string,
    saleId: string,
  ): Promise<UserAllocationDto> {
    const sale = await this.findSaleById(saleId);

    if (sale.status !== SaleStatus.ENDED) {
      throw new BadRequestException('Sale has not ended yet');
    }

    // Find user's contribution to this sale
    const contribution = await this.contributionRepository.findOne({
      where: { userId, saleId },
    });

    if (!contribution) {
      throw new BadRequestException('User has not contributed to this sale');
    }

    // Calculate total raised from all contributions
    const totalRaised = sale.contributions.reduce(
      (sum, contrib) => sum + Number(contrib.amount),
      0,
    );

    // Check if sale met soft cap
    if (totalRaised < sale.softCap) {
      // Sale failed, return 0 allocation
      return {
        saleId,
        userId,
        contributionAmount: Number(contribution.amount),
        allocationAmount: 0,
        allocationPercentage: 0,
        saleStatus: sale.status,
        saleEnded: true,
      };
    }

    // Calculate allocation based on contribution percentage
    const contributionPercentage = Number(contribution.amount) / totalRaised;
    const allocationAmount = contributionPercentage * sale.hardCap;

    return {
      saleId,
      userId,
      contributionAmount: Number(contribution.amount),
      allocationAmount,
      allocationPercentage: contributionPercentage * 100,
      saleStatus: sale.status,
      saleEnded: true,
    };
  }

  async claimTokens(userId: string, saleId: string): Promise<ClaimResponseDto> {
    const sale = await this.findSaleById(saleId);

    if (sale.status !== SaleStatus.ENDED) {
      throw new BadRequestException('Sale has not ended yet');
    }

    // Find user's contribution to this sale
    const contribution = await this.contributionRepository.findOne({
      where: { userId, saleId },
      relations: ['user'],
    });

    if (!contribution) {
      throw new BadRequestException('User has not contributed to this sale');
    }

    // Check if user has already claimed tokens
    if (contribution.status === 'claimed') {
      throw new BadRequestException('Tokens have already been claimed');
    }

    // Get user's XRPL address
    const user = await this.usersService.findOne(userId);
    if (!user?.xrplAddress) {
      throw new BadRequestException('User XRPL address not found');
    }

    // Calculate allocation
    const allocation = await this.getUserAllocation(userId, saleId);

    if (allocation.allocationAmount === 0) {
      throw new BadRequestException(
        'No tokens to claim - sale did not meet soft cap',
      );
    }

    // Check trustline existence via XRPL
    const hasTrustline = await this.xrplService.checkTrustline(
      user.xrplAddress,
      sale.collectionAddress,
    );

    if (!hasTrustline) {
      throw new BadRequestException(
        'Trustline to issuer not found. Please set up a trustline before claiming tokens.',
      );
    }

    try {
      // Issue tokens to user
      const result = await this.xrplService.issueTokens(
        user.xrplAddress,
        sale.collectionAddress,
        allocation.allocationAmount,
        sale.symbol,
      );

      // Update contribution status
      contribution.status = 'claimed';
      await this.contributionRepository.save(contribution);

      this.logger.log(
        `Tokens claimed successfully for user ${userId} in sale ${saleId}: ${allocation.allocationAmount} ${sale.symbol}`,
      );

      return {
        success: true,
        message: `Successfully claimed ${allocation.allocationAmount} ${sale.symbol}`,
        transactionHash: result.transactionHash,
      };
    } catch (error) {
      this.logger.error(
        `Failed to issue tokens for user ${userId} in sale ${saleId}: ${error as string}`,
      );
      throw new BadRequestException(
        'Failed to issue tokens. Please try again later.',
      );
    }
  }
}
