<ion-header [translucent]="true">
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-menu-button aria-label="Open navigation menu"></ion-menu-button>
    </ion-buttons>
    <ion-title>Dashboard</ion-title>
    <ion-buttons slot="end">
      <app-theme-toggle></app-theme-toggle>
      <ion-button
        (click)="onDebugWebSocket()"
        fill="clear"
        size="small"
        aria-label="Debug WebSocket connection"
      >
        <ion-icon name="bug-outline" aria-hidden="true"></ion-icon>
      </ion-button>
      <ion-button
        (click)="onRefreshData()"
        [disabled]="isLoading()"
        aria-label="Refresh data"
      >
        <ion-icon
          name="refresh"
          [class.spin]="isLoading()"
          aria-hidden="true"
        ></ion-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true">
  <ion-refresher
    slot="fixed"
    (ionRefresh)="onRefreshData()"
    aria-label="Pull to refresh"
  >
    <ion-refresher-content></ion-refresher-content>
  </ion-refresher>

  <div class="dashboard-container">
    <!-- Connection Status -->
    <app-connection-status
      [isConnected]="isConnected()"
    ></app-connection-status>

    <!-- Welcome Section -->
    <div class="welcome-section">
      @if (currentUser$ | async; as user) {
      <h1>Welcome back, {{ user.username }}!</h1>
      <p>Here's your XRPL Launchpad overview</p>
      } @else {
      <h1>Welcome to XRPL Launchpad</h1>
      <p>Here's your overview</p>
      }
    </div>

    <!-- XRPL Balance Card -->
    <app-balance-card [balance]="balance$ | async"></app-balance-card>

    <!-- Recent Transactions -->
    <app-transactions-list
      [transactions]="transactions$ | async"
      [currentUser]="currentUser$ | async"
      (viewAll)="onViewAllTransactions()"
      (viewDetails)="onViewTransactionDetails($event)"
      (copyHash)="onCopyTransactionHash($event.hash, $event.event)"
    >
    </app-transactions-list>

    <!-- Active Launchpad Sales -->
    <app-sales-list
      [sales]="activeSales$ | async"
      (viewAll)="onViewAllSales()"
      (viewSale)="onViewSale($event)"
    >
    </app-sales-list>
  </div>
</ion-content>
