import { Controller, Get, Res } from '@nestjs/common';
import { ApiOperation, ApiResponse } from '@nestjs/swagger';
import { AppService, HealthStatus } from './app.service';
import { BaseResponseDto } from './common/dto/base-response.dto';
import type { Response } from 'express';

@Controller()
export class AppController {
  constructor(private readonly appService: AppService) {}

  @Get()
  @ApiOperation({ summary: 'Get application info' })
  @ApiResponse({ status: 200, description: 'Application information' })
  getHello(): BaseResponseDto<{ message: string; version: string }> {
    return BaseResponseDto.success(
      {
        message: this.appService.getHello(),
        version: '0.0.1',
      },
      'XRPL Launchpad API is running',
    );
  }

  @Get('health')
  @ApiOperation({ summary: 'Health check endpoint' })
  @ApiResponse({ status: 200, description: 'Health status' })
  health(): BaseResponseDto<HealthStatus> {
    return BaseResponseDto.success(
      this.appService.getHealthStatus(),
      'Health check completed',
    );
  }

  @Get('favicon.ico')
  @ApiOperation({ summary: 'Favicon endpoint' })
  @ApiResponse({ status: 200, description: 'Favicon icon' })
  getFavicon(@Res() res: Response): void {
    res.status(204).end();
  }
}
