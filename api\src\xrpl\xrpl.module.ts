import { Global, Module } from '@nestjs/common';
import { XrplService } from './xrpl.service';
import { XrplController } from './xrpl.controller';
import { XrplGateway } from './xrpl.gateway';
import { JwtModule } from '@nestjs/jwt';
import { ConfigModule, ConfigService } from '@nestjs/config';

@Global()
@Module({
  imports: [
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        secret: configService.get<string>('jwt.secret') || 'super-secret-key',
        signOptions: {
          expiresIn: configService.get<string>('jwt.expiresIn') || '15m',
        },
      }),
      inject: [ConfigService],
    }),
  ],
  controllers: [XrplController],
  providers: [XrplService, XrplGateway],
  exports: [XrplService],
})
export class XrplModule {}
