# Node.js / npm
node_modules/
.pnp.caches/
api/node_modules/
web/node_modules/
npm-debug.log
yarn-error.log

# Environment variables
.env
.env.local
.env.development
.env.test
.env.production

# Build outputs
dist/
build/
api/dist/
web/dist/
web/browser/
web/server/

# Angular-specific
.angular/
coverage/
coverage-combined/
compodoc/

# NestJS-specific
coverage/
*.log

# Docker
docker-compose.override.yml
*.dockerignore
Dockerfile.local

# Database
*.sql
*.sqlite
*.db
data/

# Editor and OS files
.vscode/
.idea/
*.suo
*.ntvs*
*.njsproj
*.sln
*.swp
.DS_Store
Thumbs.db

# Cursor
.cursorrules
.cursor/


# Test artifacts
test-results/
e2e-results/
junit.xml
coverage.xml

# Temporary files
*.tmp
*.bak
*.swp
*~

# Documentation artifacts
docs/generated/
web/documentation/

# Secrets and sensitive data
*.key
*.pem
*.seed
*.secret
secrets/

