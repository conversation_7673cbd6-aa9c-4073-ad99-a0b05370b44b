import {
  ComponentFixture,
  TestBed,
  fakeAsync,
  tick,
} from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { of, throwError, BehaviorSubject } from 'rxjs';
import { LaunchpadDetailPage } from './launchpad-detail.page';
import { ApiService } from '../../services/api/api.service';
import { WebSocketService } from '../../services/websocket/websocket.service';
import { Sale, UserAllocation, PreparedTransaction } from '../../types';
import { NavController } from '@ionic/angular';

describe('LaunchpadDetailPage', () => {
  let component: LaunchpadDetailPage;
  let fixture: ComponentFixture<LaunchpadDetailPage>;
  let mockApiService: jasmine.SpyObj<ApiService>;
  let mockWebSocketService: jasmine.SpyObj<WebSocketService>;
  let mockActivatedRoute: jasmine.SpyObj<ActivatedRoute>;
  let mockRouter: jasmine.SpyObj<Router>;
  let routeParamsSubject: BehaviorSubject<any>;

  const mockSale: Sale = {
    id: 'sale1',
    symbol: 'TEST',
    price: 0.1,
    description: 'Test description',
    status: 'active',
    start: '2024-01-01T00:00:00Z',
    end: '2024-12-31T23:59:59Z',
    softCap: 1000,
    hardCap: 5000,
    collectionAddress: 'rCollection123',
    website: 'https://test.com',
    whitepaper: 'https://test.com/whitepaper',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
  };

  const mockUserAllocation: UserAllocation = {
    saleId: 'sale1',
    userId: '1',
    contributionAmount: 100,
    allocationAmount: 100,
    allocationPercentage: 10,
    saleStatus: 'ended',
    saleEnded: true,
  };

  const mockPreparedTransaction: PreparedTransaction = {
    transactionBlob: 'mock-transaction-blob',
    hash: 'mock-hash',
    amount: 100,
    fee: 0.000012,
    sequence: 123,
    lastLedgerSequence: 12345,
    message: 'Mock transaction',
  };

  beforeEach(async () => {
    const apiServiceSpy = jasmine.createSpyObj('ApiService', [
      'getSale',
      'getUserAllocation',
      'prepareSendTransaction',
      'claimTokens',
      'isAuthenticated',
    ]);

    const webSocketServiceSpy = jasmine.createSpyObj(
      'WebSocketService',
      [
        'isReady',
        'start',
        'getIsConnected',
        'subscribeToSale',
        'unsubscribeFromSale',
        'requestBalanceUpdate',
        'requestTransactionsUpdate',
      ],
      {
        balance: { signal: () => ({ balance: 1000, currency: 'XRP' }) },
        transactions: { signal: () => [] },
        saleProgress: {
          signal: () => ({
            totalRaised: 1000,
            progressPercent: 20,
            userAllocation: 100,
            saleId: 'sale1',
          }),
        },
        isConnected$: of(true),
      }
    );

    const activatedRouteSpy = jasmine.createSpyObj('ActivatedRoute', [], {
      params: new BehaviorSubject({ id: 'sale1' }),
    });

    const routerSpy = jasmine.createSpyObj('Router', ['navigate']);
    const navControllerSpy = jasmine.createSpyObj('NavController', [
      'navigateBack',
      'navigateForward',
      'navigateRoot',
    ]);

    routeParamsSubject = new BehaviorSubject({ id: 'sale1' });
    activatedRouteSpy.params = routeParamsSubject.asObservable();

    await TestBed.configureTestingModule({
      imports: [LaunchpadDetailPage, ReactiveFormsModule],
      providers: [
        { provide: ApiService, useValue: apiServiceSpy },
        { provide: WebSocketService, useValue: webSocketServiceSpy },
        { provide: ActivatedRoute, useValue: activatedRouteSpy },
        { provide: Router, useValue: routerSpy },
        { provide: NavController, useValue: navControllerSpy },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(LaunchpadDetailPage);
    component = fixture.componentInstance;
    mockApiService = TestBed.inject(ApiService) as jasmine.SpyObj<ApiService>;
    mockWebSocketService = TestBed.inject(
      WebSocketService
    ) as jasmine.SpyObj<WebSocketService>;
    mockActivatedRoute = TestBed.inject(
      ActivatedRoute
    ) as jasmine.SpyObj<ActivatedRoute>;
    mockRouter = TestBed.inject(Router) as jasmine.SpyObj<Router>;

    mockApiService.getSale.and.returnValue(of(mockSale));
    mockApiService.getUserAllocation.and.returnValue(of(mockUserAllocation));
    mockApiService.isAuthenticated.and.returnValue(true);
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize form with validators', () => {
    expect(
      component.contributionForm.get('amount')?.hasError('required')
    ).toBeTruthy();
    expect(
      component.contributionForm.get('destinationTag')?.hasError('required')
    ).toBeTruthy();
    expect(
      component.contributionForm.get('acceptTerms')?.hasError('required')
    ).toBeTruthy();
  });

  it('should load data on init with sale ID from route', () => {
    mockApiService.getSale.and.returnValue(of(mockSale));
    mockApiService.getUserAllocation.and.returnValue(of(mockUserAllocation));
    mockApiService.isAuthenticated.and.returnValue(true);
    mockWebSocketService.isReady.and.returnValue(true);
    mockWebSocketService.getIsConnected.and.returnValue(true);

    component.ngOnInit();

    expect(component.saleId()).toBe('sale1');
    expect(mockApiService.getSale).toHaveBeenCalledWith('sale1');
    expect(mockApiService.getUserAllocation).toHaveBeenCalledWith('sale1');
  });

  it('should start WebSocket service if ready', () => {
    mockApiService.getSale.and.returnValue(of(mockSale));
    mockApiService.getUserAllocation.and.returnValue(of(mockUserAllocation));
    mockApiService.isAuthenticated.and.returnValue(true);
    mockWebSocketService.isReady.and.returnValue(true);
    mockWebSocketService.getIsConnected.and.returnValue(true);

    component.ngOnInit();

    expect(mockWebSocketService.start).toHaveBeenCalled();
  });

  it('should subscribe to sale updates when connected', fakeAsync(() => {
    mockApiService.getSale.and.returnValue(of(mockSale));
    mockApiService.getUserAllocation.and.returnValue(of(mockUserAllocation));
    mockApiService.isAuthenticated.and.returnValue(true);
    mockWebSocketService.isReady.and.returnValue(true);
    mockWebSocketService.getIsConnected.and.returnValue(true);

    component.ngOnInit();

    // Fast-forward time to trigger the setTimeout in ngOnInit
    tick(1000);

    expect(mockWebSocketService.subscribeToSale).toHaveBeenCalledWith('sale1');
    expect(mockWebSocketService.requestBalanceUpdate).toHaveBeenCalled();
    expect(mockWebSocketService.requestTransactionsUpdate).toHaveBeenCalled();
  }));

  it('should pre-fill destination tag when sale is loaded', () => {
    mockApiService.getSale.and.returnValue(of(mockSale));
    mockApiService.getUserAllocation.and.returnValue(of(mockUserAllocation));
    mockApiService.isAuthenticated.and.returnValue(true);
    mockWebSocketService.isReady.and.returnValue(false);

    component.ngOnInit();

    expect(component.contributionForm.get('destinationTag')?.value).toMatch(
      /^\d+$/
    );
  });

  it('should handle contribution form submission', () => {
    component.contributionForm.patchValue({
      amount: '100',
      destinationTag: '123456',
      acceptTerms: true,
    });

    // Set up the sale$ observable
    component.sale$ = of(mockSale);
    component.saleId.set('sale1');

    mockApiService.prepareSendTransaction.and.returnValue(
      of(mockPreparedTransaction)
    );

    component.onContribute();

    expect(mockApiService.prepareSendTransaction).toHaveBeenCalledWith({
      destination: mockSale.collectionAddress,
      amount: 100,
    });
  });

  it('should not submit contribution form when invalid', () => {
    component.contributionForm.patchValue({
      amount: '',
      destinationTag: '',
      acceptTerms: false,
    });

    component.onContribute();

    expect(mockApiService.prepareSendTransaction).not.toHaveBeenCalled();
  });

  it('should handle claim tokens', () => {
    component.saleId.set('sale1');

    mockApiService.claimTokens.and.returnValue(
      of({ success: true, message: 'Claimed successfully' })
    );

    component.onClaim();

    expect(mockApiService.claimTokens).toHaveBeenCalledWith('sale1');
  });

  it('should refresh data when onRefreshData is called', () => {
    component.saleId.set('sale1');
    component.isAuthenticated.set(true);

    mockApiService.getSale.and.returnValue(of(mockSale));
    mockApiService.getUserAllocation.and.returnValue(of(mockUserAllocation));

    component.onRefreshData();

    expect(mockApiService.getSale).toHaveBeenCalledWith('sale1');
    expect(mockApiService.getUserAllocation).toHaveBeenCalledWith('sale1');
  });

  it('should return correct status colors', () => {
    expect(component.getStatusColor('active')).toBe('success');
    expect(component.getStatusColor('ended')).toBe('primary');
    expect(component.getStatusColor('canceled')).toBe('danger');
    expect(component.getStatusColor('unknown')).toBe('medium');
  });

  it('should return correct status icons', () => {
    expect(component.getStatusIcon('active')).toBe('play-circle');
    expect(component.getStatusIcon('ended')).toBe('checkmark-circle');
    expect(component.getStatusIcon('canceled')).toBe('close-circle');
    expect(component.getStatusIcon('unknown')).toBe('help-circle');
  });

  it('should calculate progress percentage correctly', () => {
    expect(component.getProgressPercentage(500, 1000)).toBe(50);
    expect(component.getProgressPercentage(1000, 1000)).toBe(100);
    expect(component.getProgressPercentage(1500, 1000)).toBe(100);
    expect(component.getProgressPercentage(0, 0)).toBe(0);
  });

  it('should format dates correctly', () => {
    const dateString = '2024-01-15T10:30:00Z';
    const formatted = component.formatDate(dateString);
    expect(formatted).toContain('January');
    expect(formatted).toContain('15');
    expect(formatted).toContain('2024');
  });

  it('should format currency correctly', () => {
    expect(component.formatCurrency(1234.567)).toBe('1,234.567');
    expect(component.formatCurrency(100)).toBe('100.00');
  });

  it('should calculate time remaining correctly', () => {
    const futureDate = new Date();
    futureDate.setDate(futureDate.getDate() + 5);
    const futureSale = {
      ...mockSale,
      end: futureDate.toISOString(),
      status: 'active' as const,
    };

    const timeRemaining = component.getTimeRemaining(futureSale);
    expect(timeRemaining).toContain('day');
  });

  it('should return empty string for ended sales', () => {
    const pastSale = { ...mockSale, status: 'ended' as const };
    expect(component.getTimeRemaining(pastSale)).toBe('');
  });

  it('should validate form correctly', () => {
    expect(component.isFormValid()).toBeFalsy();

    component.contributionForm.patchValue({
      amount: '100',
      destinationTag: '123456',
      acceptTerms: true,
    });

    expect(component.isFormValid()).toBeTruthy();
  });

  it('should determine if user can claim correctly', () => {
    expect(component.canUserClaim(null)).toBeFalsy();

    const endedAllocation = {
      ...mockUserAllocation,
      saleStatus: 'ended' as const,
      saleEnded: true,
      allocationAmount: 100,
    };
    expect(component.canUserClaim(endedAllocation)).toBeTruthy();

    const activeAllocation = {
      ...mockUserAllocation,
      saleStatus: 'active' as const,
      saleEnded: false,
    };
    expect(component.canUserClaim(activeAllocation)).toBeFalsy();

    const noAllocation = { ...mockUserAllocation, allocationAmount: 0 };
    expect(component.canUserClaim(noAllocation)).toBeFalsy();
  });

  it('should show transaction blob when prepared', () => {
    component.showTransactionBlob(mockPreparedTransaction);

    expect(component.preparedTransaction).toBe(mockPreparedTransaction);
    expect(component.isTransactionBlobVisible()).toBeTruthy();
  });

  it('should copy transaction blob to clipboard', () => {
    // Check if spy already exists and restore it if it does
    if (jasmine.isSpy(navigator.clipboard.writeText)) {
      (navigator.clipboard.writeText as jasmine.Spy).and.returnValue(
        Promise.resolve()
      );
    } else {
      spyOn(navigator.clipboard, 'writeText').and.returnValue(
        Promise.resolve()
      );
    }

    component.preparedTransaction = mockPreparedTransaction;

    component.copyTransactionBlob();

    expect(navigator.clipboard.writeText).toHaveBeenCalledWith(
      mockPreparedTransaction.transactionBlob
    );
  });

  it('should open Xaman wallet with correct URL', () => {
    spyOn(window, 'open');
    component.preparedTransaction = mockPreparedTransaction;

    component.openXamanWallet();

    expect(window.open).toHaveBeenCalledWith(
      `xumm://xapp?xapp=sign&blob=${mockPreparedTransaction.transactionBlob}`,
      '_blank'
    );
  });

  it('should close transaction blob', () => {
    component.preparedTransaction = mockPreparedTransaction;
    component.isTransactionBlobVisible.set(true);

    component.closeTransactionBlob();

    expect(component.isTransactionBlobVisible()).toBeFalsy();
    expect(component.preparedTransaction).toBeNull();
  });

  it('should generate unique destination tags', () => {
    const tag1 = component.generateDestinationTag('sale1');
    const tag2 = component.generateDestinationTag('sale2');

    expect(tag1).toMatch(/^\d+$/);
    expect(tag2).toMatch(/^\d+$/);
    expect(tag1).not.toBe(tag2);
  });

  it('should clean up subscriptions on destroy', () => {
    spyOn(component['destroy$'], 'next');
    spyOn(component['destroy$'], 'complete');

    component.ngOnDestroy();

    expect(component['destroy$'].next).toHaveBeenCalled();
    expect(component['destroy$'].complete).toHaveBeenCalled();
    expect(mockWebSocketService.unsubscribeFromSale).toHaveBeenCalled();
  });
});
