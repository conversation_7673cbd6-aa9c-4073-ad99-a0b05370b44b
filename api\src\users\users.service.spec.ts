import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { UsersService } from './users.service';
import { User, UserRole } from './entities/user.entity';

describe('UsersService', () => {
  let service: UsersService;
  let mockRepository: jest.Mocked<Repository<User>>;

  const mockUser: User = {
    id: 'user-123',
    username: 'testuser',
    email: '<EMAIL>',
    password: 'hashedpassword',
    role: UserRole.USER,
    xrplAddress: '',
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  beforeEach(async () => {
    const mockRepositoryMethods = {
      find: jest.fn(),
      findOne: jest.fn(),
      create: jest.fn(),
      save: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UsersService,
        {
          provide: getRepositoryToken(User),
          useValue: mockRepositoryMethods,
        },
      ],
    }).compile();

    service = module.get<UsersService>(UsersService);
    mockRepository = module.get(getRepositoryToken(User));
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('linkXrplAddress', () => {
    it('should successfully link XRPL address to user', async () => {
      const xrplAddress = 'rHb9CJAWyB4rj91VRWn96DkukG4bwdtyTh';
      const userId = 'user-123';

      // Mock that no other user has this address
      mockRepository.findOne.mockResolvedValue(null);

      // Mock successful update
      mockRepository.update.mockResolvedValue({ affected: 1 } as any);

      // Mock finding the updated user
      const updatedUser = { ...mockUser, xrplAddress };
      mockRepository.findOne.mockResolvedValue(updatedUser);

      const result = await service.linkXrplAddress(userId, xrplAddress);

      expect(mockRepository.findOne).toHaveBeenCalledWith({
        where: { xrplAddress },
      });
      expect(mockRepository.update).toHaveBeenCalledWith(userId, {
        xrplAddress,
      });
      expect(result).toEqual(updatedUser);
    });

    it('should throw error if address is already linked to another user', async () => {
      const xrplAddress = 'rHb9CJAWyB4rj91VRWn96DkukG4bwdtyTh';
      const userId = 'user-123';
      const otherUserId = 'user-456';

      // Mock that another user already has this address
      const existingUser = { ...mockUser, id: otherUserId, xrplAddress };
      mockRepository.findOne.mockResolvedValue(existingUser);

      try {
        await service.linkXrplAddress(userId, xrplAddress);
        fail('Expected an error to be thrown');
      } catch (error) {
        expect(error).toBeInstanceOf(Error);
        expect((error as Error).message).toBe(
          'XRPL address is already linked to another user',
        );
      }

      expect(mockRepository.findOne).toHaveBeenCalledWith({
        where: { xrplAddress },
      });
      expect(mockRepository.update).not.toHaveBeenCalled();
    });

    it('should allow user to update their own address', async () => {
      const xrplAddress = 'rHb9CJAWyB4rj91VRWn96DkukG4bwdtyTh';
      const userId = 'user-123';

      // Mock that the same user already has this address
      const existingUser = { ...mockUser, id: userId, xrplAddress };
      mockRepository.findOne.mockResolvedValue(existingUser);

      // Mock successful update
      mockRepository.update.mockResolvedValue({ affected: 1 } as any);

      // Mock finding the updated user
      mockRepository.findOne.mockResolvedValue(existingUser);

      const result = await service.linkXrplAddress(userId, xrplAddress);

      expect(result).toEqual(existingUser);
      expect(mockRepository.update).toHaveBeenCalledWith(userId, {
        xrplAddress,
      });
    });
  });

  describe('unlinkXrplAddress', () => {
    it('should successfully unlink XRPL address from user', async () => {
      const userId = 'user-123';
      const userWithAddress = {
        ...mockUser,
        xrplAddress: 'rHb9CJAWyB4rj91VRWn96DkukG4bwdtyTh',
      };

      // Mock successful update
      mockRepository.update.mockResolvedValue({ affected: 1 } as any);

      // Mock finding the updated user
      const updatedUser = { ...userWithAddress, xrplAddress: '' };
      mockRepository.findOne.mockResolvedValue(updatedUser);

      const result = await service.unlinkXrplAddress(userId);

      expect(mockRepository.update).toHaveBeenCalledWith(userId, {
        xrplAddress: '',
      });
      expect(result).toEqual(updatedUser);
    });
  });

  describe('findOne', () => {
    it('should return user by id', async () => {
      mockRepository.findOne.mockResolvedValue(mockUser);

      const result = await service.findOne('user-123');

      expect(mockRepository.findOne).toHaveBeenCalledWith({
        where: { id: 'user-123' },
      });
      expect(result).toEqual(mockUser);
    });

    it('should return null if user not found', async () => {
      mockRepository.findOne.mockResolvedValue(null);

      const result = await service.findOne('nonexistent');

      expect(result).toBeNull();
    });
  });

  describe('update', () => {
    it('should update user and return updated user', async () => {
      const updateData = { username: 'newusername' };
      const updatedUser = { ...mockUser, username: 'newusername' };

      mockRepository.update.mockResolvedValue({ affected: 1 } as any);
      mockRepository.findOne.mockResolvedValue(updatedUser);

      const result = await service.update('user-123', updateData);

      expect(mockRepository.update).toHaveBeenCalledWith(
        'user-123',
        updateData,
      );
      expect(result).toEqual(updatedUser);
    });
  });
});
