// Remove the import since it's causing issues

ion-card {
  margin-bottom: 16px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  ion-card-header {
    padding: 16px 16px 8px 16px;

    ion-card-title {
      display: flex;
      align-items: center;
      font-size: 18px;
      font-weight: 600;
      color: var(--ion-color-dark);

      ion-icon {
        margin-right: 8px;
        font-size: 20px;
      }
    }
  }

  ion-card-content {
    padding: 8px 16px 16px 16px;
  }
}

.loading-state {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 16px 0;
}

ion-list {
  padding: 0;
}

.admin-sale-item {
  --padding-start: 0;
  --padding-end: 0;
  --inner-padding-end: 0;
  margin-bottom: 12px;
  border-radius: 8px;
  border: 1px solid var(--ion-color-light-shade);

  &:last-child {
    margin-bottom: 0;
  }

  ion-label {
    h3 {
      font-size: 16px;
      font-weight: 600;
      color: var(--ion-color-dark);
      margin: 0 0 4px 0;
    }

    p {
      font-size: 14px;
      color: var(--ion-color-medium);
      margin: 0 0 4px 0;

      &.sale-details {
        display: flex;
        flex-direction: column;
        gap: 2px;
        margin-bottom: 6px;

        .sale-price {
          font-weight: 600;
          color: var(--ion-color-primary);
        }

        .sale-dates {
          font-size: 12px;
          color: var(--ion-color-medium-shade);
        }
      }

      &.sale-stats {
        display: flex;
        gap: 16px;
        font-size: 12px;
        color: var(--ion-color-medium-shade);
        margin: 0;

        span {
          font-weight: 500;
        }
      }
    }
  }

  ion-icon {
    font-size: 20px;
    margin-right: 12px;
  }

  .sale-actions {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 8px;

    .status-badge {
      font-size: 11px;
      font-weight: 600;
      color: white;
    }

    .action-buttons {
      display: flex;
      gap: 4px;

      ion-button {
        --padding-start: 6px;
        --padding-end: 6px;
        --padding-top: 6px;
        --padding-bottom: 6px;
        height: 32px;
        width: 32px;

        ion-icon {
          font-size: 16px;
          margin: 0;
        }
      }
    }
  }
}

.empty-state {
  text-align: center;
  padding: 32px 16px;
  color: var(--ion-color-medium);

  ion-icon {
    margin-bottom: 16px;
    opacity: 0.5;
  }

  p {
    margin: 0 0 16px 0;
    font-size: 16px;
  }
}

ion-badge {
  font-size: 12px;
  font-weight: 500;
}

ion-button[fill='clear'] {
  --color: var(--ion-color-primary);
  font-size: 14px;
  font-weight: 500;
  margin: 0;
  --padding-start: 8px;
  --padding-end: 8px;
}

@media (max-width: 576px) {
  .admin-sale-item {
    .sale-actions {
      .action-buttons {
        flex-direction: column;
        gap: 2px;

        ion-button {
          height: 28px;
          width: 28px;

          ion-icon {
            font-size: 14px;
          }
        }
      }
    }
  }
}
