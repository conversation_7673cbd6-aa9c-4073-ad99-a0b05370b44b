import {
  Is<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ption<PERSON>,
  IsBoolean,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class TransactionDto {
  @ApiProperty({
    description: 'XRPL transaction hash',
    example: '********90ABCDEF********90ABCDEF********90ABCDEF********90ABCDEF',
  })
  @IsString()
  hash!: string;

  @ApiProperty({
    description: 'Source account address',
    example: 'rPT1Sjq2YGrBMTttX4GZHjKu9yfEVRox89',
  })
  @IsString()
  account!: string;

  @ApiProperty({
    description: 'Destination account address',
    example: 'rPT1Sjq2YGrBMTttX4GZHjKu9yfEVRox89',
  })
  @IsString()
  destination!: string;

  @ApiProperty({
    description: 'Amount transferred in XRP',
    example: 100.5,
  })
  @IsNumber()
  amount!: number;

  @ApiProperty({
    description: 'Currency type (always XRP for this endpoint)',
    example: 'XRP',
    default: 'XRP',
  })
  @IsString()
  currency: string = 'XRP';

  @ApiProperty({
    description: 'Transaction fee in drops',
    example: 10,
  })
  @IsNumber()
  fee!: number;

  @ApiProperty({
    description: 'Ledger index when transaction was included',
    example: ********,
  })
  @IsNumber()
  ledgerIndex!: number;

  @ApiProperty({
    description: 'Date when transaction was processed',
    example: '2025-01-15T10:00:00Z',
  })
  @IsDate()
  date!: Date;

  @ApiProperty({
    description: 'Type of transaction',
    example: 'Payment',
  })
  @IsString()
  type!: string;

  @ApiProperty({
    description: 'Optional memo attached to the transaction',
    example: 'Payment for services',
    required: false,
  })
  @IsOptional()
  @IsString()
  memo?: string | undefined;

  @ApiProperty({
    description: 'Whether the transaction has been validated',
    example: true,
  })
  @IsBoolean()
  validated!: boolean;
}
