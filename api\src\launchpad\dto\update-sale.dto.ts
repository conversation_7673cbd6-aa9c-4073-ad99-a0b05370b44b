import { PartialType } from '@nestjs/mapped-types';
import { CreateSaleDto } from './create-sale.dto';
import { IsEnum, IsOptional } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { SaleStatus } from '../entities/sale.entity';

export class UpdateSaleDto extends PartialType(CreateSaleDto) {
  @ApiProperty({
    description: 'New status for the token sale',
    enum: SaleStatus,
    example: SaleStatus.CANCELED,
    required: false,
  })
  @IsOptional()
  @IsEnum(SaleStatus)
  status?: SaleStatus;
}
