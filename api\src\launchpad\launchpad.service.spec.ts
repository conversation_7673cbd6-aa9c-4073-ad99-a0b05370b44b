import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { LaunchpadService } from './launchpad.service';
import { Sale, SaleStatus } from './entities/sale.entity';
import { Contribution } from './entities/contribution.entity';
import { UsersService } from '../users/users.service';
import { XrplService } from '../xrpl/xrpl.service';
import { CreateSaleDto } from './dto/create-sale.dto';
import { SaleContributionEvent } from '../xrpl/xrpl.service';
import { BadRequestException, NotFoundException } from '@nestjs/common';
import { User, UserRole } from '../users/entities/user.entity';

describe('LaunchpadService', () => {
  let service: LaunchpadService;
  let saleRepository: jest.Mocked<Repository<Sale>>;
  let contributionRepository: jest.Mocked<Repository<Contribution>>;
  let usersService: jest.Mocked<UsersService>;
  let xrplService: jest.Mocked<XrplService>;

  const mockUser: User = {
    id: 'user123',
    email: '<EMAIL>',
    username: 'testuser',
    xrplAddress: 'rTestUserAddress123456789',
    password: 'hashedPassword',
    role: UserRole.USER,
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockSale: Sale = {
    id: 'sale123',
    name: 'Test Sale',
    symbol: 'TEST',
    description: 'Test sale description',
    price: 0.1,
    softCap: 1000,
    hardCap: 5000,
    start: new Date(Date.now() + 86400000), // Tomorrow
    end: new Date(Date.now() + 172800000), // Day after tomorrow
    status: SaleStatus.ACTIVE,
    collectionAddress: 'rTestCollectionAddress123456789',
    createdAt: new Date(),
    updatedAt: new Date(),
    contributions: [],
  } as Sale;

  const mockContribution: Contribution = {
    id: 'contribution123',
    userId: 'user123',
    saleId: 'sale123',
    amount: 500,
    txHash: 'txHash123',
    status: 'pending',
    createdAt: new Date(),
    updatedAt: new Date(),
    user: mockUser,
    sale: mockSale,
  } as Contribution;

  const mockClaimedContribution: Contribution = {
    id: 'contribution123',
    userId: 'user123',
    saleId: 'sale123',
    amount: 500,
    txHash: 'txHash123',
    status: 'claimed',
    createdAt: new Date(),
    updatedAt: new Date(),
    user: mockUser,
    sale: mockSale,
  } as Contribution;

  const mockSaleWithContributions: Sale = {
    ...mockSale,
    contributions: [mockContribution],
  };

  const mockEndedSale: Sale = {
    ...mockSale,
    status: SaleStatus.ENDED,
    end: new Date(Date.now() - 86400000), // Yesterday
  };

  beforeEach(async () => {
    const mockSaleRepository = {
      create: jest.fn(),
      save: jest.fn(),
      find: jest.fn(),
      findOne: jest.fn(),
      update: jest.fn(),
    };

    const mockContributionRepository = {
      create: jest.fn(),
      save: jest.fn(),
      find: jest.fn(),
      findOne: jest.fn(),
      update: jest.fn(),
    };

    const mockUsersService = {
      findOne: jest.fn(),
      findByXrplAddress: jest.fn(),
    };

    const mockXrplService = {
      checkTrustline: jest.fn(),
      issueTokens: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        LaunchpadService,
        {
          provide: getRepositoryToken(Sale),
          useValue: mockSaleRepository,
        },
        {
          provide: getRepositoryToken(Contribution),
          useValue: mockContributionRepository,
        },
        {
          provide: UsersService,
          useValue: mockUsersService,
        },
        {
          provide: XrplService,
          useValue: mockXrplService,
        },
      ],
    }).compile();

    service = module.get<LaunchpadService>(LaunchpadService);
    saleRepository = module.get(getRepositoryToken(Sale));
    contributionRepository = module.get(getRepositoryToken(Contribution));
    usersService = module.get(UsersService);
    xrplService = module.get(XrplService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('createSale', () => {
    const createSaleDto: CreateSaleDto = {
      symbol: 'TEST',
      description: 'Test sale description',
      price: 0.1,
      softCap: 1000,
      hardCap: 5000,
      start: new Date(Date.now() + 86400000).toISOString(),
      end: new Date(Date.now() + 172800000).toISOString(),
      collectionAddress: 'rTestCollectionAddress123456789',
    };

    it('should create sale successfully', async () => {
      const mockCreatedSale = { ...mockSale, id: 'new-sale-id' };
      saleRepository.create.mockReturnValue(mockCreatedSale);
      saleRepository.save.mockResolvedValue(mockCreatedSale);

      const result = await service.createSale(createSaleDto);

      expect(result).toEqual(mockCreatedSale);
      expect(saleRepository.create).toHaveBeenCalledWith({
        ...createSaleDto,
        start: expect.any(Date),
        end: expect.any(Date),
        status: SaleStatus.ACTIVE,
      });
      expect(saleRepository.save).toHaveBeenCalledWith(mockCreatedSale);
    });

    it('should throw error when start date is in the past', async () => {
      const pastStartDto = {
        ...createSaleDto,
        start: new Date(Date.now() - 86400000).toISOString(), // Yesterday
      };

      await expect(service.createSale(pastStartDto)).rejects.toThrow(
        new BadRequestException('Start date must be in the future'),
      );
    });

    it('should throw error when end date is before start date', async () => {
      const invalidEndDto = {
        ...createSaleDto,
        end: new Date(Date.now() + 43200000).toISOString(), // 12 hours from now
      };

      await expect(service.createSale(invalidEndDto)).rejects.toThrow(
        new BadRequestException('End date must be after start date'),
      );
    });

    it('should throw error when soft cap is greater than or equal to hard cap', async () => {
      const invalidCapsDto = {
        ...createSaleDto,
        softCap: 5000,
        hardCap: 5000,
      };

      await expect(service.createSale(invalidCapsDto)).rejects.toThrow(
        new BadRequestException('Soft cap must be less than hard cap'),
      );
    });
  });

  describe('findAllSales', () => {
    it('should return all sales when no status filter', async () => {
      const mockSales = [mockSale, mockSaleWithContributions];
      saleRepository.find.mockResolvedValue(mockSales);

      const result = await service.findAllSales();

      expect(result).toEqual(mockSales);
      expect(saleRepository.find).toHaveBeenCalledWith({
        where: {},
        relations: ['contributions'],
        order: { createdAt: 'DESC' },
      });
    });

    it('should return sales filtered by status', async () => {
      const mockActiveSales = [mockSale];
      saleRepository.find.mockResolvedValue(mockActiveSales);

      const result = await service.findAllSales(SaleStatus.ACTIVE);

      expect(result).toEqual(mockActiveSales);
      expect(saleRepository.find).toHaveBeenCalledWith({
        where: { status: SaleStatus.ACTIVE },
        relations: ['contributions'],
        order: { createdAt: 'DESC' },
      });
    });
  });

  describe('findActiveSales', () => {
    it('should return active sales within date range', async () => {
      const mockActiveSales = [mockSale];
      saleRepository.find.mockResolvedValue(mockActiveSales);

      const result = await service.findActiveSales();

      expect(result).toEqual(mockActiveSales);
      expect(saleRepository.find).toHaveBeenCalledWith({
        where: {
          status: SaleStatus.ACTIVE,
          start: expect.any(Object),
          end: expect.any(Object),
        },
        relations: ['contributions'],
      });
    });
  });

  describe('findSaleById', () => {
    it('should return sale when found', async () => {
      saleRepository.findOne.mockResolvedValue(mockSale);

      const result = await service.findSaleById('sale123');

      expect(result).toEqual(mockSale);
      expect(saleRepository.findOne).toHaveBeenCalledWith({
        where: { id: 'sale123' },
        relations: ['contributions'],
      });
    });

    it('should throw error when sale not found', async () => {
      saleRepository.findOne.mockResolvedValue(null);

      await expect(service.findSaleById('nonexistent')).rejects.toThrow(
        new NotFoundException('Sale with ID nonexistent not found'),
      );
    });
  });

  describe('getUserAllocation', () => {
    it('should return allocation when sale meets soft cap', async () => {
      const saleWithHighContributions = {
        ...mockEndedSale,
        contributions: [
          { ...mockContribution, amount: 2000 },
          {
            ...mockContribution,
            id: 'contribution456',
            userId: 'user456',
            amount: 1000,
          },
        ],
      };

      saleRepository.findOne.mockResolvedValue(saleWithHighContributions);
      contributionRepository.findOne.mockResolvedValue(mockContribution);

      const result = await service.getUserAllocation('user123', 'sale123');

      expect(result).toEqual({
        saleId: 'sale123',
        userId: 'user123',
        contributionAmount: 500,
        allocationAmount: 833.3333333333333, // (500/3000) * 5000 (hardCap)
        allocationPercentage: 16.666666666666664,
        saleStatus: SaleStatus.ENDED,
        saleEnded: true,
      });
    });

    it('should return zero allocation when sale does not meet soft cap', async () => {
      const saleWithLowContributions = {
        ...mockEndedSale,
        contributions: [{ ...mockContribution, amount: 500 }], // Below soft cap of 1000
      };

      saleRepository.findOne.mockResolvedValue(saleWithLowContributions);
      contributionRepository.findOne.mockResolvedValue(mockContribution);

      const result = await service.getUserAllocation('user123', 'sale123');

      expect(result).toEqual({
        saleId: 'sale123',
        userId: 'user123',
        contributionAmount: 500,
        allocationAmount: 0,
        allocationPercentage: 0,
        saleStatus: SaleStatus.ENDED,
        saleEnded: true,
      });
    });

    it('should throw error when sale has not ended', async () => {
      saleRepository.findOne.mockResolvedValue(mockSale); // Still active

      await expect(
        service.getUserAllocation('user123', 'sale123'),
      ).rejects.toThrow(new BadRequestException('Sale has not ended yet'));
    });

    it('should throw error when user has not contributed', async () => {
      saleRepository.findOne.mockResolvedValue(mockEndedSale);
      contributionRepository.findOne.mockResolvedValue(null);

      await expect(
        service.getUserAllocation('user123', 'sale123'),
      ).rejects.toThrow(
        new BadRequestException('User has not contributed to this sale'),
      );
    });
  });

  describe('claimTokens', () => {
    it('should claim tokens successfully when trustline exists', async () => {
      const saleWithHighContributions = {
        ...mockEndedSale,
        contributions: [
          { ...mockContribution, amount: 2000 },
          {
            ...mockContribution,
            id: 'contribution456',
            userId: 'user456',
            amount: 1000,
          },
        ],
      };

      saleRepository.findOne.mockResolvedValue(saleWithHighContributions);
      contributionRepository.findOne.mockResolvedValue(mockContribution);
      usersService.findOne.mockResolvedValue(mockUser);
      xrplService.checkTrustline.mockResolvedValue(true);
      xrplService.issueTokens.mockResolvedValue({
        success: true,
        transactionHash: 'txHash123',
      });

      const result = await service.claimTokens('user123', 'sale123');

      expect(result).toEqual({
        success: true,
        message: 'Successfully claimed 833.3333333333333 TEST',
        transactionHash: 'txHash123',
      });

      expect(xrplService.checkTrustline).toHaveBeenCalledWith(
        mockUser.xrplAddress,
        mockSale.collectionAddress,
      );

      expect(xrplService.issueTokens).toHaveBeenCalledWith(
        mockUser.xrplAddress,
        mockSale.collectionAddress,
        833.3333333333333,
        mockSale.symbol,
      );

      expect(contributionRepository.save).toHaveBeenCalledWith({
        ...mockContribution,
        status: 'claimed',
      });
    });

    it('should throw error when sale has not ended', async () => {
      saleRepository.findOne.mockResolvedValue(mockSale); // Still active

      await expect(service.claimTokens('user123', 'sale123')).rejects.toThrow(
        new BadRequestException('Sale has not ended yet'),
      );
    });

    it('should throw error when user has not contributed', async () => {
      saleRepository.findOne.mockResolvedValue(mockEndedSale);
      contributionRepository.findOne.mockResolvedValue(null);

      await expect(service.claimTokens('user123', 'sale123')).rejects.toThrow(
        new BadRequestException('User has not contributed to this sale'),
      );
    });

    it('should throw error when tokens already claimed', async () => {
      saleRepository.findOne.mockResolvedValue(mockEndedSale);
      contributionRepository.findOne.mockResolvedValue(mockClaimedContribution);

      await expect(service.claimTokens('user123', 'sale123')).rejects.toThrow(
        new BadRequestException('Tokens have already been claimed'),
      );
    });

    it('should throw error when user XRPL address not found', async () => {
      saleRepository.findOne.mockResolvedValue(mockEndedSale);
      contributionRepository.findOne.mockResolvedValue({
        id: 'contribution123',
        userId: 'user123',
        saleId: 'sale123',
        amount: 500,
        txHash: 'txHash123',
        status: 'pending',
        createdAt: new Date(),
        updatedAt: new Date(),
        user: mockUser,
        sale: mockSale,
      } as Contribution);
      usersService.findOne.mockResolvedValue({
        ...mockUser,
        xrplAddress: null as any,
      });

      await expect(service.claimTokens('user123', 'sale123')).rejects.toThrow(
        new BadRequestException('User XRPL address not found'),
      );
    });

    it('should throw error when no tokens to claim (sale failed)', async () => {
      const saleWithLowContributions = {
        ...mockEndedSale,
        contributions: [{ ...mockContribution, amount: 500 }], // Below soft cap
      };

      saleRepository.findOne.mockResolvedValue(saleWithLowContributions);
      contributionRepository.findOne.mockResolvedValue({
        id: 'contribution123',
        userId: 'user123',
        saleId: 'sale123',
        amount: 500,
        txHash: 'txHash123',
        status: 'pending',
        createdAt: new Date(),
        updatedAt: new Date(),
        user: mockUser,
        sale: mockSale,
      } as Contribution);
      usersService.findOne.mockResolvedValue(mockUser);

      await expect(service.claimTokens('user123', 'sale123')).rejects.toThrow(
        new BadRequestException(
          'No tokens to claim - sale did not meet soft cap',
        ),
      );
    });

    it('should throw error when trustline does not exist', async () => {
      const saleWithHighContributions = {
        ...mockEndedSale,
        contributions: [
          { ...mockContribution, amount: 2000 },
          {
            ...mockContribution,
            id: 'contribution456',
            userId: 'user456',
            amount: 1000,
          },
        ],
      };

      saleRepository.findOne.mockResolvedValue(saleWithHighContributions);
      contributionRepository.findOne.mockResolvedValue({
        id: 'contribution123',
        userId: 'user123',
        saleId: 'sale123',
        amount: 500,
        txHash: 'txHash123',
        status: 'pending',
        createdAt: new Date(),
        updatedAt: new Date(),
        user: mockUser,
        sale: mockSale,
      } as Contribution);
      usersService.findOne.mockResolvedValue(mockUser);
      xrplService.checkTrustline.mockResolvedValue(false);

      await expect(service.claimTokens('user123', 'sale123')).rejects.toThrow(
        new BadRequestException(
          'Trustline to issuer not found. Please set up a trustline before claiming tokens.',
        ),
      );

      expect(xrplService.checkTrustline).toHaveBeenCalledWith(
        mockUser.xrplAddress,
        mockSale.collectionAddress,
      );
    });

    it('should handle XRPL token issuance errors', async () => {
      const saleWithHighContributions = {
        ...mockEndedSale,
        contributions: [
          { ...mockContribution, amount: 2000 },
          {
            ...mockContribution,
            id: 'contribution456',
            userId: 'user456',
            amount: 1000,
          },
        ],
      };

      saleRepository.findOne.mockResolvedValue(saleWithHighContributions);
      contributionRepository.findOne.mockResolvedValue({
        id: 'contribution123',
        userId: 'user123',
        saleId: 'sale123',
        amount: 500,
        txHash: 'txHash123',
        status: 'pending',
        createdAt: new Date(),
        updatedAt: new Date(),
        user: mockUser,
        sale: mockSale,
      } as Contribution);
      usersService.findOne.mockResolvedValue(mockUser);
      xrplService.checkTrustline.mockResolvedValue(true);
      xrplService.issueTokens.mockRejectedValue(
        new Error('XRPL issuance failed'),
      );

      await expect(service.claimTokens('user123', 'sale123')).rejects.toThrow(
        new BadRequestException(
          'Failed to issue tokens. Please try again later.',
        ),
      );
    });
  });

  describe('processContributionEvent', () => {
    it('should process contribution event successfully', async () => {
      const contributionEvent: SaleContributionEvent = {
        saleId: 'sale123',
        collectionAddress: 'rTestCollectionAddress123456789',
        destinationTag: 12345,
        transactionHash: 'txHash123',
        sender: 'rTestSenderAddress123456789',
        amount: 100,
        timestamp: new Date(),
      };

      // Create a mock sale that is currently active
      const activeSale = {
        ...mockSale,
        start: new Date(Date.now() - 86400000), // Started yesterday
        end: new Date(Date.now() + 86400000), // Ends tomorrow
        status: SaleStatus.ACTIVE,
      };

      // Mock the service method findSaleById
      jest.spyOn(service, 'findSaleById').mockResolvedValue(activeSale);

      // Mock the user lookup by XRPL address
      usersService.findByXrplAddress.mockResolvedValue(mockUser);

      contributionRepository.create.mockReturnValue({
        ...mockContribution,
        userId: mockUser.id,
        amount: contributionEvent.amount,
        txHash: contributionEvent.transactionHash,
        status: 'pending_verification',
      });
      contributionRepository.save.mockResolvedValue({
        ...mockContribution,
        userId: mockUser.id,
        amount: contributionEvent.amount,
        txHash: contributionEvent.transactionHash,
        status: 'pending_verification',
      });

      await service.processContributionEvent(contributionEvent);

      expect(service.findSaleById).toHaveBeenCalledWith(
        contributionEvent.saleId,
      );

      expect(usersService.findByXrplAddress).toHaveBeenCalledWith(
        contributionEvent.sender,
      );

      expect(contributionRepository.create).toHaveBeenCalledWith({
        userId: mockUser.id,
        saleId: contributionEvent.saleId,
        amount: contributionEvent.amount,
        txHash: contributionEvent.transactionHash,
        status: 'pending_verification',
      });

      expect(contributionRepository.save).toHaveBeenCalled();
    });

    it('should create new contribution when user has not contributed before', async () => {
      const contributionEvent: SaleContributionEvent = {
        saleId: 'sale123',
        collectionAddress: 'rTestCollectionAddress123456789',
        destinationTag: 12345,
        transactionHash: 'txHash123',
        sender: 'rTestSenderAddress123456789',
        amount: 100,
        timestamp: new Date(),
      };

      // Create a mock sale that is currently active
      const activeSale = {
        ...mockSale,
        start: new Date(Date.now() - 86400000), // Started yesterday
        end: new Date(Date.now() + 86400000), // Ends tomorrow
        status: SaleStatus.ACTIVE,
      };

      // Mock the service method findSaleById
      jest.spyOn(service, 'findSaleById').mockResolvedValue(activeSale);

      // Mock the user lookup by XRPL address
      usersService.findByXrplAddress.mockResolvedValue(mockUser);

      contributionRepository.create.mockReturnValue({
        ...mockContribution,
        userId: mockUser.id,
        amount: contributionEvent.amount,
        txHash: contributionEvent.transactionHash,
        status: 'pending_verification',
      });
      contributionRepository.save.mockResolvedValue({
        ...mockContribution,
        userId: mockUser.id,
        amount: contributionEvent.amount,
        txHash: contributionEvent.transactionHash,
        status: 'pending_verification',
      });

      await service.processContributionEvent(contributionEvent);

      expect(service.findSaleById).toHaveBeenCalledWith(
        contributionEvent.saleId,
      );

      expect(usersService.findByXrplAddress).toHaveBeenCalledWith(
        contributionEvent.sender,
      );

      expect(contributionRepository.create).toHaveBeenCalledWith({
        userId: mockUser.id,
        saleId: contributionEvent.saleId,
        amount: contributionEvent.amount,
        txHash: contributionEvent.transactionHash,
        status: 'pending_verification',
      });

      expect(contributionRepository.save).toHaveBeenCalled();
    });
  });
});
