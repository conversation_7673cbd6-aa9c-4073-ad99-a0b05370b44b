import {
  Component,
  ChangeDetectionStrategy,
  inject,
  OnInit,
  OnDestroy,
  computed,
  signal,
  HostListener,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';
import { RouterModule, Router } from '@angular/router';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { ApiService } from './services/api/api.service';
import { User } from './types';
import { ThemeToggleComponent } from './components/theme-toggle/theme-toggle.component';

@Component({
  selector: 'app-root',
  templateUrl: 'app.component.html',
  styleUrls: ['app.component.scss'],
  standalone: true,
  imports: [CommonModule, IonicModule, RouterModule, ThemeToggleComponent],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AppComponent implements OnInit, OnDestroy {
  private apiService = inject(ApiService);
  private router = inject(Router);
  private destroy$ = new Subject<void>();

  public currentUser = signal<User | null>(null);
  public isAdmin = computed(() => this.currentUser()?.role === 'admin');

  public appPages = computed(() => {
    const basePages = [
      { title: 'Dashboard', url: '/dashboard', icon: 'home' },
      { title: 'Launchpad', url: '/launchpad', icon: 'rocket' },
      { title: 'Connect XRPL Account', url: '/xrpl-connect', icon: 'link' },
    ];

    if (this.isAdmin()) {
      basePages.push({
        title: 'Admin Sales',
        url: '/admin/sales',
        icon: 'settings',
      });
    }

    // Show "Log in" if not authenticated, "Logout" if authenticated
    const authTitle = this.currentUser() ? 'Logout' : 'Log in';
    basePages.push({ title: authTitle, url: '/login', icon: 'log-out' });
    return basePages;
  });

  public labels = ['Settings', 'Help', 'About'];

  ngOnInit() {
    // Subscribe to user changes to update menu when user logs in/out
    this.apiService.currentUser$.pipe(takeUntil(this.destroy$)).subscribe({
      next: user => this.currentUser.set(user),
      error: error => console.error('Error loading user:', error),
    });

    // Load current user data if not already loaded
    this.apiService
      .getMeIfNeeded()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: user => this.currentUser.set(user),
        error: error => console.error('Error loading user:', error),
      });
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  onMenuClick(page: { title: string; url: string; icon: string }) {
    if (page.title === 'Logout') {
      this.logout();
    } else if (page.title === 'Log in') {
      this.router.navigate(['/login']);
    } else {
      this.router.navigate([page.url]);
    }
  }

  private logout() {
    this.apiService
      .logout()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          // currentUser will be updated automatically via the currentUser$ subscription
          this.router.navigate(['/login']);
        },
        error: error => {
          console.error('Logout error:', error);
          this.router.navigate(['/login']);
        },
      });
  }

  // Keyboard navigation support
  @HostListener('keydown', ['$event'])
  onKeyDown(event: KeyboardEvent): void {
    // Handle keyboard shortcuts
    if (event.ctrlKey || event.metaKey) {
      switch (event.key) {
        case 'h':
          event.preventDefault();
          this.router.navigate(['/dashboard']);
          break;
        case 'l':
          event.preventDefault();
          this.router.navigate(['/launchpad']);
          break;
        case 'x':
          event.preventDefault();
          this.router.navigate(['/xrpl-connect']);
          break;
      }
    }

    // Handle Enter key on focused elements
    if (event.key === 'Enter' && event.target instanceof HTMLElement) {
      const target = event.target as HTMLElement;
      if (
        target.hasAttribute('tabindex') &&
        target.getAttribute('tabindex') !== '-1'
      ) {
        target.click();
      }
    }
  }
}
