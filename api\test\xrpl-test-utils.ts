import { Subject } from 'rxjs';
import { Payment } from 'xrpl';

// Mock XRPL Client for consistent testing
export const createMockXrplClient = () => ({
  connect: jest.fn().mockResolvedValue(undefined),
  disconnect: jest.fn().mockResolvedValue(undefined),
  isConnected: jest.fn().mockReturnValue(true),
  on: jest.fn(),
  request: jest.fn(),
  submit: jest.fn(),
  submitAndWait: jest.fn(),
  autofill: jest.fn(),
});

// Mock XRPL Wallet
export const createMockWallet = (
  address: string = 'rTestWalletAddress123456789',
) => ({
  address,
  sign: jest.fn().mockReturnValue({ tx_blob: 'signedTransactionBlob' }),
});

// Mock XRPL Payment transaction
export const createMockPayment = (
  overrides: Partial<Payment> = {},
): Payment => ({
  TransactionType: 'Payment',
  Account: 'rTestSourceAddress123456789',
  Destination: 'rTestDestAddress123456789',
  Amount: '1000000', // 1 XRP in drops
  Fee: '10000', // 0.01 XRP in drops
  Sequence: 1,
  LastLedgerSequence: 100,
  ...overrides,
});

// Mock account info response
export const createMockAccountInfo = (
  balance: string = '**********',
  sequence: number = 123,
) => ({
  result: {
    account_data: {
      Balance: balance,
      Sequence: sequence,
    },
    ledger_index: 12345,
  },
});

// Mock server info response
export const createMockServerInfo = (ledgerRange: string = '1-1000') => ({
  result: {
    info: {
      complete_ledgers: ledgerRange,
    },
  },
});

// Mock account lines response for trustline checking
export const createMockAccountLines = (
  lines: Array<{ account: string; limit_peer: string }> = [],
) => ({
  result: {
    lines: lines.length > 0 ? lines : null,
  },
});

// Mock account transactions response
export const createMockAccountTransactions = (
  transactions: Array<any> = [],
) => ({
  result: {
    transactions:
      transactions.length > 0
        ? transactions
        : [
            {
              tx: {
                hash: 'txHash123',
                Account: 'rTestAddress123456789',
                Destination: 'rDestAddress123456789',
                Amount: '1000000',
                Fee: '10000',
                ledger_index: 12345,
                date: *********, // Ripple epoch
                TransactionType: 'Payment',
                Memos: [
                  {
                    Memo: {
                      MemoData: '74657374206d656d6f', // "test memo" in hex
                    },
                  },
                ],
              },
            },
          ],
  },
});

// Mock transaction submission response
export const createMockTransactionResult = (
  hash: string = 'txHash123',
  result: string = 'tesSUCCESS',
) => ({
  result: {
    hash,
    meta: {
      TransactionResult: result,
    },
  },
});

// Mock sale contribution event
export const createMockSaleContributionEvent = (
  overrides: Partial<any> = {},
) => ({
  saleId: 'sale123',
  collectionAddress: 'rTestCollectionAddress123456789',
  destinationTag: 12345,
  transactionHash: 'txHash123',
  sender: 'rTestSenderAddress123456789',
  amount: 100,
  timestamp: new Date(),
  ...overrides,
});

// Mock sale progress update
export const createMockSaleProgressUpdate = (overrides: Partial<any> = {}) => ({
  saleId: 'sale123',
  totalRaised: 1000,
  progressPercent: 20,
  totalContributors: 5,
  lastContribution: {
    amount: 100,
    sender: 'user123',
    timestamp: new Date(),
  },
  ...overrides,
});

// Mock XRPL connection status
export const createMockConnectionStatus = (overrides: Partial<any> = {}) => ({
  isConnected: true,
  connectionAttempts: 0,
  reconnectDelay: 1000,
  lastConnected: new Date(),
  lastError: '',
  ...overrides,
});

// Mock XRPL subscription subject
export const createMockSubscription = <T>() => new Subject<T>();

// Mock XRPL client with specific responses
export const createMockXrplClientWithResponses = (
  responses: Record<string, any>,
) => {
  const mockClient = createMockXrplClient();

  // Set up specific responses for different request types
  mockClient.request.mockImplementation((command: any) => {
    if (command.command === 'account_info') {
      return Promise.resolve(responses.accountInfo || createMockAccountInfo());
    }
    if (command.command === 'server_info') {
      return Promise.resolve(responses.serverInfo || createMockServerInfo());
    }
    if (command.command === 'account_lines') {
      return Promise.resolve(
        responses.accountLines || createMockAccountLines(),
      );
    }
    if (command.command === 'account_tx') {
      return Promise.resolve(
        responses.accountTx || createMockAccountTransactions(),
      );
    }
    if (command.command === 'ping') {
      return Promise.resolve(responses.ping || { result: 'pong' });
    }
    if (command.command === 'subscribe') {
      return Promise.resolve(responses.subscribe || { result: 'subscribed' });
    }
    if (command.command === 'unsubscribe') {
      return Promise.resolve(
        responses.unsubscribe || { result: 'unsubscribed' },
      );
    }

    // Default response
    return Promise.resolve(responses.default || { result: 'success' });
  });

  return mockClient;
};

// Mock XRPL service with common methods
export const createMockXrplService = () => ({
  getBalance: jest.fn(),
  getTransactions: jest.fn(),
  subscribeToAccount: jest.fn(),
  unsubscribeFromAccount: jest.fn(),
  prepareSendTransaction: jest.fn(),
  subscribeToSale: jest.fn(),
  unsubscribeFromSale: jest.fn(),
  checkTrustline: jest.fn(),
  issueTokens: jest.fn(),
  request: jest.fn(),
  submit: jest.fn(),
  submitAndWait: jest.fn(),
  getConnectionStatus: jest.fn(),
  isConnected: jest.fn(),
  getClient: jest.fn(),
  getActiveSales: jest.fn(),
  getSaleSubscriptionCount: jest.fn(),
});

// Mock launchpad service with common methods
export const createMockLaunchpadService = () => ({
  findSaleById: jest.fn(),
  findAllSales: jest.fn(),
  findActiveSales: jest.fn(),
  createSale: jest.fn(),
  updateSale: jest.fn(),
  deleteSale: jest.fn(),
  getUserAllocation: jest.fn(),
  claimTokens: jest.fn(),
  processContributionEvent: jest.fn(),
  updateSaleStatus: jest.fn(),
});

// Mock users service with common methods
export const createMockUsersService = () => ({
  findOne: jest.fn(),
  findByXrplAddress: jest.fn(),
  create: jest.fn(),
  update: jest.fn(),
  remove: jest.fn(),
});

// Helper to create a mock user
export const createMockUser = (overrides: Partial<any> = {}) => ({
  id: 'user123',
  email: '<EMAIL>',
  username: 'testuser',
  xrplAddress: 'rTestUserAddress123456789',
  password: 'hashedPassword',
  role: 'user',
  createdAt: new Date(),
  updatedAt: new Date(),
  ...overrides,
});

// Helper to create a mock sale
export const createMockSale = (overrides: Partial<any> = {}) => ({
  id: 'sale123',
  name: 'Test Sale',
  symbol: 'TEST',
  description: 'Test sale description',
  price: 0.1,
  softCap: 1000,
  hardCap: 5000,
  start: new Date(Date.now() + 86400000), // Tomorrow
  end: new Date(Date.now() + 172800000), // Day after tomorrow
  status: 'ACTIVE',
  collectionAddress: 'rTestCollectionAddress123456789',
  createdAt: new Date(),
  updatedAt: new Date(),
  contributions: [],
  ...overrides,
});

// Helper to create a mock contribution
export const createMockContribution = (overrides: Partial<any> = {}) => ({
  id: 'contribution123',
  userId: 'user123',
  saleId: 'sale123',
  amount: 500,
  status: 'pending',
  createdAt: new Date(),
  updatedAt: new Date(),
  user: createMockUser(),
  sale: createMockSale(),
  ...overrides,
});

// Helper to create a mock socket
export const createMockSocket = (overrides: Partial<any> = {}) => ({
  id: 'socket123',
  emit: jest.fn(),
  join: jest.fn().mockResolvedValue(undefined),
  leave: jest.fn().mockResolvedValue(undefined),
  user: createMockUser(),
  ...overrides,
});

// Helper to create a mock server
export const createMockServer = () => ({
  to: jest.fn().mockReturnThis(),
  emit: jest.fn(),
  in: jest.fn().mockReturnThis(),
  sockets: {
    emit: jest.fn(),
  },
});

// Helper to wait for async operations
export const waitFor = (ms: number) =>
  new Promise((resolve) => setTimeout(resolve, ms));

// Helper to create a mock repository
export const createMockRepository = () => ({
  create: jest.fn(),
  save: jest.fn(),
  find: jest.fn(),
  findOne: jest.fn(),
  update: jest.fn(),
  remove: jest.fn(),
  count: jest.fn(),
  query: jest.fn(),
});

// Helper to create a mock config service
export const createMockConfigService = (config: Record<string, any> = {}) => ({
  get: jest.fn((key: string) => config[key]),
});

// Helper to create a mock logger
export const createMockLogger = () => ({
  log: jest.fn(),
  error: jest.fn(),
  warn: jest.fn(),
  debug: jest.fn(),
  verbose: jest.fn(),
});

// Helper to create a mock JWT payload
export const createMockJwtPayload = (overrides: Partial<any> = {}) => ({
  sub: 'user123',
  email: '<EMAIL>',
  username: 'testuser',
  role: 'user',
  iat: Math.floor(Date.now() / 1000),
  exp: Math.floor(Date.now() / 1000) + 3600,
  ...overrides,
});

// Helper to create a mock request object
export const createMockRequest = (overrides: Partial<any> = {}) => ({
  headers: {
    authorization: 'Bearer mock-jwt-token',
  },
  user: createMockJwtPayload(),
  ...overrides,
});

// Helper to create a mock response object
export const createMockResponse = () => ({
  status: jest.fn().mockReturnThis(),
  json: jest.fn().mockReturnThis(),
  send: jest.fn().mockReturnThis(),
  setHeader: jest.fn().mockReturnThis(),
});

// Helper to create a mock next function
export const createMockNext = () => jest.fn();

// Helper to create a mock exception filter
export const createMockExceptionFilter = () => ({
  catch: jest.fn(),
});

// Helper to create a mock interceptor
export const createMockInterceptor = () => ({
  intercept: jest.fn(),
});

// Helper to create a mock guard
export const createMockGuard = () => ({
  canActivate: jest.fn(),
});

// Helper to create a mock strategy
export const createMockStrategy = () => ({
  validate: jest.fn(),
});

// Helper to create a mock validator
export const createMockValidator = () => ({
  validate: jest.fn(),
});

// Helper to create a mock filter
export const createMockFilter = () => ({
  filter: jest.fn(),
});

// Helper to create a mock decorator
export const createMockDecorator = () => jest.fn();

// Helper to create a mock module
export const createMockModule = () => ({
  providers: [],
  controllers: [],
  imports: [],
  exports: [],
});

// Helper to create a mock application
export const createMockApplication = () => ({
  listen: jest.fn(),
  close: jest.fn(),
  get: jest.fn(),
  use: jest.fn(),
  enableCors: jest.fn(),
  setGlobalPrefix: jest.fn(),
  useGlobalPipes: jest.fn(),
  useGlobalFilters: jest.fn(),
  useGlobalInterceptors: jest.fn(),
  useGlobalGuards: jest.fn(),
});

// Helper to create a mock testing module
export const createMockTestingModule = () => ({
  compile: jest.fn(),
  get: jest.fn(),
  select: jest.fn(),
  resolve: jest.fn(),
  overrideProvider: jest.fn(),
  overrideGuard: jest.fn(),
  overrideInterceptor: jest.fn(),
  overrideFilter: jest.fn(),
  overridePipe: jest.fn(),
  overrideModule: jest.fn(),
});

// Helper to create a mock test suite
export const createMockTestSuite = () => ({
  describe: jest.fn(),
  it: jest.fn(),
  beforeEach: jest.fn(),
  afterEach: jest.fn(),
  beforeAll: jest.fn(),
  afterAll: jest.fn(),
  expect: jest.fn(),
  jest: {
    fn: jest.fn(),
    mock: jest.fn(),
    spyOn: jest.fn(),
    clearAllMocks: jest.fn(),
    resetAllMocks: jest.fn(),
    restoreAllMocks: jest.fn(),
  },
});

// Export all mocks for easy access
export const XrplTestUtils = {
  createMockXrplClient,
  createMockWallet,
  createMockPayment,
  createMockAccountInfo,
  createMockServerInfo,
  createMockAccountLines,
  createMockAccountTransactions,
  createMockTransactionResult,
  createMockSaleContributionEvent,
  createMockSaleProgressUpdate,
  createMockConnectionStatus,
  createMockSubscription,
  createMockXrplClientWithResponses,
  createMockXrplService,
  createMockLaunchpadService,
  createMockUsersService,
  createMockUser,
  createMockSale,
  createMockContribution,
  createMockSocket,
  createMockServer,
  waitFor,
  createMockRepository,
  createMockConfigService,
  createMockLogger,
  createMockJwtPayload,
  createMockRequest,
  createMockResponse,
  createMockNext,
  createMockExceptionFilter,
  createMockInterceptor,
  createMockGuard,
  createMockStrategy,
  createMockValidator,
  createMockFilter,
  createMockDecorator,
  createMockModule,
  createMockApplication,
  createMockTestingModule,
  createMockTestSuite,
};

export default XrplTestUtils;
