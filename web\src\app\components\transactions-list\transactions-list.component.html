<ion-card class="transactions-card">
  <ion-card-header>
    <ion-card-title>
      <ion-icon name="swap-horizontal-outline"></ion-icon>
      Recent Transactions
    </ion-card-title>
    <ion-button
      fill="clear"
      size="small"
      (click)="onViewAllClick()"
      aria-label="View all transactions"
    >
      View All
    </ion-button>
  </ion-card-header>
  <ion-card-content>
    @if (transactions) {
      @if (transactions.length > 0) {
        <ion-list>
          @for (transaction of transactions; track transaction.hash) {
            <ion-item
              button
              (click)="onViewDetailsClick(transaction)"
              [attr.aria-label]="
                'View transaction details for ' +
                transaction.type +
                ' of ' +
                transaction.amount +
                ' ' +
                transaction.currency
              "
            >
              <ion-icon
                [name]="
                  transaction.type === 'Payment'
                    ? isOutgoingPayment(transaction)
                      ? 'arrow-back'
                      : 'arrow-forward'
                    : 'swap-horizontal'
                "
                slot="start"
                [color]="
                  getStatusColor(
                    transaction.validated ? 'validated' : 'pending'
                  )
                "
                aria-hidden="true"
              ></ion-icon>
              <ion-label>
                <h3>{{ transaction.type }}</h3>
                <p class="transaction-amount">
                  {{ transaction.amount }} {{ transaction.currency }}
                </p>
                <p class="transaction-account">
                  <ion-icon
                    name="person-outline"
                    size="small"
                    aria-hidden="true"
                  ></ion-icon>
                  From: {{ formatAddress(transaction.account) }}
                </p>
                <p class="transaction-destination">
                  <ion-icon
                    name="arrow-forward-outline"
                    size="small"
                    aria-hidden="true"
                  ></ion-icon>
                  To: {{ formatAddress(transaction.destination) }}
                </p>
                @if (transaction.memo) {
                  <p class="transaction-memo">
                    <ion-icon
                      name="document-text-outline"
                      size="small"
                      aria-hidden="true"
                    ></ion-icon>
                    {{ transaction.memo }}
                  </p>
                }
                <p class="transaction-details">
                  <span class="transaction-fee"
                    >Fee: {{ transaction.fee }} XRP</span
                  >
                  <span class="transaction-date">{{
                    transaction.date | date: 'short'
                  }}</span>
                </p>
              </ion-label>
              <div slot="end" class="transaction-status">
                <ion-badge
                  [color]="
                    getStatusColor(
                      transaction.validated ? 'validated' : 'pending'
                    )
                  "
                  class="status-badge"
                  color="white"
                >
                  {{ transaction.validated ? 'Confirmed' : 'Pending' }}
                </ion-badge>
                <ion-button
                  fill="clear"
                  size="small"
                  (click)="onCopyHashClick(transaction.hash, $event)"
                  aria-label="Copy transaction hash"
                >
                  <ion-icon name="copy-outline" aria-hidden="true"></ion-icon>
                </ion-button>
              </div>
            </ion-item>
          }
        </ion-list>
      } @else {
        <div class="empty-state">
          <ion-icon name="swap-horizontal-outline" size="large"></ion-icon>
          <p>No transactions yet</p>
        </div>
      }
    } @else {
      <ion-skeleton-text animated></ion-skeleton-text>
    }
  </ion-card-content>
</ion-card>
