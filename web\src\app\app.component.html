<ion-app>
  <!-- Skip to content link for accessibility -->
  <a href="#main-content" class="skip-link">Skip to main content</a>

  <ion-split-pane contentId="main-content">
    <ion-menu contentId="main-content" type="overlay">
      <ion-content>
        <ion-list id="menu-list">
          <ion-list-header>XRPL Launchpad</ion-list-header>
          <ion-note>Welcome to the future of token launches</ion-note>

          @for (p of appPages(); track p; let i = $index) {
            <ion-menu-toggle auto-hide="false">
              @if (p.title === 'Logout' || p.title === 'Log in') {
                <ion-item
                  (click)="onMenuClick(p)"
                  lines="none"
                  detail="false"
                  button
                  routerLinkActive="selected"
                  [attr.aria-label]="p.title"
                  tabindex="0"
                >
                  <ion-icon
                    aria-hidden="true"
                    slot="start"
                    [ios]="p.icon + '-outline'"
                    [md]="p.icon + '-sharp'"
                  ></ion-icon>
                  <ion-label>{{ p.title }}</ion-label>
                </ion-item>
              } @else {
                <ion-item
                  routerDirection="root"
                  [routerLink]="[p.url]"
                  lines="none"
                  detail="false"
                  routerLinkActive="selected"
                  [attr.aria-label]="p.title"
                  tabindex="0"
                >
                  <ion-icon
                    aria-hidden="true"
                    slot="start"
                    [ios]="p.icon + '-outline'"
                    [md]="p.icon + '-sharp'"
                  ></ion-icon>
                  <ion-label>{{ p.title }}</ion-label>
                </ion-item>
              }
            </ion-menu-toggle>
          }
        </ion-list>

        <ion-list id="labels-list">
          <ion-list-header>Support</ion-list-header>

          @for (label of labels; track label) {
            <ion-item lines="none" [attr.aria-label]="label" tabindex="0">
              <ion-icon
                aria-hidden="true"
                slot="start"
                ios="information-circle-outline"
                md="information-circle-sharp"
              ></ion-icon>
              <ion-label>{{ label }}</ion-label>
            </ion-item>
          }
        </ion-list>

        <!-- Theme Toggle Section -->
        <ion-list id="theme-list">
          <ion-list-header>Appearance</ion-list-header>
          <ion-item lines="none" aria-label="Theme settings">
            <ion-icon
              aria-hidden="true"
              slot="start"
              name="color-palette-outline"
            ></ion-icon>
            <ion-label>Theme</ion-label>
            <app-theme-toggle slot="end"></app-theme-toggle>
          </ion-item>
        </ion-list>
      </ion-content>
    </ion-menu>

    <ion-router-outlet id="main-content"></ion-router-outlet>
  </ion-split-pane>
</ion-app>
