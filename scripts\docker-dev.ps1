# PowerShell script for Docker development operations

Write-Host "XRPL Launchpad Docker Development Script" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

# Function to show usage
function Show-Usage {
    Write-Host "Usage: .\scripts\docker-dev.ps1 [command]" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "Commands:" -ForegroundColor Yellow
    Write-Host "  prod     - Run production build (API + Web + DB)"
    Write-Host "  dev      - Run development build with live reload (API + Web-Dev + DB)"
    Write-Host "  api      - Run only API and DB"
    Write-Host "  web      - Run only Web and DB"
    Write-Host "  db       - Run only database"
    Write-Host "  stop     - Stop all services"
    Write-Host "  clean    - Stop and remove all containers/volumes"
    Write-Host "  logs     - Show logs for all services"
    Write-Host "  status   - Show status of all services"
}

# Function to run production
function Start-Production {
    Write-Host "Starting production environment..." -ForegroundColor Green
    docker-compose up -d
    Write-Host "Production environment started!" -ForegroundColor Green
    Write-Host "API: http://localhost:3000" -ForegroundColor Cyan
    Write-Host "Web: http://localhost:80" -ForegroundColor Cyan
    Write-Host "Web (alt): http://localhost:8100" -ForegroundColor Cyan
}

# Function to run development
function Start-Development {
    Write-Host "Starting development environment..." -ForegroundColor Green
    docker-compose --profile dev up -d
    Write-Host "Development environment started!" -ForegroundColor Green
    Write-Host "API: http://localhost:3000" -ForegroundColor Cyan
    Write-Host "Web Dev: http://localhost:8100" -ForegroundColor Cyan
}

# Function to run API only
function Start-API {
    Write-Host "Starting API and database..." -ForegroundColor Green
    docker-compose up -d db api
    Write-Host "API and database started!" -ForegroundColor Green
    Write-Host "API: http://localhost:3000" -ForegroundColor Cyan
}

# Function to run Web only
function Start-Web {
    Write-Host "Starting Web and database..." -ForegroundColor Green
    docker-compose up -d db web
    Write-Host "Web and database started!" -ForegroundColor Green
    Write-Host "Web: http://localhost:80" -ForegroundColor Cyan
}

# Function to run DB only
function Start-DB {
    Write-Host "Starting database only..." -ForegroundColor Green
    docker-compose up -d db
    Write-Host "Database started!" -ForegroundColor Green
}

# Function to stop all services
function Stop-All {
    Write-Host "Stopping all services..." -ForegroundColor Yellow
    docker-compose down
    Write-Host "All services stopped!" -ForegroundColor Green
}

# Function to clean everything
function Clean-All {
    Write-Host "Cleaning all containers and volumes..." -ForegroundColor Red
    docker-compose down -v --remove-orphans
    docker system prune -f
    Write-Host "Cleanup completed!" -ForegroundColor Green
}

# Function to show logs
function Show-Logs {
    Write-Host "Showing logs for all services..." -ForegroundColor Green
    docker-compose logs -f
}

# Function to show status
function Show-Status {
    Write-Host "Service Status:" -ForegroundColor Green
    docker-compose ps
}

# Main script logic
param(
    [string]$Command = ""
)

if ($Command -eq "") {
    Show-Usage
    exit 0
}

switch ($Command.ToLower()) {
    "prod" { Start-Production }
    "dev" { Start-Development }
    "api" { Start-API }
    "web" { Start-Web }
    "db" { Start-DB }
    "stop" { Stop-All }
    "clean" { Clean-All }
    "logs" { Show-Logs }
    "status" { Show-Status }
    default {
        Write-Host "Unknown command: $Command" -ForegroundColor Red
        Show-Usage
    }
}
