/*
 * App Global CSS
 * ----------------------------------------------------------------------------
 * Put style rules here that you want to apply globally. These styles are for
 * the entire app and not just one component. Additionally, this file can be
 * used as an entry point to import other CSS/Sass files to be included in the
 * output CSS.
 * For more information on global stylesheets, visit the documentation:
 * https://ionicframework.com/docs/layout/global-stylesheets
 */

/* Core CSS required for Ionic components to work properly */
@import '@ionic/angular/css/core.css';

/* Basic CSS for apps built with Ionic */
@import '@ionic/angular/css/normalize.css';
@import '@ionic/angular/css/structure.css';
@import '@ionic/angular/css/typography.css';
@import '@ionic/angular/css/display.css';

/* Optional CSS utils that can be commented out */
@import '@ionic/angular/css/padding.css';
@import '@ionic/angular/css/float-elements.css';
@import '@ionic/angular/css/text-alignment.css';
@import '@ionic/angular/css/text-transformation.css';
@import '@ionic/angular/css/flex-utils.css';

/**
 * Ionic Dark Mode
 * -----------------------------------------------------
 * For more info, please see:
 * https://ionicframework.com/docs/theming/dark-mode
 */

/* @import "@ionic/angular/css/palettes/dark.always.css"; */
@import '@ionic/angular/css/palettes/dark.class.css';
/* @import "@ionic/angular/css/palettes/dark.system.css"; */

/**
 * Accessibility Improvements
 * -----------------------------------------------------
 * Enhanced focus indicators and keyboard navigation
 */

/* Enhanced focus indicators */
*:focus {
  outline: 2px solid var(--ion-color-primary);
  outline-offset: 2px;
}

/* Skip to content link for screen readers */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--ion-color-primary);
  color: var(--ion-color-primary-contrast);
  padding: 8px;
  text-decoration: none;
  border-radius: 4px;
  z-index: 1000;
}

.skip-link:focus {
  top: 6px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --ion-color-primary: #0000ff;
    --ion-color-primary-contrast: #ffffff;
    --ion-color-secondary: #008000;
    --ion-color-secondary-contrast: #ffffff;
    --ion-color-tertiary: #800080;
    --ion-color-tertiary-contrast: #ffffff;
    --ion-color-success: #008000;
    --ion-color-success-contrast: #ffffff;
    --ion-color-warning: #ff8c00;
    --ion-color-warning-contrast: #000000;
    --ion-color-danger: #ff0000;
    --ion-color-danger-contrast: #ffffff;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Screen reader only content */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Focus visible for better keyboard navigation */
:focus-visible {
  outline: 2px solid var(--ion-color-primary);
  outline-offset: 2px;
}

/* Remove focus outline for mouse users */
:focus:not(:focus-visible) {
  outline: none;
}
