import {
  Component,
  ChangeDetectionStrategy,
  inject,
  OnDestroy,
  signal,
  EventEmitter,
  Output,
  Input,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';
import {
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
  AbstractControl,
  ValidationErrors,
} from '@angular/forms';
import { Subject } from 'rxjs';
import { takeUntil, finalize } from 'rxjs/operators';
import { ApiService } from '../../services/api/api.service';
import { CreateSaleRequest } from '../../types';

/**
 * Sale form component for creating new token sales.
 *
 * This component handles the creation of new token sales with comprehensive
 * validation and form management.
 */
@Component({
  selector: 'app-sale-form',
  templateUrl: './sale-form.component.html',
  styleUrls: ['./sale-form.component.scss'],
  standalone: true,
  imports: [CommonModule, IonicModule, ReactiveFormsModule],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SaleFormComponent implements OnDestroy {
  private apiService = inject(ApiService);
  private fb = inject(FormBuilder);
  private destroy$ = new Subject<void>();

  @Input() isLoading = signal<boolean>(false);
  @Output() saleCreated = new EventEmitter<any>();
  @Output() loadingChange = new EventEmitter<boolean>();

  // Form
  saleForm: FormGroup;

  // State
  isSubmitting = signal<boolean>(false);

  // XRPL address validation regex
  private readonly XRPL_ADDRESS_REGEX = /^r[a-zA-Z0-9]{24,34}$/;

  constructor() {
    this.saleForm = this.createSaleForm();
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private createSaleForm(): FormGroup {
    return this.fb.group(
      {
        symbol: [
          '',
          [
            Validators.required,
            Validators.minLength(2),
            Validators.maxLength(10),
            Validators.pattern(/^[A-Z0-9]+$/),
          ],
        ],
        price: [
          '',
          [
            Validators.required,
            Validators.min(0.000001),
            Validators.max(1000000),
          ],
        ],
        start: ['', [Validators.required]],
        end: ['', [Validators.required]],
        softCap: [
          '',
          [Validators.required, Validators.min(1), Validators.max(10000000)],
        ],
        hardCap: [
          '',
          [Validators.required, Validators.min(1), Validators.max(10000000)],
        ],
        collectionAddress: [
          '',
          [Validators.required, this.xrplAddressValidator.bind(this)],
        ],
        description: ['', [Validators.maxLength(1000)]],
        website: ['', [Validators.pattern(/^https?:\/\/.+/)]],
        whitepaper: ['', [Validators.pattern(/^https?:\/\/.+/)]],
      },
      { validators: [this.dateRangeValidator, this.capRangeValidator] }
    );
  }

  private xrplAddressValidator(
    control: AbstractControl
  ): ValidationErrors | null {
    if (!control.value) return null;
    return this.XRPL_ADDRESS_REGEX.test(control.value)
      ? null
      : { invalidXrplAddress: true };
  }

  private dateRangeValidator(
    control: AbstractControl
  ): ValidationErrors | null {
    const start = control.get('start')?.value;
    const end = control.get('end')?.value;

    if (!start || !end) return null;

    const startDate = new Date(start);
    const endDate = new Date(end);
    const now = new Date();

    if (startDate < now) {
      return { startDateInPast: true };
    }

    if (startDate >= endDate) {
      return { invalidDateRange: true };
    }

    return null;
  }

  private capRangeValidator(control: AbstractControl): ValidationErrors | null {
    const softCap = control.get('softCap')?.value;
    const hardCap = control.get('hardCap')?.value;

    if (!softCap || !hardCap) return null;

    if (softCap >= hardCap) {
      return { invalidCapRange: true };
    }

    return null;
  }

  onSubmit(): void {
    if (this.saleForm.valid && !this.isSubmitting()) {
      this.isSubmitting.set(true);

      const formValue = this.saleForm.value;
      const saleData: CreateSaleRequest = {
        symbol: formValue.symbol.toUpperCase(),
        price: parseFloat(formValue.price),
        start: new Date(formValue.start).toISOString(),
        end: new Date(formValue.end).toISOString(),
        softCap: parseFloat(formValue.softCap),
        hardCap: parseFloat(formValue.hardCap),
        collectionAddress: formValue.collectionAddress,
        description: formValue.description || undefined,
        website: formValue.website || undefined,
        whitepaper: formValue.whitepaper || undefined,
      };

      this.apiService
        .createSale(saleData)
        .pipe(
          takeUntil(this.destroy$),
          finalize(() => this.isSubmitting.set(false))
        )
        .subscribe({
          next: newSale => {
            // Reset form
            this.saleForm.reset();

            // Emit the created sale
            this.saleCreated.emit(newSale);
          },
          error: error => {
            console.error('Error creating sale:', error);
            // Error handling will be done by parent component
          },
        });
    }
  }

  getFieldError(fieldName: string): string {
    const field = this.saleForm.get(fieldName);
    if (!field || !field.errors || !field.touched) return '';

    const errors = field.errors;

    if (errors['required']) return `${fieldName} is required`;
    if (errors['minlength'])
      return `${fieldName} must be at least ${errors['minlength'].requiredLength} characters`;
    if (errors['maxlength'])
      return `${fieldName} must be no more than ${errors['maxlength'].requiredLength} characters`;
    if (errors['min'])
      return `${fieldName} must be at least ${errors['min'].min}`;
    if (errors['max'])
      return `${fieldName} must be no more than ${errors['max'].max}`;
    if (errors['pattern']) return `Invalid ${fieldName} format`;
    if (errors['invalidXrplAddress']) return 'Invalid XRPL address format';

    return 'Invalid value';
  }

  getFormError(): string {
    const errors = this.saleForm.errors;
    if (!errors || !this.saleForm.touched) return '';

    if (errors['startDateInPast']) return 'Start date must be in the future';
    if (errors['invalidDateRange']) return 'End date must be after start date';
    if (errors['invalidCapRange'])
      return 'Hard cap must be greater than soft cap';

    return 'Form validation error';
  }

  hasFieldErrors(): boolean {
    return Object.keys(this.saleForm.controls).some(
      fieldName => this.getFieldError(fieldName) !== ''
    );
  }

  getFieldsWithErrors(): string[] {
    return Object.keys(this.saleForm.controls).filter(
      fieldName => this.getFieldError(fieldName) !== ''
    );
  }
}
