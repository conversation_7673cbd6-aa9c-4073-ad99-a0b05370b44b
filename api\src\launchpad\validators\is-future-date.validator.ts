import {
  ValidatorConstraint,
  ValidatorConstraintInterface,
} from 'class-validator';

@ValidatorConstraint({ name: 'isFutureDate', async: false })
export class IsFutureDate implements ValidatorConstraintInterface {
  validate(startDate: string) {
    if (!startDate) {
      return false;
    }

    const start = new Date(startDate);
    const now = new Date();

    return start > now;
  }

  defaultMessage() {
    return 'Start date must be in the future';
  }
}
