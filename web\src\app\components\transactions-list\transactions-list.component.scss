ion-card {
  margin-bottom: 16px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  ion-card-header {
    padding: 16px 16px 8px 16px;

    ion-card-title {
      display: flex;
      align-items: center;
      font-size: 18px;
      font-weight: 600;
      color: var(--ion-color-dark);

      ion-icon {
        margin-right: 8px;
        font-size: 20px;
      }
    }
  }

  ion-card-content {
    padding: 8px 16px 16px 16px;
  }
}

ion-list {
  padding: 0;
}

ion-item {
  --padding-start: 0;
  --padding-end: 0;
  --inner-padding-end: 0;
  margin-bottom: 8px;
  border-radius: 8px;

  &:last-child {
    margin-bottom: 0;
  }

  ion-label {
    h3 {
      font-size: 16px;
      font-weight: 600;
      color: var(--ion-color-dark);
      margin: 0 0 4px 0;
    }

    p {
      font-size: 14px;
      color: var(--ion-color-medium);
      margin: 0 0 2px 0;

      &.transaction-amount {
        font-size: 16px;
        font-weight: 600;
        color: var(--ion-color-primary);
        margin-bottom: 4px;
      }

      &.transaction-account {
        font-size: 13px;
        color: var(--ion-color-dark);
        display: flex;
        align-items: center;
        gap: 4px;
        margin-bottom: 2px;

        ion-icon {
          font-size: 12px;
          margin: 0;
        }
      }

      &.transaction-destination {
        font-size: 13px;
        color: var(--ion-color-dark);
        display: flex;
        align-items: center;
        gap: 4px;
        margin-bottom: 2px;

        ion-icon {
          font-size: 12px;
          margin: 0;
        }
      }

      &.transaction-memo {
        font-size: 12px;
        color: var(--ion-color-medium);
        font-style: italic;
        display: flex;
        align-items: center;
        gap: 4px;
        margin-bottom: 4px;

        ion-icon {
          font-size: 12px;
          margin: 0;
        }
      }

      &.transaction-details {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 12px;
        color: var(--ion-color-medium-shade);
        margin-top: 4px;

        .transaction-fee {
          font-weight: 500;
        }

        .transaction-date {
          margin: 0;
        }
      }
    }
  }

  ion-icon {
    font-size: 20px;
    margin-right: 12px;
  }

  .transaction-status {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 4px;

    .status-badge {
      font-size: 11px;
      font-weight: 600;
      color: white;
    }

    ion-button {
      --padding-start: 4px;
      --padding-end: 4px;
      --padding-top: 4px;
      --padding-bottom: 4px;
      height: 28px;
      width: 28px;

      ion-icon {
        font-size: 14px;
        margin: 0;
      }
    }
  }
}

.empty-state {
  text-align: center;
  padding: 32px 16px;
  color: var(--ion-color-medium);

  ion-icon {
    margin-bottom: 16px;
    opacity: 0.5;
  }

  p {
    margin: 0;
    font-size: 16px;
  }
}

ion-badge {
  font-size: 12px;
  font-weight: 500;
}

ion-button[fill='clear'] {
  --color: var(--ion-color-primary);
  font-size: 14px;
  font-weight: 500;
  margin: 0;
  --padding-start: 8px;
  --padding-end: 8px;
}
