// Shared styles for admin sales components
// This file contains common styles used across admin sales components

// Common card styles
.admin-card {
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  margin-bottom: 24px;

  ion-card-header {
    padding: 24px;
    color: white;

    ion-card-title {
      display: flex;
      align-items: center;
      font-size: 1.5rem;
      font-weight: 600;
      margin: 0;

      .title-icon {
        margin-right: 12px;
        font-size: 1.8rem;
      }
    }

    ion-card-subtitle {
      color: rgba(255, 255, 255, 0.9);
      margin-top: 8px;
      font-size: 1rem;
    }
  }

  ion-card-content {
    padding: 24px;
  }
}

// Primary gradient card header
.primary-gradient-header {
  background: linear-gradient(
    135deg,
    var(--ion-color-primary),
    var(--ion-color-secondary)
  );
}

// Secondary gradient card header
.secondary-gradient-header {
  background: linear-gradient(
    135deg,
    var(--ion-color-secondary),
    var(--ion-color-tertiary)
  );
}

// Common form styles
.form-section {
  margin-bottom: 32px;
  padding: 20px;
  background: var(--ion-color-step-100);
  border-radius: 12px;
  border: 1px solid var(--ion-color-step-200);

  .section-title {
    display: flex;
    align-items: center;
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--ion-color-primary);
    margin: 0 0 20px 0;
    padding-bottom: 12px;
    border-bottom: 2px solid var(--ion-color-primary-tint);

    .section-icon {
      margin-right: 8px;
      font-size: 1.3rem;
    }
  }

  .form-row {
    display: grid;
    grid-template-columns: 1fr;
    gap: 16px;
    margin-bottom: 16px;

    @media (min-width: 768px) {
      grid-template-columns: 1fr 1fr;
    }
  }
}

// Common form item styles
.form-item {
  --background: var(--ion-color-step-50);
  --border-radius: 8px;
  --padding-start: 16px;
  --padding-end: 16px;
  --padding-top: 16px;
  --padding-bottom: 16px;
  margin-bottom: 8px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  &.error {
    --border-color: var(--ion-color-danger);
    --background: var(--ion-color-danger-tint);
    box-shadow: 0 0 0 2px var(--ion-color-danger-tint);
  }

  ion-label {
    font-weight: 500;
    color: var(--ion-color-step-900);

    .required {
      color: var(--ion-color-danger);
      font-weight: 600;
    }
  }

  ion-input,
  ion-textarea,
  ion-datetime {
    --color: var(--ion-color-step-900);
    --placeholder-color: var(--ion-color-step-500);
    font-size: 1rem;
  }

  ion-note {
    font-size: 0.85rem;
    color: var(--ion-color-step-600);
    margin-top: 4px;
  }
}

// Common button styles
.primary-button {
  --border-radius: 12px;
  --padding-top: 16px;
  --padding-bottom: 16px;
  font-size: 1.1rem;
  font-weight: 600;
  text-transform: none;
  height: 56px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;

  &:hover:not([disabled]) {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
  }

  &[disabled] {
    opacity: 0.6;
    transform: none;
  }
}

// Common loading styles
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px 24px;
  text-align: center;

  .loading-spinner {
    margin-bottom: 16px;
    --color: var(--ion-color-primary);
  }

  .loading-text {
    color: var(--ion-color-step-600);
    font-size: 1rem;
    margin: 0;
  }
}

// Common empty state styles
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px 24px;
  text-align: center;

  .empty-icon {
    font-size: 4rem;
    color: var(--ion-color-medium);
    margin-bottom: 16px;
  }

  .empty-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--ion-color-step-900);
    margin: 0 0 8px 0;
  }

  .empty-description {
    color: var(--ion-color-step-600);
    font-size: 1rem;
    line-height: 1.5;
    margin: 0 0 24px 0;
    max-width: 400px;
  }

  .empty-action {
    --border-radius: 12px;
    --padding-start: 24px;
    --padding-end: 24px;
    height: 48px;
    font-weight: 600;
  }
}

// Common error styles
.error-messages {
  margin: 16px 0;
  padding: 16px;
  background: var(--ion-color-danger-tint);
  border-radius: 8px;
  border-left: 4px solid var(--ion-color-danger);

  .error-item {
    --background: transparent;
    --padding-start: 0;
    --padding-end: 0;
    --padding-top: 4px;
    --padding-bottom: 4px;
    margin-bottom: 8px;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.form-alert {
  margin-bottom: 20px;
  border-radius: 8px;
  padding: 12px 16px;
  background: var(--ion-color-danger-tint);
  border-left: 4px solid var(--ion-color-danger);
}

// Common animations
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-in-up {
  animation: slideInUp 0.3s ease-out;
}

// Focus states
.form-item:focus-within {
  box-shadow: 0 0 0 2px var(--ion-color-primary-tint);
}

// Responsive design helpers
@media (max-width: 767px) {
  .admin-card {
    margin-bottom: 16px;

    ion-card-header {
      padding: 16px;

      ion-card-title {
        font-size: 1.3rem;
      }
    }

    ion-card-content {
      padding: 16px;
    }
  }

  .form-section {
    padding: 16px;
    margin-bottom: 24px;

    .section-title {
      font-size: 1.1rem;
    }
  }

  .form-item {
    --padding-start: 12px;
    --padding-end: 12px;
    --padding-top: 12px;
    --padding-bottom: 12px;
  }
}
