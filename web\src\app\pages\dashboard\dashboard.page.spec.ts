/// <reference types="jasmine" />

import {
  ComponentFixture,
  TestBed,
  fakeAsync,
  tick,
} from '@angular/core/testing';
import { Router } from '@angular/router';
import { of, throwError, BehaviorSubject } from 'rxjs';
import { DashboardPage } from './dashboard.page';
import { ApiService } from '../../services/api/api.service';
import { WebSocketService } from '../../services/websocket/websocket.service';
import { BalanceResponse, Transaction, Sale, User } from '../../types';
import {
  ToastController,
  ModalController,
  AngularDelegate,
} from '@ionic/angular';

describe('DashboardPage', () => {
  let component: DashboardPage;
  let fixture: ComponentFixture<DashboardPage>;
  let mockApiService: jasmine.SpyObj<ApiService>;
  let mockWebSocketService: jasmine.SpyObj<WebSocketService>;
  let mockRouter: jasmine.SpyObj<Router>;
  let mockToastController: jasmine.SpyObj<ToastController>;
  let mockModalController: jasmine.SpyObj<ModalController>;
  let currentUserSubject: BehaviorSubject<User | null>;

  const mockBalance: BalanceResponse = {
    xrp: 1000.5,
    currency: 'XRP',
    address: 'rTestAddress123456789',
    ledgerIndex: 12345,
    validated: true,
  };

  const mockTransactions: Transaction[] = [
    {
      hash: 'tx1',
      account: 'rTestAddress123456789',
      destination: 'rDestination123',
      amount: 100,
      currency: 'XRP',
      fee: 0.000012,
      ledgerIndex: 12345,
      date: '2024-01-01T00:00:00Z',
      type: 'payment',
      validated: true,
    },
    {
      hash: 'tx2',
      account: 'rTestAddress123456789',
      destination: 'rDestination456',
      amount: 50,
      currency: 'XRP',
      fee: 0.000012,
      ledgerIndex: 12346,
      date: '2024-01-02T00:00:00Z',
      type: 'payment',
      validated: true,
    },
  ];

  const mockSales: Sale[] = [
    {
      id: 'sale1',
      symbol: 'TEST',
      price: 0.1,
      description: 'Test description',
      status: 'active',
      start: '2024-01-01T00:00:00Z',
      end: '2024-12-31T23:59:59Z',
      softCap: 1000,
      hardCap: 5000,
      collectionAddress: 'rCollection123',
      website: 'https://test.com',
      whitepaper: 'https://test.com/whitepaper',
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z',
    },
  ];

  const mockUser: User = {
    id: '1',
    username: 'testuser',
    email: '<EMAIL>',
    role: 'user',
    xrplAddress: 'rTestAddress123456789',
  };

  beforeEach(async () => {
    const apiServiceSpy = jasmine.createSpyObj(
      'ApiService',
      ['getBalance', 'getTransactions', 'getActiveSales'],
      {
        currentUser$: new BehaviorSubject<User | null>(null),
      }
    );

    const webSocketServiceSpy = jasmine.createSpyObj(
      'WebSocketService',
      [
        'isReady',
        'start',
        'getIsConnected',
        'requestBalanceUpdate',
        'requestTransactionsUpdate',
        'debugStatus',
      ],
      {
        balance: { signal: () => mockBalance },
        transactions: { signal: () => mockTransactions },
        isConnected$: of(true),
      }
    );

    const routerSpy = jasmine.createSpyObj('Router', ['navigate']);
    const toastControllerSpy = jasmine.createSpyObj('ToastController', [
      'create',
    ]);
    const modalControllerSpy = jasmine.createSpyObj('ModalController', [
      'create',
    ]);
    const angularDelegateSpy = jasmine.createSpyObj('AngularDelegate', [
      'create',
    ]);

    currentUserSubject = new BehaviorSubject<User | null>(null);
    Object.defineProperty(apiServiceSpy, 'currentUser$', {
      value: currentUserSubject.asObservable(),
      writable: false,
    });

    await TestBed.configureTestingModule({
      imports: [DashboardPage],
      providers: [
        { provide: ApiService, useValue: apiServiceSpy },
        { provide: WebSocketService, useValue: webSocketServiceSpy },
        { provide: Router, useValue: routerSpy },
        { provide: ToastController, useValue: toastControllerSpy },
        { provide: ModalController, useValue: modalControllerSpy },
        { provide: AngularDelegate, useValue: angularDelegateSpy },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(DashboardPage);
    component = fixture.componentInstance;
    mockApiService = TestBed.inject(ApiService) as jasmine.SpyObj<ApiService>;
    mockWebSocketService = TestBed.inject(
      WebSocketService
    ) as jasmine.SpyObj<WebSocketService>;
    mockRouter = TestBed.inject(Router) as jasmine.SpyObj<Router>;
    mockToastController = TestBed.inject(
      ToastController
    ) as jasmine.SpyObj<ToastController>;
    mockModalController = TestBed.inject(
      ModalController
    ) as jasmine.SpyObj<ModalController>;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should load data on init', () => {
    mockApiService.getBalance.and.returnValue(of(mockBalance));
    mockApiService.getTransactions.and.returnValue(of(mockTransactions));
    mockApiService.getActiveSales.and.returnValue(of(mockSales));
    mockWebSocketService.isReady.and.returnValue(true);
    mockWebSocketService.getIsConnected.and.returnValue(true);

    component.ngOnInit();

    expect(mockApiService.getBalance).toHaveBeenCalled();
    expect(mockApiService.getTransactions).toHaveBeenCalled();
    expect(mockApiService.getActiveSales).toHaveBeenCalled();
  });

  it('should start WebSocket service if ready', () => {
    mockApiService.getBalance.and.returnValue(of(mockBalance));
    mockApiService.getTransactions.and.returnValue(of(mockTransactions));
    mockApiService.getActiveSales.and.returnValue(of(mockSales));
    mockWebSocketService.isReady.and.returnValue(true);
    mockWebSocketService.getIsConnected.and.returnValue(true);

    component.ngOnInit();

    expect(mockWebSocketService.start).toHaveBeenCalled();
  });

  it('should not start WebSocket service if not ready', () => {
    mockApiService.getBalance.and.returnValue(of(mockBalance));
    mockApiService.getTransactions.and.returnValue(of(mockTransactions));
    mockApiService.getActiveSales.and.returnValue(of(mockSales));
    mockWebSocketService.isReady.and.returnValue(false);

    component.ngOnInit();

    expect(mockWebSocketService.start).not.toHaveBeenCalled();
  });

  it('should request WebSocket updates when connected', fakeAsync(() => {
    mockApiService.getBalance.and.returnValue(of(mockBalance));
    mockApiService.getTransactions.and.returnValue(of(mockTransactions));
    mockApiService.getActiveSales.and.returnValue(of(mockSales));
    mockWebSocketService.isReady.and.returnValue(true);
    mockWebSocketService.getIsConnected.and.returnValue(true);

    component.ngOnInit();

    // Fast-forward time to trigger the setTimeout in ngOnInit
    tick(1000);

    expect(mockWebSocketService.requestBalanceUpdate).toHaveBeenCalled();
    expect(mockWebSocketService.requestTransactionsUpdate).toHaveBeenCalled();
  }));

  it('should redirect to XRPL connect if no address linked', fakeAsync(() => {
    const userWithoutAddress: User = {
      id: '1',
      username: 'testuser',
      email: '<EMAIL>',
      role: 'user',
    };

    mockApiService.getBalance.and.returnValue(of(mockBalance));
    mockApiService.getTransactions.and.returnValue(of(mockTransactions));
    mockApiService.getActiveSales.and.returnValue(of(mockSales));
    mockWebSocketService.isReady.and.returnValue(false);

    // Set up the user without address before initialization
    currentUserSubject.next(userWithoutAddress);

    // Initialize to set up the subscription and trigger the check
    component.ngOnInit();

    // Wait for the subscription to execute
    tick();

    expect(mockRouter.navigate).toHaveBeenCalledWith(['/xrpl-connect']);
  }));

  it('should not redirect if address is linked', () => {
    mockApiService.getBalance.and.returnValue(of(mockBalance));
    mockApiService.getTransactions.and.returnValue(of(mockTransactions));
    mockApiService.getActiveSales.and.returnValue(of(mockSales));
    mockWebSocketService.isReady.and.returnValue(false);

    component.ngOnInit();
    currentUserSubject.next(mockUser);

    expect(mockRouter.navigate).not.toHaveBeenCalledWith(['/xrpl-connect']);
  });

  it('should handle API errors gracefully', () => {
    mockApiService.getBalance.and.returnValue(
      throwError(() => new Error('API Error'))
    );
    mockApiService.getTransactions.and.returnValue(
      throwError(() => new Error('API Error'))
    );
    mockApiService.getActiveSales.and.returnValue(
      throwError(() => new Error('API Error'))
    );
    mockWebSocketService.isReady.and.returnValue(false);

    expect(() => component.ngOnInit()).not.toThrow();
  });

  it('should refresh data when onRefreshData is called', () => {
    mockApiService.getBalance.and.returnValue(of(mockBalance));
    mockApiService.getTransactions.and.returnValue(of(mockTransactions));
    mockApiService.getActiveSales.and.returnValue(of(mockSales));
    mockWebSocketService.getIsConnected.and.returnValue(true);

    component.onRefreshData();

    expect(mockApiService.getBalance).toHaveBeenCalled();
    expect(mockApiService.getTransactions).toHaveBeenCalled();
    expect(mockApiService.getActiveSales).toHaveBeenCalled();
    expect(mockWebSocketService.requestBalanceUpdate).toHaveBeenCalled();
    expect(mockWebSocketService.requestTransactionsUpdate).toHaveBeenCalled();
  });

  it('should navigate to sale detail when onViewSale is called', () => {
    const saleId = 'sale1';
    component.onViewSale(saleId);
    expect(mockRouter.navigate).toHaveBeenCalledWith(['/launchpad', saleId]);
  });

  it('should navigate to launchpad list when onViewAllSales is called', () => {
    component.onViewAllSales();
    expect(mockRouter.navigate).toHaveBeenCalledWith(['/launchpad']);
  });

  it('should debug WebSocket when onDebugWebSocket is called', () => {
    mockWebSocketService.getIsConnected.and.returnValue(false);
    component.onDebugWebSocket();

    expect(mockWebSocketService.debugStatus).toHaveBeenCalled();
    expect(mockWebSocketService.start).toHaveBeenCalled();
  });

  it('should navigate to view all transactions when onViewAllTransactions is called', () => {
    spyOn(console, 'log');
    component.onViewAllTransactions();
    expect(console.log).toHaveBeenCalledWith('View all transactions');
  });

  it('should show transaction details modal when onViewTransactionDetails is called', async () => {
    const mockTransaction: Transaction = {
      hash: 'tx1',
      account: 'rTestAddress123456789',
      destination: 'rDestination123',
      amount: 100,
      currency: 'XRP',
      fee: 0.000012,
      ledgerIndex: 12345,
      date: '2024-01-01T00:00:00Z',
      type: 'payment',
      validated: true,
    };

    spyOn(component as any, 'showTransactionDetailsModal').and.returnValue(
      Promise.resolve()
    );
    spyOn(console, 'log');

    await component.onViewTransactionDetails(mockTransaction);

    expect(console.log).toHaveBeenCalledWith(
      'View transaction details:',
      mockTransaction
    );
    expect((component as any).showTransactionDetailsModal).toHaveBeenCalledWith(
      mockTransaction
    );
  });

  it('should copy transaction hash to clipboard when onCopyTransactionHash is called', async () => {
    const mockHash = 'test-hash-123';
    const mockEvent = new Event('click');

    // Mock navigator.clipboard
    const mockClipboard = {
      writeText: jasmine
        .createSpy('writeText')
        .and.returnValue(Promise.resolve()),
    };
    Object.defineProperty(navigator, 'clipboard', {
      value: mockClipboard,
      writable: true,
    });

    spyOn(component as any, 'showToast').and.returnValue(Promise.resolve());

    component.onCopyTransactionHash(mockHash, mockEvent);

    expect(mockEvent.stopPropagation).toBeDefined();
    expect(mockClipboard.writeText).toHaveBeenCalledWith(mockHash);
  });

  it('should handle keyboard shortcuts correctly', () => {
    spyOn(component, 'onRefreshData');
    spyOn(component, 'onDebugWebSocket');

    // Test Ctrl+R shortcut
    const refreshEvent = new KeyboardEvent('keydown', {
      key: 'r',
      ctrlKey: true,
    });
    component.onKeyDown(refreshEvent);
    expect(component.onRefreshData).toHaveBeenCalled();

    // Test Ctrl+D shortcut
    const debugEvent = new KeyboardEvent('keydown', {
      key: 'd',
      ctrlKey: true,
    });
    component.onKeyDown(debugEvent);
    expect(component.onDebugWebSocket).toHaveBeenCalled();
  });

  it('should clean up subscriptions on destroy', () => {
    spyOn(component['destroy$'], 'next');
    spyOn(component['destroy$'], 'complete');

    component.ngOnDestroy();

    expect(component['destroy$'].next).toHaveBeenCalled();
    expect(component['destroy$'].complete).toHaveBeenCalled();
  });
});
