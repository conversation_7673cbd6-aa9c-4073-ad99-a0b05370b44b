.filter-segment {
  margin: 16px;
  --background: var(--ion-color-light-shade);
  --border-radius: 8px;
}

.launchpad-container {
  padding: 0 16px 16px 16px;
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;

  ion-spinner {
    margin-bottom: 16px;
  }

  p {
    color: var(--ion-color-medium);
    margin: 0;
    font-size: 16px;
  }
}

.sales-list {
  .sale-card {
    margin-bottom: 16px;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition:
      transform 0.2s ease,
      box-shadow 0.2s ease;

    &:active {
      transform: scale(0.98);
      box-shadow: 0 1px 4px rgba(0, 0, 0, 0.15);
    }

    ion-card-content {
      padding: 20px;
    }
  }
}

.sale-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;

  .sale-info {
    flex: 1;
    margin-right: 16px;

    .sale-symbol {
      font-size: 24px;
      font-weight: 700;
      color: var(--ion-color-primary);
      margin: 0 0 4px 0;
    }

    .sale-name {
      font-size: 18px;
      font-weight: 600;
      color: var(--ion-color-dark);
      margin: 0 0 8px 0;
    }

    .sale-description {
      font-size: 14px;
      color: var(--ion-color-medium);
      margin: 0;
      line-height: 1.4;
    }
  }

  .status-badge {
    font-size: 12px;
    font-weight: 600;
    padding: 6px 12px;
    border-radius: 16px;
    white-space: nowrap;
  }
}

.sale-progress {
  margin-bottom: 20px;

  .progress-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;

    .progress-label {
      font-size: 14px;
      font-weight: 600;
      color: var(--ion-color-dark);
    }

    .progress-percentage {
      font-size: 16px;
      font-weight: 700;
      color: var(--ion-color-primary);
    }
  }

  .progress-bar {
    margin-bottom: 8px;
    --progress-background: var(--ion-color-light-shade);
    --progress-bar-background: var(--ion-color-primary);
  }

  .progress-amounts {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;

    .raised {
      color: var(--ion-color-success);
      font-weight: 600;
    }

    .target {
      color: var(--ion-color-medium);
    }
  }
}

.sale-details {
  margin-bottom: 20px;

  .detail-row {
    display: flex;
    gap: 16px;
    margin-bottom: 16px;

    &:last-child {
      margin-bottom: 0;
    }

    .detail-item {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      text-align: center;
      padding: 12px;
      background: var(--ion-color-light-shade);
      border-radius: 8px;

      ion-icon {
        font-size: 20px;
        color: var(--ion-color-primary);
        margin-bottom: 8px;
      }

      .detail-label {
        font-size: 12px;
        color: var(--ion-color-medium);
        margin-bottom: 4px;
        font-weight: 500;
      }

      .detail-value {
        font-size: 14px;
        color: var(--ion-color-dark);
        font-weight: 600;
      }
    }
  }
}

.sale-actions {
  ion-button {
    --border-radius: 8px;
    --padding-top: 16px;
    --padding-bottom: 16px;
    font-weight: 600;
    font-size: 16px;

    ion-icon {
      margin-right: 8px;
    }
  }
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: var(--ion-color-medium);

  ion-icon {
    margin-bottom: 16px;
    opacity: 0.5;
    font-size: 64px;
  }

  h3 {
    font-size: 20px;
    font-weight: 600;
    color: var(--ion-color-dark);
    margin: 0 0 8px 0;
  }

  p {
    font-size: 16px;
    margin: 0 0 20px 0;
    line-height: 1.4;
  }

  ion-button {
    --color: var(--ion-color-primary);
    font-weight: 500;
  }
}

@media (max-width: 576px) {
  .launchpad-container {
    padding: 0 12px 12px 12px;
  }

  .filter-segment {
    margin: 12px;
  }

  .sale-card ion-card-content {
    padding: 16px;
  }

  .sale-header {
    flex-direction: column;
    align-items: flex-start;

    .sale-info {
      margin-right: 0;
      margin-bottom: 12px;
    }
  }

  .sale-details .detail-row {
    flex-direction: column;
    gap: 12px;

    .detail-item {
      flex: none;
    }
  }
}
