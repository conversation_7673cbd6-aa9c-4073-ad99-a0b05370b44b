import {
  Component,
  ChangeDetectionStrategy,
  inject,
  OnInit,
  OnDestroy,
  signal,
  ElementRef,
  ViewChild,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule, AlertController, ToastController } from '@ionic/angular';
import { Subject, BehaviorSubject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { ApiService } from '../../services/api/api.service';
import { Sale } from '../../types';
import { SaleFormComponent } from '../../components/sale-form/sale-form.component';
import { AdminSalesListComponent } from '../../components/admin-sales-list/admin-sales-list.component';

/**
 * Admin sales management component for creating and managing token sales.
 *
 * This component provides administrative functionality for:
 * - Creating new token sales with comprehensive validation
 * - Viewing and managing existing token sales
 * - Updating sale parameters and status
 * - Deleting sales when necessary
 * - Monitoring sale statistics and contributions
 *
 * Features:
 * - Comprehensive form validation with custom validators
 * - Real-time form feedback and error handling
 * - Responsive design with loading states
 * - Interactive sale management with swipe actions
 * - XRPL address validation and date range validation
 * - Admin-only access with role-based security
 *
 * @example
 * ```html
 * <app-admin-sales></app-admin-sales>
 * ```
 *
 * @since 1.0.0
 * <AUTHOR>
 */
@Component({
  selector: 'app-admin-sales',
  templateUrl: './admin-sales.page.html',
  styleUrls: ['./admin-sales.page.scss'],
  standalone: true,
  imports: [
    CommonModule,
    IonicModule,
    SaleFormComponent,
    AdminSalesListComponent,
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AdminSalesPage implements OnInit, OnDestroy {
  private apiService = inject(ApiService);
  private alertController = inject(AlertController);
  private toastController = inject(ToastController);
  private destroy$ = new Subject<void>();

  @ViewChild('formCard', { static: false }) formCard!: ElementRef;

  // State
  isLoading = signal<boolean>(false);
  sales$ = new BehaviorSubject<Sale[]>([]);

  ngOnInit() {
    this.loadSales();
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  loadSales(): void {
    this.isLoading.set(true);
    this.apiService
      .getSales()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: sales => {
          this.sales$.next(sales);
          this.isLoading.set(false);
        },
        error: error => {
          console.error('Error loading sales:', error);
          this.isLoading.set(false);
        },
      });
  }

  onSaleCreated(newSale: Sale): void {
    // Add new sale to the list
    const currentSales = this.sales$.value;
    this.sales$.next([newSale, ...currentSales]);

    // Show success message
    this.presentToast('Token sale created successfully!', 'success');
  }

  onDeleteSale(saleId: string): void {
    this.presentDeleteAlert(saleId);
  }

  onEditSale(sale: Sale): void {
    // TODO: Implement edit functionality
    this.presentToast('Edit functionality coming soon!', 'info');
  }

  onOpenLink(url: string): void {
    window.open(url, '_blank');
  }

  onScrollToForm(): void {
    if (this.formCard) {
      this.formCard.nativeElement.scrollIntoView({
        behavior: 'smooth',
        block: 'start',
      });
    }
  }

  private async presentDeleteAlert(saleId: string): Promise<void> {
    const alert = await this.alertController.create({
      header: 'Delete Sale',
      message:
        'Are you sure you want to delete this sale? This action cannot be undone.',
      buttons: [
        {
          text: 'Cancel',
          role: 'cancel',
        },
        {
          text: 'Delete',
          role: 'destructive',
          handler: () => {
            this.deleteSale(saleId);
          },
        },
      ],
    });

    await alert.present();
  }

  private deleteSale(saleId: string): void {
    this.apiService
      .deleteSale(saleId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          // Remove sale from the list
          const currentSales = this.sales$.value;
          this.sales$.next(currentSales.filter(sale => sale.id !== saleId));
          this.presentToast('Sale deleted successfully', 'success');
        },
        error: error => {
          console.error('Error deleting sale:', error);
          this.presentToast('Failed to delete sale', 'danger');
        },
      });
  }

  private async presentToast(
    message: string,
    color: 'success' | 'danger' | 'warning' | 'info'
  ): Promise<void> {
    const toast = await this.toastController.create({
      message,
      duration: 3000,
      color,
      position: 'top',
    });
    await toast.present();
  }
}
