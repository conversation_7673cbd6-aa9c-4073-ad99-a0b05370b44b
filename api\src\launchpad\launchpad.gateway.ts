import {
  WebSocketGateway,
  WebSocketServer,
  SubscribeMessage,
  OnGatewayConnection,
  OnGatewayDisconnect,
  ConnectedSocket,
  MessageBody,
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { UseGuards, Logger, Injectable } from '@nestjs/common';
import { WsJwtAuthGuard } from '../auth/guards/ws-jwt-auth.guard';
import {
  XrplService,
  SaleContributionEvent,
  SaleProgressUpdate,
} from '../xrpl/xrpl.service';
import { LaunchpadService } from './launchpad.service';
import { Sale } from './entities/sale.entity';

@WebSocketGateway({
  cors: {
    origin: '*',
  },
  namespace: '/launchpad',
})
@UseGuards(WsJwtAuthGuard)
@Injectable()
export class LaunchpadGateway
  implements OnGatewayConnection, OnGatewayDisconnect
{
  @WebSocketServer()
  server!: Server;

  private readonly logger = new Logger(LaunchpadGateway.name);
  private userSockets = new Map<string, Socket[]>();
  private saleRooms = new Map<string, Set<string>>(); // saleId -> Set of userIds
  private saleSubscriptions = new Map<string, any>(); // saleId -> XRPL subscription

  constructor(
    private readonly xrplService: XrplService,
    private readonly launchpadService: LaunchpadService,
  ) {}

  handleConnection(client: Socket) {
    this.logger.log(`Client connected: ${client.id}`);
  }

  handleDisconnect(client: Socket) {
    this.logger.log(`Client disconnected: ${client.id}`);
    this.removeClientFromAllRooms(client);
  }

  @SubscribeMessage('joinSale')
  async handleJoinSale(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: { saleId: string },
  ) {
    try {
      const userId = (client as any).user?.id;
      if (!userId) {
        client.emit('error', { message: 'Authentication required' });
        return;
      }

      const { saleId } = data;
      if (!saleId) {
        client.emit('error', { message: 'Sale ID is required' });
        return;
      }

      // Get sale information
      const sale = await this.launchpadService.findSaleById(saleId);
      if (!sale) {
        client.emit('error', { message: 'Sale not found' });
        return;
      }

      // Add client to user's socket list
      if (!this.userSockets.has(userId)) {
        this.userSockets.set(userId, []);
      }
      this.userSockets.get(userId)!.push(client);

      // Add user to sale room
      if (!this.saleRooms.has(saleId)) {
        this.saleRooms.set(saleId, new Set());
      }
      this.saleRooms.get(saleId)!.add(userId);

      // Join the socket room
      await client.join(`sale:${saleId}`);

      // Start monitoring this sale if not already monitoring
      if (!this.saleSubscriptions.has(saleId)) {
        this.startSaleMonitoring(sale);
      }

      // Send current sale progress
      const progress = this.getSaleProgress(sale);
      client.emit('saleProgress', progress);

      client.emit('joinedSale', { saleId, sale });
      this.logger.log(`User ${userId} joined sale ${saleId}`);
    } catch (error) {
      this.logger.error(`Join sale error: ${error as string}`);
      client.emit('error', { message: 'Failed to join sale' });
    }
  }

  @SubscribeMessage('leaveSale')
  async handleLeaveSale(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: { saleId: string },
  ) {
    try {
      const userId = (client as any).user?.id;
      if (!userId) {
        client.emit('error', { message: 'Authentication required' });
        return;
      }

      const { saleId } = data;
      if (!saleId) {
        client.emit('error', { message: 'Sale ID is required' });
        return;
      }

      // Leave the socket room
      await client.leave(`sale:${saleId}`);

      // Remove user from sale room
      const saleRoom = this.saleRooms.get(saleId);
      if (saleRoom) {
        saleRoom.delete(userId);
        if (saleRoom.size === 0) {
          this.saleRooms.delete(saleId);
          // Stop monitoring if no users are in the room
          this.stopSaleMonitoring(saleId);
        }
      }

      // Remove client from user's socket list
      const userSockets = this.userSockets.get(userId);
      if (userSockets) {
        const index = userSockets.indexOf(client);
        if (index > -1) {
          userSockets.splice(index, 1);
        }
        if (userSockets.length === 0) {
          this.userSockets.delete(userId);
        }
      }

      client.emit('leftSale', { saleId });
      this.logger.log(`User ${userId} left sale ${saleId}`);
    } catch (error) {
      this.logger.error(`Leave sale error: ${error as string}`);
      client.emit('error', { message: 'Failed to leave sale' });
    }
  }

  private startSaleMonitoring(sale: Sale): void {
    try {
      // Generate destination tag from sale ID (simple hash-based approach)
      const destinationTag = this.generateDestinationTag(sale.id);

      // Subscribe to XRPL events for this sale
      const subscription = this.xrplService.subscribeToSale(
        sale.id,
        sale.collectionAddress,
        destinationTag,
      );

      // Store subscription
      this.saleSubscriptions.set(sale.id, subscription);

      // Listen for contribution events
      subscription.subscribe({
        next: (event: SaleContributionEvent) => {
          void this.handleContributionEvent(event);
        },
        error: (error) => {
          this.logger.error(
            `Sale subscription error for ${sale.id}: ${error as string}`,
          );
        },
      });

      this.logger.log(`Started monitoring sale ${sale.id}`);
    } catch (error) {
      this.logger.error(
        `Failed to start monitoring sale ${sale.id}: ${error as string}`,
      );
    }
  }

  private stopSaleMonitoring(saleId: string): void {
    if (this.saleSubscriptions.has(saleId)) {
      this.xrplService.unsubscribeFromSale(saleId);
      this.saleSubscriptions.delete(saleId);
      this.logger.log(`Stopped monitoring sale ${saleId}`);
    }
  }

  private async handleContributionEvent(
    event: SaleContributionEvent,
  ): Promise<void> {
    try {
      // Update contribution in database
      await this.launchpadService.processContributionEvent(event);

      // Get updated sale progress
      const sale = await this.launchpadService.findSaleById(event.saleId);
      if (sale) {
        const progress = this.getSaleProgress(sale);

        // Broadcast to all users in the sale room
        this.server.to(`sale:${event.saleId}`).emit('contributionReceived', {
          event,
          progress,
        });

        this.logger.log(
          `Processed contribution for sale ${event.saleId}: ${event.amount} XRP`,
        );
      }
    } catch (error) {
      this.logger.error(
        `Error handling contribution event: ${error as string}`,
      );
    }
  }

  private getSaleProgress(sale: Sale): SaleProgressUpdate {
    try {
      // Get total raised from contributions
      const totalRaised = sale.contributions.reduce(
        (sum, contrib) => sum + contrib.amount,
        0,
      );

      // Calculate progress percentage
      const progressPercent = (totalRaised / sale.hardCap) * 100;

      // Get total contributors
      const totalContributors = sale.contributions.length;

      // Get last contribution
      const lastContribution =
        sale.contributions.length > 0
          ? sale.contributions[sale.contributions.length - 1]
          : undefined;

      return {
        saleId: sale.id,
        totalRaised,
        progressPercent: Math.min(progressPercent, 100),
        totalContributors,
        lastContribution: lastContribution
          ? {
              amount: lastContribution.amount,
              sender: lastContribution.user?.id || 'Unknown',
              timestamp: lastContribution.createdAt,
            }
          : undefined,
      };
    } catch (error) {
      this.logger.error(`Error calculating sale progress: ${error as string}`);
      return {
        saleId: sale.id,
        totalRaised: 0,
        progressPercent: 0,
        totalContributors: 0,
      };
    }
  }

  private generateDestinationTag(saleId: string): number {
    // Simple hash-based destination tag generation
    let hash = 0;
    for (let i = 0; i < saleId.length; i++) {
      const char = saleId.charCodeAt(i);
      hash = (hash << 5) - hash + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash) % 1000000; // Keep within reasonable range
  }

  private removeClientFromAllRooms(client: Socket): void {
    const userId = (client as any).user?.id;
    if (!userId) return;

    // Remove client from user's socket list
    const userSockets = this.userSockets.get(userId);
    if (userSockets) {
      const index = userSockets.indexOf(client);
      if (index > -1) {
        userSockets.splice(index, 1);
      }
      if (userSockets.length === 0) {
        this.userSockets.delete(userId);
      }
    }

    // Check if user has no more active connections
    if (!this.userSockets.has(userId)) {
      // Remove user from all sale rooms
      this.saleRooms.forEach((userIds, saleId) => {
        userIds.delete(userId);
        if (userIds.size === 0) {
          this.saleRooms.delete(saleId);
          // Stop monitoring if no users are in the room
          this.stopSaleMonitoring(saleId);
        }
      });
    }
  }

  // Public method to broadcast sale updates (can be called from other services)
  public broadcastSaleUpdate(saleId: string, update: any): void {
    if (this.saleRooms.has(saleId)) {
      this.server.to(`sale:${saleId}`).emit('saleUpdate', update);
    }
  }

  // Public method to get active users in a sale
  public getActiveUsersInSale(saleId: string): string[] {
    const room = this.saleRooms.get(saleId);
    return room ? Array.from(room) : [];
  }
}
