import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Sale } from './sale.entity';
import { User } from '../../users/entities/user.entity';

@Entity('contributions')
export class Contribution {
  @ApiProperty({
    description: 'Unique identifier for the contribution',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @ApiProperty({
    description: 'ID of the user who made the contribution',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @Column({ type: 'uuid' })
  userId!: string;

  @ApiProperty({
    description: 'ID of the sale this contribution is for',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @Column({ type: 'uuid' })
  saleId!: string;

  @ApiProperty({
    description: 'Amount contributed in XRP',
    example: 100,
    minimum: 0,
  })
  @Column({ type: 'decimal', precision: 18, scale: 8 })
  amount!: number;

  @ApiProperty({
    description: 'XRPL transaction hash for the contribution',
    example: '1234567890ABCDEF1234567890ABCDEF1234567890ABCDEF1234567890ABCDEF',
    maxLength: 255,
  })
  @Column({ type: 'varchar', length: 255 })
  txHash!: string;

  @ApiProperty({
    description: 'Status of the contribution',
    required: false,
    example: 'confirmed',
    maxLength: 50,
  })
  @Column({ type: 'varchar', length: 50, nullable: true })
  status?: string;

  @ApiProperty({
    description: 'Date when the contribution was created',
    example: '2025-01-01T00:00:00.000Z',
  })
  @CreateDateColumn()
  createdAt!: Date;

  @ApiProperty({
    description: 'Date when the contribution was last updated',
    example: '2025-01-01T00:00:00.000Z',
  })
  @UpdateDateColumn()
  updatedAt!: Date;

  @ApiProperty({
    description: 'The sale this contribution is for',
    type: () => Sale,
  })
  @ManyToOne(() => Sale, (sale) => sale.contributions)
  @JoinColumn({ name: 'saleId' })
  sale!: Sale;

  @ApiProperty({
    description: 'The user who made this contribution',
    type: () => User,
  })
  @ManyToOne(() => User)
  @JoinColumn({ name: 'userId' })
  user!: User;
}
