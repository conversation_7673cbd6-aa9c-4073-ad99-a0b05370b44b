<ion-card class="sales-card">
  <ion-card-header>
    <ion-card-title>
      <ion-icon name="rocket-outline"></ion-icon>
      Active Sales
    </ion-card-title>
    <ion-button
      fill="clear"
      size="small"
      (click)="onViewAllClick()"
      aria-label="View all sales"
    >
      View All
    </ion-button>
  </ion-card-header>
  <ion-card-content>
    @if (sales) {
      @if (sales.length > 0) {
        <ion-list>
          @for (sale of sales; track sale.id) {
            <ion-item
              button
              (click)="onViewSaleClick(sale.id)"
              class="sale-item"
              [attr.aria-label]="'View sale details for ' + sale.symbol"
            >
              <ion-icon
                [name]="sale.status === 'active' ? 'play-circle' : 'time'"
                slot="start"
                [color]="getSaleStatusColor(sale.status)"
                aria-hidden="true"
              ></ion-icon>
              <ion-label>
                <h3>{{ sale.symbol }}</h3>
                <p>{{ sale.description }}</p>
                <p class="sale-price">Price: {{ sale.price }} XRP</p>
                <p class="sale-dates">
                  {{ sale.start | date: 'short' }} -
                  {{ sale.end | date: 'short' }}
                </p>
              </ion-label>
              <ion-badge
                [color]="getSaleStatusColor(sale.status)"
                slot="end"
                class="status-badge"
                color="white"
              >
                {{ sale.status | titlecase }}
              </ion-badge>
            </ion-item>
          }
        </ion-list>
      } @else {
        <div class="empty-state">
          <ion-icon name="rocket-outline" size="large"></ion-icon>
          <p>No active sales at the moment</p>
        </div>
      }
    } @else {
      <ion-skeleton-text animated></ion-skeleton-text>
    }
  </ion-card-content>
</ion-card>
