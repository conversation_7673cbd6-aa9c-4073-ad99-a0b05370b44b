<ion-item-sliding class="sale-item-sliding">
  <ion-item class="sale-item" [class]="'status-' + sale.status">
    <div class="sale-content">
      <!-- Sale Header -->
      <div class="sale-header">
        <div class="sale-title">
          <h2 class="symbol">{{ sale.symbol }}</h2>
          <ion-chip [color]="getStatusColor(sale.status)" class="status-chip">
            <ion-icon
              [name]="getStatusIcon(sale.status)"
              slot="start"
            ></ion-icon>
            <ion-label>{{ sale.status | titlecase }}</ion-label>
          </ion-chip>
        </div>
        <div class="sale-price">
          <span class="price-label">Price</span>
          <span class="price-value">{{ sale.price }} XRP</span>
        </div>
      </div>

      <!-- Sale Details -->
      <div class="sale-details">
        <div class="detail-row">
          <div class="detail-item">
            <ion-icon name="calendar" class="detail-icon"></ion-icon>
            <span class="detail-label">Start:</span>
            <span class="detail-value">{{ formatDate(sale.start) }}</span>
          </div>
          <div class="detail-item">
            <ion-icon name="calendar" class="detail-icon"></ion-icon>
            <span class="detail-label">End:</span>
            <span class="detail-value">{{ formatDate(sale.end) }}</span>
          </div>
        </div>

        <div class="detail-row">
          <div class="detail-item">
            <ion-icon name="trending-up" class="detail-icon"></ion-icon>
            <span class="detail-label">Soft Cap:</span>
            <span class="detail-value">{{ sale.softCap | number }} XRP</span>
          </div>
          <div class="detail-item">
            <ion-icon name="trending-up" class="detail-icon"></ion-icon>
            <span class="detail-label">Hard Cap:</span>
            <span class="detail-value">{{ sale.hardCap | number }} XRP</span>
          </div>
        </div>

        <div class="detail-row">
          <div class="detail-item full-width">
            <ion-icon name="wallet" class="detail-icon"></ion-icon>
            <span class="detail-label">Collection:</span>
            <span class="detail-value address">{{
              sale.collectionAddress
            }}</span>
          </div>
        </div>

        <div *ngIf="sale.description" class="detail-row">
          <div class="detail-item full-width">
            <ion-icon name="document-text" class="detail-icon"></ion-icon>
            <span class="detail-value description">{{ sale.description }}</span>
          </div>
        </div>

        <!-- Links -->
        <div *ngIf="sale.website || sale.whitepaper" class="links-row">
          <ion-button
            *ngIf="sale.website"
            fill="clear"
            size="small"
            (click)="onOpenLink(sale.website)"
            class="link-button"
          >
            <ion-icon name="globe" slot="start"></ion-icon>
            Website
          </ion-button>
          <ion-button
            *ngIf="sale.whitepaper"
            fill="clear"
            size="small"
            (click)="onOpenLink(sale.whitepaper)"
            class="link-button"
          >
            <ion-icon name="document" slot="start"></ion-icon>
            Whitepaper
          </ion-button>
        </div>
      </div>
    </div>
  </ion-item>

  <ion-item-options side="end" class="sale-options">
    <ion-item-option color="primary" (click)="onEditSale()">
      <ion-icon name="create" slot="icon-only"></ion-icon>
    </ion-item-option>
    <ion-item-option color="danger" (click)="onDeleteSale()">
      <ion-icon name="trash" slot="icon-only"></ion-icon>
    </ion-item-option>
  </ion-item-options>
</ion-item-sliding>
