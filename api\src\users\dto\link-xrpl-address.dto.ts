import { IsString, IsNotEmpty, Matches } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class LinkXrplAddressDto {
  @ApiProperty({
    description: 'XRPL address to link to the user account',
    example: 'rPT1Sjq2YGrBMTttX4GZHjKu9yfEVRox89',
    pattern: '^r[a-zA-Z0-9]{24,34}$',
  })
  @IsString()
  @IsNotEmpty()
  @Matches(/^r[a-zA-Z0-9]{24,34}$/, {
    message:
      'Invalid XRPL address format. Must be a valid XRPL address starting with "r" followed by 24-34 alphanumeric characters.',
  })
  xrplAddress!: string;
}
