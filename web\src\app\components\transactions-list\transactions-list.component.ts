import {
  Component,
  Input,
  Output,
  EventEmitter,
  ChangeDetectionStrategy,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';
import { Transaction, User } from '../../types';

/**
 * Component that displays a list of recent transactions in a card format.
 *
 * Features:
 * - Shows recent transactions with proper formatting
 * - Displays transaction details including amount, accounts, and status
 * - Interactive transaction details modal
 * - Copy transaction hash functionality
 * - Empty state when no transactions
 * - Loading state with skeleton text
 *
 * @example
 * ```html
 * <app-transactions-list
 *   [transactions]="transactions$ | async"
 *   [currentUser]="currentUser$ | async"
 *   (viewAll)="onViewAllTransactions()"
 *   (viewDetails)="onViewTransactionDetails($event)"
 *   (copyHash)="onCopyTransactionHash($event, $event2)">
 * </app-transactions-list>
 * ```
 */
@Component({
  selector: 'app-transactions-list',
  templateUrl: './transactions-list.component.html',
  styleUrls: ['./transactions-list.component.scss'],
  standalone: true,
  imports: [CommonModule, IonicModule],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TransactionsListComponent {
  @Input() transactions: Transaction[] | null = null;
  @Input() currentUser: User | null = null;

  @Output() viewAll = new EventEmitter<void>();
  @Output() viewDetails = new EventEmitter<Transaction>();
  @Output() copyHash = new EventEmitter<{ hash: string; event: Event }>();

  getStatusColor(status: string): string {
    switch (status) {
      case 'success':
      case 'validated':
        return 'success';
      case 'pending':
        return 'warning';
      case 'failed':
        return 'danger';
      default:
        return 'medium';
    }
  }

  formatAddress(address: string): string {
    if (!address) return '';
    // Show first 6 and last 4 characters of the address
    if (address.length > 10) {
      return `${address.substring(0, 6)}...${address.substring(address.length - 4)}`;
    }
    return address;
  }

  isOutgoingPayment(transaction: Transaction): boolean {
    if (!this.currentUser?.xrplAddress || transaction.type !== 'Payment') {
      return false;
    }
    return transaction.account === this.currentUser.xrplAddress;
  }

  onViewAllClick(): void {
    this.viewAll.emit();
  }

  onViewDetailsClick(transaction: Transaction): void {
    this.viewDetails.emit(transaction);
  }

  onCopyHashClick(hash: string, event: Event): void {
    this.copyHash.emit({ hash, event });
  }
}
