{"name": "xrpl-launchpad-api", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "test:integration": "jest --testPathPatterns=integration --coverage", "test:xrpl-integration": "jest --testPathPatterns=xrpl.*integration --coverage", "test:launchpad-integration": "jest --testPathPatterns=launchpad.*integration --coverage", "test:cov:combined": "jest --coverage --coverageDirectory=../coverage-combined --collectCoverageFrom='src/**/*.ts' --collectCoverageFrom='!src/**/*.module.ts' --collectCoverageFrom='!src/**/*.dto.ts' --collectCoverageFrom='!src/**/*.entity.ts'", "typeorm": "typeorm-ts-node-commonjs", "migration:generate": "npm run typeorm -- migration:generate -d typeorm.config.ts", "migration:create": "npm run typeorm -- migration:create", "migration:run": "npm run typeorm -- migration:run -d typeorm.config.ts", "migration:revert": "npm run typeorm -- migration:revert -d typeorm.config.ts", "migration:show": "npm run typeorm -- migration:show -d typeorm.config.ts", "schema:drop": "npm run typeorm -- schema:drop -d typeorm.config.ts", "schema:sync": "npm run typeorm -- schema:sync -d typeorm.config.ts", "db:seed": "ts-node scripts/seed-database.ts"}, "dependencies": {"@nestjs/common": "^11.0.1", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.0.1", "@nestjs/jwt": "^11.0.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.0.1", "@nestjs/platform-socket.io": "^11.1.6", "@nestjs/schedule": "^6.0.0", "@nestjs/swagger": "^11.2.0", "@nestjs/throttler": "^6.4.0", "@nestjs/typeorm": "^11.0.0", "@nestjs/websockets": "^11.1.6", "@types/joi": "^17.2.2", "@types/pg": "^8.15.5", "bcryptjs": "^3.0.2", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "dotenv": "^17.2.1", "helmet": "^8.1.0", "joi": "^18.0.1", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "pg": "^8.16.3", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "typeorm": "^0.3.26", "xrpl": "^4.4.0"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.18.0", "@nestjs/cli": "^11.0.0", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.1", "@types/bcryptjs": "^3.0.0", "@types/express": "^5.0.0", "@types/jest": "^30.0.0", "@types/node": "^22.18.0", "@types/passport-jwt": "^4.0.1", "@types/passport-local": "^1.0.38", "@types/supertest": "^6.0.2", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.2", "globals": "^16.0.0", "jest": "^30.0.0", "jest-extended": "^6.0.0", "prettier": "^3.4.2", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.3", "typescript-eslint": "^8.20.0"}}