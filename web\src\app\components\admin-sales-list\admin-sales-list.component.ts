import {
  Component,
  Input,
  Output,
  EventEmitter,
  ChangeDetectionStrategy,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';
import { Sale } from '../../types';

/**
 * Admin-specific component that displays a list of sales with management capabilities.
 *
 * Features:
 * - Shows all sales with proper formatting
 * - Displays sale details including symbol, description, price, and dates
 * - Admin actions: edit, delete, open link
 * - Loading state support
 * - Empty state when no sales
 *
 * @example
 * ```html
 * <app-admin-sales-list
 *   [sales]="sales$ | async"
 *   [isLoading]="isLoading()"
 *   (editSale)="onEditSale($event)"
 *   (deleteSale)="onDeleteSale($event)"
 *   (openLink)="onOpenLink($event)"
 *   (scrollToForm)="onScrollToForm()">
 * </app-admin-sales-list>
 * ```
 */
@Component({
  selector: 'app-admin-sales-list',
  templateUrl: './admin-sales-list.component.html',
  styleUrls: ['./admin-sales-list.component.scss'],
  standalone: true,
  imports: [CommonModule, IonicModule],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AdminSalesListComponent {
  @Input() sales: Sale[] = [];
  @Input() isLoading: boolean = false;

  @Output() editSale = new EventEmitter<Sale>();
  @Output() deleteSale = new EventEmitter<string>();
  @Output() openLink = new EventEmitter<string>();
  @Output() scrollToForm = new EventEmitter<void>();

  getSaleStatusColor(status: string): string {
    switch (status) {
      case 'active':
        return 'success';
      case 'ended':
        return 'primary';
      case 'canceled':
        return 'danger';
      default:
        return 'medium';
    }
  }

  onEditSaleClick(sale: Sale): void {
    this.editSale.emit(sale);
  }

  onDeleteSaleClick(saleId: string): void {
    this.deleteSale.emit(saleId);
  }

  onOpenLinkClick(url: string): void {
    this.openLink.emit(url);
  }

  onScrollToFormClick(): void {
    this.scrollToForm.emit();
  }
}
