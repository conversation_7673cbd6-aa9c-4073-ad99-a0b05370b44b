{"activeSale": {"id": "sale1", "name": "Test Token Sale", "symbol": "TEST", "description": "A test token sale for E2E testing", "status": "active", "start": "2024-01-01T00:00:00Z", "end": "2024-12-31T23:59:59Z", "softCap": 1000, "hardCap": 5000, "pricePerToken": 0.1, "totalSupply": 10000, "collectionAddress": "rCollectionAddress123456789", "website": "https://test.com", "whitepaper": "https://test.com/whitepaper", "socialLinks": {"twitter": "https://twitter.com/test", "telegram": "https://t.me/test"}}, "endedSale": {"id": "sale2", "name": "Ended <PERSON>ken <PERSON>", "symbol": "ENDED", "description": "An ended token sale for testing claims", "status": "ended", "start": "2024-01-01T00:00:00Z", "end": "2024-01-31T23:59:59Z", "softCap": 1000, "hardCap": 5000, "pricePerToken": 0.1, "totalSupply": 10000, "collectionAddress": "rEndedCollectionAddress123456789", "website": "https://ended.com", "whitepaper": "https://ended.com/whitepaper", "socialLinks": {}}}