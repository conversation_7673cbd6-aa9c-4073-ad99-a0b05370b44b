import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { TestBed } from '@angular/core/testing';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { RouterModule } from '@angular/router';
import { BehaviorSubject, of } from 'rxjs';

import { AppComponent } from './app.component';
import { ApiService } from './services/api/api.service';

describe('AppComponent', () => {
  let mockApiService: jasmine.SpyObj<ApiService>;

  beforeEach(async () => {
    // Create a mock ApiService with currentUser$ returning null (not logged in)
    const apiServiceSpy = jasmine.createSpyObj(
      'ApiService',
      ['getMeIfNeeded', 'logout'],
      {
        currentUser$: new BehaviorSubject(null),
      }
    );

    // Mock the getMeIfNeeded method to return null (no user)
    apiServiceSpy.getMeIfNeeded.and.returnValue(of(null));

    await TestBed.configureTestingModule({
      imports: [
        AppComponent,
        RouterModule.forRoot([]),
        HttpClientTestingModule,
      ],
      providers: [{ provide: ApiService, useValue: apiServiceSpy }],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
    }).compileComponents();

    mockApiService = TestBed.inject(ApiService) as jasmine.SpyObj<ApiService>;
  });

  it('should create the app', () => {
    const fixture = TestBed.createComponent(AppComponent);
    const app = fixture.componentInstance;
    expect(app).toBeTruthy();
  });

  it('should have menu labels', () => {
    const fixture = TestBed.createComponent(AppComponent);
    fixture.detectChanges();

    // Check the component's data directly instead of DOM queries
    const component = fixture.componentInstance;
    const appPages = component.appPages();
    expect(appPages.length).toEqual(4); // Dashboard, Launchpad, Connect XRPL Account, Login
    expect(appPages[0].title).toEqual('Dashboard');
    expect(appPages[1].title).toEqual('Launchpad');
    expect(appPages[2].title).toEqual('Connect XRPL Account');
    expect(appPages[3].title).toEqual('Log in'); // Login/Logout is always present
  });

  it('should have urls', () => {
    const fixture = TestBed.createComponent(AppComponent);
    fixture.detectChanges();
    const app = fixture.nativeElement;
    const menuItems = app.querySelectorAll('ion-item');
    expect(menuItems.length).toEqual(8); // 4 app pages + 3 labels + 1 theme toggle

    // Check that the routerLink directive is applied correctly
    // The routerLink should be present as a property binding
    const firstItem = menuItems[0];
    const secondItem = menuItems[1];

    // Verify the items have the correct routing behavior
    expect(firstItem).toBeTruthy();
    expect(secondItem).toBeTruthy();

    // Check that the items are properly configured for routing
    // Since we're using routerLink, we can verify the component's appPages data
    const component = fixture.componentInstance;
    const appPages = component.appPages();
    expect(appPages[0].url).toEqual('/dashboard');
    expect(appPages[1].url).toEqual('/launchpad');
    expect(appPages[2].url).toEqual('/xrpl-connect');
    expect(appPages[3].url).toEqual('/login'); // Login/Logout item
  });
});
