import { Injectable, inject } from '@angular/core';
import {
  CanActivate,
  Router,
  ActivatedRouteSnapshot,
  RouterStateSnapshot,
} from '@angular/router';
import { Observable, of } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { ApiService } from '../services/api/api.service';

@Injectable({
  providedIn: 'root',
})
export class AdminRoleGuard implements CanActivate {
  private apiService = inject(ApiService);
  private router = inject(Router);

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> {
    const token = localStorage.getItem('access_token');

    if (!token) {
      this.router.navigate(['/login']);
      return of(false);
    }

    // Check if user is authenticated and has admin role
    return this.apiService.getMeIfNeeded().pipe(
      map(user => {
        if (!user || user.role !== 'admin') {
          this.router.navigate(['/dashboard']);
          return false;
        }
        return true;
      }),
      catchError(error => {
        localStorage.removeItem('access_token');
        localStorage.removeItem('user');
        this.router.navigate(['/login']);
        return of(false);
      })
    );
  }
}
