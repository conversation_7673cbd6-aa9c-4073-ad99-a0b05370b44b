import {
  Component,
  Input,
  Output,
  EventEmitter,
  ChangeDetectionStrategy,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';
import { Sale } from '../../types';

/**
 * Component that displays a list of active sales in a card format.
 *
 * Features:
 * - Shows active sales with proper formatting
 * - Displays sale details including symbol, description, price, and dates
 * - Interactive sale navigation
 * - Empty state when no sales
 * - Loading state with skeleton text
 *
 * @example
 * ```html
 * <app-sales-list
 *   [sales]="activeSales$ | async"
 *   (viewAll)="onViewAllSales()"
 *   (viewSale)="onViewSale($event)">
 * </app-sales-list>
 * ```
 */
@Component({
  selector: 'app-sales-list',
  templateUrl: './sales-list.component.html',
  styleUrls: ['./sales-list.component.scss'],
  standalone: true,
  imports: [CommonModule, IonicModule],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SalesListComponent {
  @Input() sales: Sale[] | null = null;

  @Output() viewAll = new EventEmitter<void>();
  @Output() viewSale = new EventEmitter<string>();

  getSaleStatusColor(status: string): string {
    switch (status) {
      case 'active':
        return 'success';
      case 'ended':
        return 'primary';
      case 'canceled':
        return 'danger';
      default:
        return 'medium';
    }
  }

  onViewAllClick(): void {
    this.viewAll.emit();
  }

  onViewSaleClick(saleId: string): void {
    this.viewSale.emit(saleId);
  }
}
