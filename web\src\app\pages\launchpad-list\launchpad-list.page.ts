import {
  Component,
  ChangeDetectionStrategy,
  inject,
  OnInit,
  signal,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';
import { Router } from '@angular/router';
import { Observable, of } from 'rxjs';
import { map, catchError, startWith } from 'rxjs/operators';
import { ApiService } from '../../services/api/api.service';
import { Sale } from '../../types';

@Component({
  selector: 'app-launchpad-list',
  templateUrl: './launchpad-list.page.html',
  styleUrls: ['./launchpad-list.page.scss'],
  standalone: true,
  imports: [CommonModule, IonicModule],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class LaunchpadListPage implements OnInit {
  private apiService = inject(ApiService);
  private router = inject(Router);

  // Observable properties for use with async pipe
  sales$!: Observable<Sale[]>;
  filteredSales$!: Observable<Sale[]>;

  // State
  isLoading = signal<boolean>(false);
  filterStatus = signal<string>('all');

  ngOnInit() {
    this.loadData();
  }

  private loadData() {
    this.isLoading.set(true);

    // Initialize observables - these will be consumed by async pipe in template
    this.sales$ = this.apiService.getSales().pipe(
      catchError(() => of([])),
      startWith([])
    );

    // Create filtered sales observable
    this.filteredSales$ = this.sales$.pipe(
      map(sales => {
        const filter = this.filterStatus();
        if (filter === 'all') {
          return sales;
        }
        return sales.filter(sale => sale.status === filter);
      })
    );

    // Set loading to false after a short delay
    setTimeout(() => this.isLoading.set(false), 500);
  }

  onViewSale(saleId: string): void {
    this.router.navigate(['/launchpad/detail', saleId]);
  }

  onSaleAction(sale: Sale): void {
    switch (sale.status) {
      case 'active':
        // Navigate to sale detail page for active sales (which includes contribution functionality)
        this.router.navigate(['/launchpad/detail', sale.id]);
        break;
      case 'ended':
      case 'canceled':
      default:
        // Navigate to sale details for ended/canceled sales
        this.router.navigate(['/launchpad/detail', sale.id]);
        break;
    }
  }

  onRefreshData(): void {
    this.loadData();
  }

  onFilterChange(value: string | number | null): void {
    const filterValue = typeof value === 'string' ? value : 'all';
    this.filterStatus.set(filterValue);

    // Update filtered sales observable
    this.filteredSales$ = this.sales$.pipe(
      map(sales => {
        if (filterValue === 'all') {
          return sales;
        }
        return sales.filter(sale => sale.status === filterValue);
      })
    );
  }

  getStatusColor(status: string): string {
    switch (status) {
      case 'active':
        return 'success';
      case 'ended':
        return 'primary';
      case 'canceled':
        return 'danger';
      default:
        return 'medium';
    }
  }

  getStatusIcon(status: string): string {
    switch (status) {
      case 'active':
        return 'play-circle';
      case 'ended':
        return 'checkmark-circle';
      case 'canceled':
        return 'close-circle';
      default:
        return 'help-circle';
    }
  }

  getProgressPercentage(softCap: number, hardCap: number): number {
    if (hardCap === 0) return 0;
    return Math.min((softCap / hardCap) * 100, 100);
  }

  formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString();
  }
}
