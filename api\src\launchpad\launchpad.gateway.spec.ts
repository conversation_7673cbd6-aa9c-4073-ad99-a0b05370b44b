import { Test, TestingModule } from '@nestjs/testing';
import { LaunchpadGateway } from './launchpad.gateway';
import { LaunchpadService } from './launchpad.service';
import { XrplService } from '../xrpl/xrpl.service';
import { Sale, SaleStatus } from './entities/sale.entity';
import { Contribution } from './entities/contribution.entity';
import { Subject } from 'rxjs';
import { Server, Socket } from 'socket.io';
import { SaleContributionEvent } from '../xrpl/xrpl.service';
import { WsJwtAuthGuard } from '../auth/guards/ws-jwt-auth.guard';

// Mock the WsJwtAuthGuard
jest.mock('../auth/guards/ws-jwt-auth.guard');

describe('LaunchpadGateway', () => {
  let gateway: LaunchpadGateway;
  let launchpadService: jest.Mocked<LaunchpadService>;
  let xrplService: jest.Mocked<XrplService>;
  let mockServer: jest.Mocked<Server>;
  let mockSocket: jest.Mocked<Socket>;

  const mockUser = {
    id: 'user123',
    email: '<EMAIL>',
  };

  const mockSale: Sale = {
    id: 'sale123',
    name: 'Test Sale',
    symbol: 'TEST',
    description: 'Test sale description',
    price: 0.1,
    softCap: 1000,
    hardCap: 5000,
    start: new Date(Date.now() + 86400000),
    end: new Date(Date.now() + 172800000),
    status: SaleStatus.ACTIVE,
    collectionAddress: 'rTestCollectionAddress123456789',
    createdAt: new Date(),
    updatedAt: new Date(),
    contributions: [],
  } as Sale;

  const mockContributionEvent: SaleContributionEvent = {
    saleId: 'sale123',
    collectionAddress: 'rTestCollectionAddress123456789',
    destinationTag: 12345,
    transactionHash: 'txHash123',
    sender: 'rTestSenderAddress123456789',
    amount: 100,
    timestamp: new Date(),
  };

  beforeEach(async () => {
    const mockLaunchpadService = {
      findSaleById: jest.fn(),
      processContributionEvent: jest.fn(),
    };

    const mockXrplService = {
      subscribeToSale: jest.fn(),
      unsubscribeFromSale: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        LaunchpadGateway,
        {
          provide: LaunchpadService,
          useValue: mockLaunchpadService,
        },
        {
          provide: XrplService,
          useValue: mockXrplService,
        },
      ],
    })
      .overrideGuard(WsJwtAuthGuard)
      .useValue({ canActivate: jest.fn(() => true) })
      .compile();

    gateway = module.get<LaunchpadGateway>(LaunchpadGateway);
    launchpadService = module.get(LaunchpadService);
    xrplService = module.get(XrplService);

    // Mock Socket.IO server
    mockServer = {
      to: jest.fn().mockReturnThis(),
      emit: jest.fn(),
    } as any;

    // Mock Socket.IO client with proper typing
    mockSocket = {
      id: 'socket123',
      emit: jest.fn(),
      join: jest.fn(),
      leave: jest.fn(),
      user: mockUser,
      // Add missing Socket properties
      nsp: {} as any,
      client: {} as any,
      pid: 'pid123',
      server: {} as any,
      adapter: {} as any,
      acks: new Map(),
      connected: true,
      disconnected: false,
      handshake: {} as any,
      rooms: new Set(),
      data: {},
      operatorEmit: jest.fn(),
      fetchSockets: jest.fn(),
      addSockets: jest.fn(),
      delSockets: jest.fn(),
      disconnectSockets: jest.fn(),
      serverSideEmit: jest.fn(),
      emitWithAck: jest.fn(),
      timeout: jest.fn(),
      compress: jest.fn(),
      to: jest.fn(),
      in: jest.fn(),
      except: jest.fn(),
      onAny: jest.fn(),
      prependAny: jest.fn(),
      offAny: jest.fn(),
      listenersAny: jest.fn(),
      eventNames: jest.fn(),
    } as any;

    // Set the server property
    (gateway as any).server = mockServer;
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('handleConnection', () => {
    it('should log client connection', () => {
      const logSpy = jest.spyOn(gateway['logger'], 'log');

      gateway.handleConnection(mockSocket);

      expect(logSpy).toHaveBeenCalledWith('Client connected: socket123');
    });
  });

  describe('handleDisconnect', () => {
    it('should log client disconnection and remove from rooms', () => {
      const logSpy = jest.spyOn(gateway['logger'], 'log');
      const removeClientSpy = jest.spyOn(
        gateway as any,
        'removeClientFromAllRooms',
      );

      gateway.handleDisconnect(mockSocket);

      expect(logSpy).toHaveBeenCalledWith('Client disconnected: socket123');
      expect(removeClientSpy).toHaveBeenCalledWith(mockSocket);
    });
  });

  describe('handleJoinSale', () => {
    it('should join sale successfully', async () => {
      const data = { saleId: 'sale123' };

      launchpadService.findSaleById.mockResolvedValue(mockSale);

      await gateway.handleJoinSale(mockSocket, data);

      expect(launchpadService.findSaleById).toHaveBeenCalledWith('sale123');
      expect(mockSocket.join).toHaveBeenCalledWith('sale:sale123');
      expect(mockSocket.emit).toHaveBeenCalledWith('joinedSale', {
        saleId: 'sale123',
        sale: mockSale,
      });
    });

    it('should handle missing authentication', async () => {
      const socketWithoutUser = { ...mockSocket, user: undefined };
      const data = { saleId: 'sale123' };

      await gateway.handleJoinSale(socketWithoutUser as any, data);

      expect(socketWithoutUser.emit).toHaveBeenCalledWith('error', {
        message: 'Authentication required',
      });
      expect(launchpadService.findSaleById).not.toHaveBeenCalled();
    });

    it('should handle missing sale ID', async () => {
      const data = { saleId: '' };

      await gateway.handleJoinSale(mockSocket, data);

      expect(mockSocket.emit).toHaveBeenCalledWith('error', {
        message: 'Sale ID is required',
      });
      expect(launchpadService.findSaleById).not.toHaveBeenCalled();
    });

    it('should handle sale not found', async () => {
      const data = { saleId: 'nonexistent' };

      launchpadService.findSaleById.mockResolvedValue(null as any);

      await gateway.handleJoinSale(mockSocket, data);

      expect(mockSocket.emit).toHaveBeenCalledWith('error', {
        message: 'Sale not found',
      });
    });

    it('should start sale monitoring when first user joins', async () => {
      const data = { saleId: 'sale123' };

      launchpadService.findSaleById.mockResolvedValue(mockSale);

      await gateway.handleJoinSale(mockSocket, data);

      expect(xrplService.subscribeToSale).toHaveBeenCalledWith(
        'sale123',
        mockSale.collectionAddress,
        expect.any(Number), // Generated destination tag
      );
    });

    it('should send current sale progress when joining', async () => {
      const data = { saleId: 'sale123' };
      const saleWithContributions = {
        ...mockSale,
        contributions: [
          {
            id: 'contribution123',
            amount: 500,
            userId: 'user123',
            saleId: 'sale123',
            txHash: 'txHash123',
            status: 'pending',
            createdAt: new Date(),
            updatedAt: new Date(),
            user: { id: 'user123' },
            sale: mockSale,
          } as Contribution,
        ],
      };

      launchpadService.findSaleById.mockResolvedValue(saleWithContributions);

      await gateway.handleJoinSale(mockSocket, data);

      expect(mockSocket.emit).toHaveBeenCalledWith('saleProgress', {
        saleId: 'sale123',
        totalRaised: 500,
        progressPercent: 10, // (500/5000) * 100
        totalContributors: 1,
        lastContribution: {
          amount: 500,
          sender: 'user123',
          timestamp: expect.any(Date),
        },
      });
    });

    it('should handle join sale errors gracefully', async () => {
      const data = { saleId: 'sale123' };

      launchpadService.findSaleById.mockRejectedValue(
        new Error('Service error'),
      );

      await gateway.handleJoinSale(mockSocket, data);

      expect(mockSocket.emit).toHaveBeenCalledWith('error', {
        message: 'Failed to join sale',
      });
    });
  });

  describe('handleLeaveSale', () => {
    it('should leave sale successfully', async () => {
      const data = { saleId: 'sale123' };

      // Set up existing room membership
      (gateway as any).userSockets.set(mockUser.id, [mockSocket]);
      (gateway as any).saleRooms.set('sale123', new Set([mockUser.id]));

      await gateway.handleLeaveSale(mockSocket, data);

      expect(mockSocket.leave).toHaveBeenCalledWith('sale:sale123');
      expect(mockSocket.emit).toHaveBeenCalledWith('leftSale', {
        saleId: 'sale123',
      });
    });

    it('should handle missing authentication', async () => {
      const socketWithoutUser = { ...mockSocket, user: undefined };
      const data = { saleId: 'sale123' };

      await gateway.handleLeaveSale(socketWithoutUser as any, data);

      expect(socketWithoutUser.emit).toHaveBeenCalledWith('error', {
        message: 'Authentication required',
      });
    });

    it('should handle missing sale ID', async () => {
      const data = { saleId: '' };

      await gateway.handleLeaveSale(mockSocket, data);

      expect(mockSocket.emit).toHaveBeenCalledWith('error', {
        message: 'Sale ID is required',
      });
    });

    it('should stop monitoring sale when no users remain', async () => {
      const data = { saleId: 'sale123' };

      // Set up existing room membership
      (gateway as any).userSockets.set(mockUser.id, [mockSocket]);
      (gateway as any).saleRooms.set('sale123', new Set([mockUser.id]));

      await gateway.handleLeaveSale(mockSocket, data);

      expect(mockSocket.leave).toHaveBeenCalledWith('sale:sale123');
      expect(mockSocket.emit).toHaveBeenCalledWith('leftSale', {
        saleId: 'sale123',
      });

      // The sale room should be removed since no users remain
      expect((gateway as any).saleRooms.has('sale123')).toBe(false);
    });

    it('should handle leave sale errors gracefully', async () => {
      const data = { saleId: 'sale123' };

      // Set up existing room memberships
      (gateway as any).userSockets.set(mockUser.id, [mockSocket]);
      (gateway as any).saleRooms.set('sale123', new Set([mockUser.id]));

      mockSocket.leave.mockRejectedValue(new Error('Leave error'));

      await gateway.handleLeaveSale(mockSocket, data);

      expect(mockSocket.emit).toHaveBeenCalledWith('error', {
        message: 'Failed to leave sale',
      });
    });
  });

  describe('startSaleMonitoring', () => {
    it('should start monitoring sale successfully', () => {
      const mockSubscription = new Subject<SaleContributionEvent>();
      xrplService.subscribeToSale.mockReturnValue(mockSubscription);

      (gateway as any).startSaleMonitoring(mockSale);

      expect(xrplService.subscribeToSale).toHaveBeenCalledWith(
        mockSale.id,
        mockSale.collectionAddress,
        expect.any(Number), // Generated destination tag
      );

      expect((gateway as any).saleSubscriptions.has(mockSale.id)).toBe(true);
    });

    it('should handle monitoring start errors gracefully', () => {
      xrplService.subscribeToSale.mockImplementation(() => {
        throw new Error('Monitoring error');
      });

      expect(() => {
        (gateway as any).startSaleMonitoring(mockSale);
      }).not.toThrow();
    });
  });

  describe('stopSaleMonitoring', () => {
    it('should stop monitoring sale successfully', () => {
      const mockSubscription = new Subject<SaleContributionEvent>();
      (gateway as any).saleSubscriptions.set('sale123', mockSubscription);

      (gateway as any).stopSaleMonitoring('sale123');

      expect(xrplService.unsubscribeFromSale).toHaveBeenCalledWith('sale123');
      expect((gateway as any).saleSubscriptions.has('sale123')).toBe(false);
    });

    it('should do nothing when sale not being monitored', () => {
      expect(() => {
        (gateway as any).stopSaleMonitoring('nonexistent');
      }).not.toThrow();
    });
  });

  describe('handleContributionEvent', () => {
    it('should process contribution event and broadcast updates', async () => {
      const saleWithContributions = {
        ...mockSale,
        contributions: [
          {
            id: 'contribution123',
            amount: 500,
            userId: 'user123',
            saleId: 'sale123',
            txHash: 'txHash123',
            status: 'pending',
            createdAt: new Date(),
            updatedAt: new Date(),
            user: { id: 'user123' },
            sale: mockSale,
          } as Contribution,
        ],
      };

      launchpadService.processContributionEvent.mockResolvedValue(undefined);
      launchpadService.findSaleById.mockResolvedValue(saleWithContributions);

      await (gateway as any).handleContributionEvent(mockContributionEvent);

      expect(launchpadService.processContributionEvent).toHaveBeenCalledWith(
        mockContributionEvent,
      );
      expect(launchpadService.findSaleById).toHaveBeenCalledWith(
        mockContributionEvent.saleId,
      );

      expect(mockServer.to).toHaveBeenCalledWith('sale:sale123');
      expect(mockServer.emit).toHaveBeenCalledWith('contributionReceived', {
        event: mockContributionEvent,
        progress: {
          saleId: 'sale123',
          totalRaised: 500,
          progressPercent: 10,
          totalContributors: 1,
          lastContribution: {
            amount: 500,
            sender: 'user123',
            timestamp: expect.any(Date),
          },
        },
      });
    });

    it('should handle contribution event processing errors gracefully', async () => {
      launchpadService.processContributionEvent.mockRejectedValue(
        new Error('Processing error'),
      );

      await expect(
        (gateway as any).handleContributionEvent(mockContributionEvent),
      ).resolves.not.toThrow();
    });

    it('should handle sale not found gracefully', async () => {
      launchpadService.processContributionEvent.mockResolvedValue(undefined);
      launchpadService.findSaleById.mockResolvedValue(null as any);

      await expect(
        (gateway as any).handleContributionEvent(mockContributionEvent),
      ).resolves.not.toThrow();

      expect(mockServer.to).not.toHaveBeenCalled();
      expect(mockServer.emit).not.toHaveBeenCalled();
    });
  });

  describe('getSaleProgress', () => {
    it('should calculate sale progress correctly', () => {
      const saleWithContributions = {
        ...mockSale,
        contributions: [
          {
            id: 'contribution123',
            amount: 2000,
            userId: 'user123',
            saleId: 'sale123',
            txHash: 'txHash123',
            status: 'pending',
            createdAt: new Date(Date.now() - 3600000), // 1 hour ago
            updatedAt: new Date(),
            user: { id: 'user123' },
            sale: mockSale,
          } as Contribution,
          {
            id: 'contribution456',
            amount: 1000,
            userId: 'user456',
            saleId: 'sale123',
            txHash: 'txHash456',
            status: 'pending',
            createdAt: new Date(), // Now
            updatedAt: new Date(),
            user: { id: 'user456' },
            sale: mockSale,
          } as Contribution,
        ],
      };

      const progress = (gateway as any).getSaleProgress(saleWithContributions);

      expect(progress).toEqual({
        saleId: 'sale123',
        totalRaised: 3000,
        progressPercent: 60, // (3000/5000) * 100
        totalContributors: 2,
        lastContribution: {
          amount: 1000,
          sender: 'user456',
          timestamp: expect.any(Date),
        },
      });
    });

    it('should handle sale with no contributions', () => {
      const progress = (gateway as any).getSaleProgress(mockSale);

      expect(progress).toEqual({
        saleId: 'sale123',
        totalRaised: 0,
        progressPercent: 0,
        totalContributors: 0,
      });
    });

    it('should cap progress at 100%', () => {
      const saleWithHighContributions = {
        ...mockSale,
        contributions: [
          {
            id: 'contribution123',
            amount: 6000, // Above hard cap
            userId: 'user123',
            saleId: 'sale123',
            txHash: 'txHash123',
            status: 'pending',
            createdAt: new Date(),
            updatedAt: new Date(),
            user: { id: 'user123' },
            sale: mockSale,
          } as Contribution,
        ],
      };

      const progress = (gateway as any).getSaleProgress(
        saleWithHighContributions,
      );

      expect(progress.progressPercent).toBe(100);
    });

    it('should handle calculation errors gracefully', () => {
      const invalidSale = {
        ...mockSale,
        hardCap: 0, // This will cause division by zero
      };

      const progress = (gateway as any).getSaleProgress(invalidSale);

      expect(progress).toEqual({
        saleId: 'sale123',
        totalRaised: 0,
        progressPercent: NaN,
        totalContributors: 0,
        lastContribution: undefined,
      });
    });
  });

  describe('generateDestinationTag', () => {
    it('should generate consistent destination tags for same sale ID', () => {
      const tag1 = (gateway as any).generateDestinationTag('sale123');
      const tag2 = (gateway as any).generateDestinationTag('sale123');

      expect(tag1).toBe(tag2);
      expect(typeof tag1).toBe('number');
      expect(tag1).toBeGreaterThanOrEqual(0);
      expect(tag1).toBeLessThan(1000000);
    });

    it('should generate different destination tags for different sale IDs', () => {
      const tag1 = (gateway as any).generateDestinationTag('sale123');
      const tag2 = (gateway as any).generateDestinationTag('sale456');

      expect(tag1).not.toBe(tag2);
    });
  });

  describe('removeClientFromAllRooms', () => {
    it('should remove client from all rooms', () => {
      // Set up existing room memberships
      (gateway as any).userSockets.set(mockUser.id, [mockSocket]);
      (gateway as any).saleRooms.set('sale123', new Set([mockUser.id]));
      (gateway as any).saleRooms.set('sale456', new Set([mockUser.id]));

      (gateway as any).removeClientFromAllRooms(mockSocket);

      expect((gateway as any).userSockets.has(mockUser.id)).toBe(false);
      expect((gateway as any).saleRooms.has('sale123')).toBe(false);
      expect((gateway as any).saleRooms.has('sale456')).toBe(false);
    });

    it('should stop monitoring sales when user has no more connections', () => {
      // Set up existing room memberships
      (gateway as any).userSockets.set(mockUser.id, [mockSocket]);
      (gateway as any).saleRooms.set('sale123', new Set([mockUser.id]));

      (gateway as any).removeClientFromAllRooms(mockSocket);

      // The sale room should be removed since no users remain
      expect((gateway as any).saleRooms.has('sale123')).toBe(false);
    });

    it('should handle user without rooms gracefully', () => {
      expect(() => {
        (gateway as any).removeClientFromAllRooms(mockSocket);
      }).not.toThrow();
    });
  });

  describe('public methods', () => {
    it('should broadcast sale updates to room', () => {
      const update = { type: 'status_change', status: SaleStatus.ENDED };
      (gateway as any).saleRooms.set('sale123', new Set([mockUser.id]));

      gateway.broadcastSaleUpdate('sale123', update);

      expect(mockServer.to).toHaveBeenCalledWith('sale:sale123');
      expect(mockServer.emit).toHaveBeenCalledWith('saleUpdate', update);
    });

    it('should return active users in sale', () => {
      (gateway as any).saleRooms.set(
        'sale123',
        new Set([mockUser.id, 'user456']),
      );

      const activeUsers = gateway.getActiveUsersInSale('sale123');

      expect(activeUsers).toEqual(['user123', 'user456']);
    });

    it('should return empty array for non-existent sale', () => {
      const activeUsers = gateway.getActiveUsersInSale('nonexistent');

      expect(activeUsers).toEqual([]);
    });
  });

  describe('error handling', () => {
    it('should handle XRPL service errors gracefully', () => {
      xrplService.subscribeToSale.mockImplementation(() => {
        throw new Error('XRPL service error');
      });

      expect(() => {
        (gateway as any).startSaleMonitoring(mockSale);
      }).not.toThrow();
    });

    it('should handle missing user data gracefully', () => {
      const socketWithoutUser = { ...mockSocket, user: null };
      const data = { saleId: 'sale123' };

      expect(() => {
        void gateway.handleJoinSale(socketWithoutUser as any, data);
      }).not.toThrow();
    });

    it('should handle service errors gracefully', async () => {
      const data = { saleId: 'sale123' };

      launchpadService.findSaleById.mockRejectedValue(
        new Error('Service error'),
      );

      await expect(
        gateway.handleJoinSale(mockSocket, data),
      ).resolves.not.toThrow();
    });
  });
});
