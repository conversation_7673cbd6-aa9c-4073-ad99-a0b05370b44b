import * as Jo<PERSON> from 'joi';

export interface DatabaseConfig {
  host: string;
  port: number;
  name: string;
  username: string;
  password: string;
}

export interface JwtConfig {
  secret: string;
  refreshSecret: string;
  expiresIn: string;
  refreshExpiresIn: string;
}

export interface XrplConfig {
  testnetUrl: string;
  issuerSeed: string;
}

export interface AppConfig {
  port: number;
  nodeEnv: string;
  frontendUrl: string;
}

export interface ThrottleConfig {
  ttl: number;
  limit: number;
}

export interface Configuration {
  database: DatabaseConfig;
  jwt: JwtConfig;
  xrpl: XrplConfig;
  app: AppConfig;
  throttle: ThrottleConfig;
}

export const validationSchema = Joi.object({
  // Database
  DATABASE_HOST: Joi.string().default('localhost'),
  DATABASE_PORT: Joi.number().port().default(5432),
  DATABASE_NAME: Joi.string().default('xrpl_launchpad'),
  DATABASE_USERNAME: Joi.string().default('postgres'),
  DATABASE_PASSWORD: Joi.string().default('password'),

  // JWT
  JWT_SECRET: Joi.string().min(32).required(),
  JWT_REFRESH_SECRET: Joi.string().min(32).required(),
  JWT_EXPIRES_IN: Joi.string().default('15m'),
  JWT_REFRESH_EXPIRES_IN: Joi.string().default('7d'),

  // XRPL
  XRPL_WS_URL: Joi.string()
    .uri()
    .default('wss://s.altnet.rippletest.net:51233'),
  XRPL_ISSUER_SEED: Joi.string().min(29).max(32).required(),

  // App
  PORT: Joi.number().port().default(3000),
  NODE_ENV: Joi.string()
    .valid('development', 'production', 'test')
    .default('development'),
  FRONTEND_URL: Joi.string().uri().default('http://localhost:8100'),

  // Throttle
  THROTTLE_TTL: Joi.number().default(60),
  THROTTLE_LIMIT: Joi.number().default(100),
});

export default (): Configuration => ({
  database: {
    host: process.env.DATABASE_HOST || 'localhost',
    port: parseInt(process.env.DATABASE_PORT || '5432', 10),
    name: process.env.DATABASE_NAME || 'xrpl_launchpad',
    username: process.env.DATABASE_USERNAME || 'postgres',
    password: process.env.DATABASE_PASSWORD || 'password',
  },
  jwt: {
    secret: process.env.JWT_SECRET || 'your-super-secret-jwt-key',
    refreshSecret:
      process.env.JWT_REFRESH_SECRET || 'your-super-secret-refresh-key',
    expiresIn: process.env.JWT_EXPIRES_IN || '15m',
    refreshExpiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '7d',
  },
  xrpl: {
    testnetUrl:
      process.env.XRPL_WS_URL || 'wss://s.altnet.rippletest.net:51233',
    issuerSeed: process.env.XRPL_ISSUER_SEED || '',
  },
  app: {
    port: parseInt(process.env.PORT || '3000', 10),
    nodeEnv: process.env.NODE_ENV || 'development',
    frontendUrl: process.env.FRONTEND_URL || 'http://localhost:8100',
  },
  throttle: {
    ttl: parseInt(process.env.THROTTLE_TTL || '60', 10),
    limit: parseInt(process.env.THROTTLE_TTL || '100', 10),
  },
});
