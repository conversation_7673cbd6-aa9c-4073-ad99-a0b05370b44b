{"name": "xrpl-launchpad", "version": "0.0.1", "private": true, "description": "XRPL Launchpad monorepo with NestJS backend and Angular frontend", "workspaces": ["api", "web"], "scripts": {"api:dev": "npm run -w api start:dev", "api:build": "npm run -w api build", "api:test": "npm run -w api test", "web:dev": "npm run -w web start", "web:build": "npm run -w web build", "web:test": "npm run -w web test", "dev": "concurrently \"npm run -w api start:dev\" \"npm run -w web start\"", "build": "npm run -w api build && npm run -w web build", "lint": "npm run -w api lint && npm run -w web lint", "lint:check": "npm run -w api lint -- --max-warnings=0 && npm run -w web lint -- --max-warnings=0", "format": "prettier --write \"**/*.{ts,js,json,md,scss,css,html}\"", "format:check": "prettier --check \"**/*.{ts,js,json,md,scss,css,html}\"", "test": "npm run -w api test && npm run -w web test -- --browsers=ChromeHeadless --watch=false", "test:ci": "npm run -w api test && npm run -w web test:ci", "test:cov": "npm run -w api test:cov", "test:cov:combined": "npm run -w api test:cov:combined", "test:e2e": "npm run -w api test:e2e", "test:integration": "jest --testPathPattern=integration --coverage", "test:xrpl-integration": "jest --testPathPattern=xrpl.*integration --coverage", "test:launchpad-integration": "jest --testPathPattern=launchpad.*integration --coverage", "docs": "npm run -w api docs && npm run -w web docs", "prepare": "husky install", "ci": "npm run lint:check && npm run build && npm run test"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "concurrently": "^9.2.1", "eslint": "^9.34.0", "husky": "^9.1.7", "lint-staged": "^16.1.6", "prettier": "^3.6.2", "ts-jest": "^29.4.1"}, "lint-staged": {"*.{ts,js,json,md,scss,css,html}": "prettier --write"}}