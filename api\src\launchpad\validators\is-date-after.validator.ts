import {
  ValidatorConstraint,
  ValidatorConstraintInterface,
  ValidationArguments,
} from 'class-validator';

@ValidatorConstraint({ name: 'isDateAfter', async: false })
export class IsDateAfter implements ValidatorConstraintInterface {
  validate(endDate: string, args: ValidationArguments) {
    const [startDateProperty] = args.constraints;
    const startDate = (args.object as any)[startDateProperty];

    if (!startDate || !endDate) {
      return false;
    }

    const start = new Date(startDate);
    const end = new Date(endDate);

    return end > start;
  }

  defaultMessage(args: ValidationArguments) {
    const [startDateProperty] = args.constraints;
    return `${args.property} must be after ${startDateProperty}`;
  }
}
