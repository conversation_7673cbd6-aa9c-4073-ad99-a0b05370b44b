ion-card {
  margin-bottom: 16px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  ion-card-header {
    padding: 16px 16px 8px 16px;

    ion-card-title {
      display: flex;
      align-items: center;
      font-size: 18px;
      font-weight: 600;
      color: var(--ion-color-dark);

      ion-icon {
        margin-right: 8px;
        font-size: 20px;
      }
    }
  }

  ion-card-content {
    padding: 8px 16px 16px 16px;
  }
}

.balance-amount {
  display: flex;
  align-items: baseline;
  margin-bottom: 16px;

  .currency {
    font-size: 16px;
    color: var(--ion-color-medium);
    margin-right: 8px;
  }

  .amount {
    font-size: 32px;
    font-weight: 700;
    color: var(--ion-color-primary);
  }
}

.balance-details {
  p {
    margin: 4px 0;
    font-size: 14px;
    color: var(--ion-color-medium);

    &.address {
      font-family: monospace;
      word-break: break-all;
    }

    &.ledger {
      font-weight: 500;
    }
  }
}

@media (max-width: 576px) {
  .balance-amount .amount {
    font-size: 28px;
  }
}
