import {
  ValidatorConstraint,
  ValidatorConstraintInterface,
  ValidationArguments,
} from 'class-validator';

@ValidatorConstraint({ name: 'isCapsValid', async: false })
export class IsCapsValid implements ValidatorConstraintInterface {
  validate(hardCap: number, args: ValidationArguments) {
    const { object } = args;
    const softCap = (object as any).softCap;

    if (!softCap || !hardCap) {
      return false;
    }

    return hardCap > softCap;
  }

  defaultMessage(args: ValidationArguments) {
    return `${args.property} must be greater than softCap`;
  }
}
