# XRPL Launchpad Docker Operations Script
# PowerShell script for common Docker operations

param(
    [Parameter(Position=0)]
    [ValidateSet("up", "down", "build", "logs", "restart", "clean", "reset-db", "help")]
    [string]$Action = "help"
)

function Show-Help {
    Write-Host @"
XRPL Launchpad Docker Operations Script

Usage: .\docker-ops.ps1 [Action]

Actions:
  up          - Start all services (docker-compose up -d)
  down        - Stop all services (docker-compose down)
  build       - Build and start all services (docker-compose up --build -d)
  logs        - Show logs for all services (docker-compose logs -f)
  restart     - Restart all services
  clean       - Stop and remove containers, networks, and images
  reset-db    - Stop services, remove database volume, and restart
  help        - Show this help message (default)

Examples:
  .\docker-ops.ps1 up
  .\docker-ops.ps1 build
  .\docker-ops.ps1 logs
"@
}

function Start-Services {
    Write-Host "Starting XRPL Launchpad services..." -ForegroundColor Green
    docker-compose up -d
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Services started successfully!" -ForegroundColor Green
        Write-Host "Frontend: http://localhost:8080" -ForegroundColor Cyan
        Write-Host "Backend:  http://localhost:3000" -ForegroundColor Cyan
        Write-Host "Database: localhost:5432" -ForegroundColor Cyan
    } else {
        Write-Host "Failed to start services!" -ForegroundColor Red
    }
}

function Stop-Services {
    Write-Host "Stopping XRPL Launchpad services..." -ForegroundColor Yellow
    docker-compose down
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Services stopped successfully!" -ForegroundColor Green
    } else {
        Write-Host "Failed to stop services!" -ForegroundColor Red
    }
}

function Build-And-Start-Services {
    Write-Host "Building and starting XRPL Launchpad services..." -ForegroundColor Green
    docker-compose up --build -d
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Services built and started successfully!" -ForegroundColor Green
        Write-Host "Frontend: http://localhost:8080" -ForegroundColor Cyan
        Write-Host "Backend:  http://localhost:3000" -ForegroundColor Cyan
        Write-Host "Database: localhost:5432" -ForegroundColor Cyan
    } else {
        Write-Host "Failed to build and start services!" -ForegroundColor Red
    }
}

function Show-Logs {
    Write-Host "Showing logs for all services (Press Ctrl+C to exit)..." -ForegroundColor Green
    docker-compose logs -f
}

function Restart-Services {
    Write-Host "Restarting XRPL Launchpad services..." -ForegroundColor Yellow
    docker-compose restart
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Services restarted successfully!" -ForegroundColor Green
    } else {
        Write-Host "Failed to restart services!" -ForegroundColor Red
    }
}

function Clean-All {
    Write-Host "Cleaning up Docker resources..." -ForegroundColor Yellow
    Write-Host "This will remove all containers, networks, and images!" -ForegroundColor Red
    
    $confirmation = Read-Host "Are you sure? (y/N)"
    if ($confirmation -eq "y" -or $confirmation -eq "Y") {
        docker-compose down --rmi all --volumes --remove-orphans
        if ($LASTEXITCODE -eq 0) {
            Write-Host "Cleanup completed successfully!" -ForegroundColor Green
        } else {
            Write-Host "Cleanup failed!" -ForegroundColor Red
        }
    } else {
        Write-Host "Cleanup cancelled." -ForegroundColor Yellow
    }
}

function Reset-Database {
    Write-Host "Resetting database..." -ForegroundColor Yellow
    Write-Host "This will delete all database data!" -ForegroundColor Red
    
    $confirmation = Read-Host "Are you sure? (y/N)"
    if ($confirmation -eq "y" -or $confirmation -eq "Y") {
        Write-Host "Stopping services..." -ForegroundColor Yellow
        docker-compose down
        
        Write-Host "Removing database volume..." -ForegroundColor Yellow
        docker volume rm xrpl_db_data -ErrorAction SilentlyContinue
        
        Write-Host "Starting services..." -ForegroundColor Green
        docker-compose up -d
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "Database reset completed successfully!" -ForegroundColor Green
        } else {
            Write-Host "Database reset failed!" -ForegroundColor Red
        }
    } else {
        Write-Host "Database reset cancelled." -ForegroundColor Yellow
    }
}

# Main execution
switch ($Action) {
    "up" { Start-Services }
    "down" { Stop-Services }
    "build" { Build-And-Start-Services }
    "logs" { Show-Logs }
    "restart" { Restart-Services }
    "clean" { Clean-All }
    "reset-db" { Reset-Database }
    "help" { Show-Help }
    default { Show-Help }
}
