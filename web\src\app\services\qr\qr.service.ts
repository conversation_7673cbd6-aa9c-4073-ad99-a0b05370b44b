import { Injectable } from '@angular/core';
import * as QRCode from 'qrcode';

export interface QRCodeOptions {
  width?: number;
  margin?: number;
  color?: {
    dark?: string;
    light?: string;
  };
  errorCorrectionLevel?: 'L' | 'M' | 'Q' | 'H';
}

@Injectable({
  providedIn: 'root',
})
export class QRService {
  /**
   * Generate QR code as data URL (base64 image)
   */
  async generateDataURL(
    text: string,
    options?: QRCodeOptions
  ): Promise<string> {
    const defaultOptions: QRCode.QRCodeToDataURLOptions = {
      width: options?.width || 256,
      margin: options?.margin || 2,
      color: {
        dark: options?.color?.dark || '#000000',
        light: options?.color?.light || '#FFFFFF',
      },
      errorCorrectionLevel: options?.errorCorrectionLevel || 'M',
    };

    try {
      return await QRCode.toDataURL(text, defaultOptions);
    } catch (error) {
      console.error('Error generating QR code:', error);
      throw error;
    }
  }

  /**
   * Generate QR code as SVG string
   */
  async generateSVG(text: string, options?: QRCodeOptions): Promise<string> {
    const defaultOptions: QRCode.QRCodeToStringOptions = {
      width: options?.width || 256,
      margin: options?.margin || 2,
      color: {
        dark: options?.color?.dark || '#000000',
        light: options?.color?.light || '#FFFFFF',
      },
      errorCorrectionLevel: options?.errorCorrectionLevel || 'M',
    };

    try {
      return await QRCode.toString(text, { type: 'svg', ...defaultOptions });
    } catch (error) {
      console.error('Error generating QR code SVG:', error);
      throw error;
    }
  }

  /**
   * Generate QR code as canvas element
   */
  async generateCanvas(
    text: string,
    canvas: HTMLCanvasElement,
    options?: QRCodeOptions
  ): Promise<void> {
    const defaultOptions: QRCode.QRCodeRenderersOptions = {
      width: options?.width || 256,
      margin: options?.margin || 2,
      color: {
        dark: options?.color?.dark || '#000000',
        light: options?.color?.light || '#FFFFFF',
      },
      errorCorrectionLevel: options?.errorCorrectionLevel || 'M',
    };

    try {
      await QRCode.toCanvas(canvas, text, defaultOptions);
    } catch (error) {
      console.error('Error generating QR code canvas:', error);
      throw error;
    }
  }

  /**
   * Generate XRPL trustline QR code data
   */
  generateTrustlineQRData(
    issuerAddress: string,
    currency: string,
    limit?: string
  ): string {
    // Create a custom URL scheme for XRPL trustline setup
    // This could be a deep link to Xaman or other XRPL wallets
    const trustlineData = {
      type: 'trustline',
      issuer: issuerAddress,
      currency: currency,
      limit: limit || '**********', // Default high limit
    };

    // Return as JSON string for QR code
    return JSON.stringify(trustlineData);
  }

  /**
   * Generate XRPL payment QR code data
   */
  generatePaymentQRData(
    destination: string,
    amount: string,
    currency: string = 'XRP',
    destinationTag?: string
  ): string {
    const paymentData = {
      type: 'payment',
      destination: destination,
      amount: amount,
      currency: currency,
      destinationTag: destinationTag,
    };

    return JSON.stringify(paymentData);
  }

  /**
   * Generate Xaman wallet deep link for trustline
   */
  generateXamanTrustlineLink(
    issuerAddress: string,
    currency: string,
    limit?: string
  ): string {
    const trustlineData = {
      TransactionType: 'TrustSet',
      Account: '{{account}}', // Placeholder for user's account
      LimitAmount: {
        currency: currency,
        issuer: issuerAddress,
        value: limit || '**********',
      },
    };

    // Xaman URL scheme for trustline
    return `xaman://xapp?xapp=trustline&data=${encodeURIComponent(JSON.stringify(trustlineData))}`;
  }

  /**
   * Generate Xaman wallet deep link for payment
   */
  generateXamanPaymentLink(
    destination: string,
    amount: string,
    currency: string = 'XRP',
    destinationTag?: string
  ): string {
    const paymentData = {
      TransactionType: 'Payment',
      Account: '{{account}}', // Placeholder for user's account
      Destination: destination,
      Amount:
        currency === 'XRP'
          ? amount
          : {
              currency: currency,
              value: amount,
              issuer: '{{issuer}}', // Placeholder for issuer
            },
      DestinationTag: destinationTag ? parseInt(destinationTag) : undefined,
    };

    // Xaman URL scheme for payment
    return `xaman://xapp?xapp=payment&data=${encodeURIComponent(JSON.stringify(paymentData))}`;
  }
}
