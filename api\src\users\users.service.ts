import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from './entities/user.entity';

@Injectable()
export class UsersService {
  constructor(
    @InjectRepository(User)
    private usersRepository: Repository<User>,
  ) {}

  async findAll(): Promise<User[]> {
    return this.usersRepository.find();
  }

  async findOne(id: string): Promise<User | null> {
    return this.usersRepository.findOne({ where: { id } });
  }

  async findByUsername(username: string): Promise<User | null> {
    return this.usersRepository.findOne({ where: { username } });
  }

  async findByEmail(email: string): Promise<User | null> {
    return this.usersRepository.findOne({ where: { email } });
  }

  async findByXrplAddress(xrplAddress: string): Promise<User | null> {
    return this.usersRepository.findOne({ where: { xrplAddress } });
  }

  async create(createUserDto: Partial<User>): Promise<User> {
    const user = this.usersRepository.create(createUserDto);
    return this.usersRepository.save(user);
  }

  async update(id: string, updateUserDto: Partial<User>): Promise<User | null> {
    await this.usersRepository.update(id, updateUserDto);
    return this.findOne(id);
  }

  async remove(id: string): Promise<void> {
    await this.usersRepository.delete(id);
  }

  async linkXrplAddress(
    userId: string,
    xrplAddress: string,
  ): Promise<User | null> {
    // Check if the address is already linked to another user
    const existingUser = await this.usersRepository.findOne({
      where: { xrplAddress },
    });

    if (existingUser && existingUser.id !== userId) {
      throw new Error('XRPL address is already linked to another user');
    }

    // Update the user's XRPL address
    await this.usersRepository.update(userId, { xrplAddress });
    return this.findOne(userId);
  }

  async unlinkXrplAddress(userId: string): Promise<User | null> {
    await this.usersRepository.update(userId, { xrplAddress: '' });
    return this.findOne(userId);
  }
}
