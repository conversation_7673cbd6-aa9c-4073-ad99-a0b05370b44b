// Trustline component styles

.trustline-card {
  .trustline-instructions {
    .instruction-text {
      font-size: 16px;
      color: var(--ion-color-dark);
      margin: 0 0 20px 0;
      line-height: 1.5;
    }

    .issuer-info {
      margin-bottom: 20px;

      h4 {
        font-size: 16px;
        font-weight: 600;
        color: var(--ion-color-dark);
        margin: 0 0 8px 0;
      }

      .address-container {
        display: flex;
        align-items: center;
        padding: 12px;
        background: var(--ion-color-light-shade);
        border-radius: 8px;
        border: 1px solid var(--ion-color-medium);

        .issuer-address {
          flex: 1;
          font-family: monospace;
          font-size: 14px;
          color: var(--ion-color-dark);
          word-break: break-all;
        }

        .copy-button {
          margin-left: 8px;
          --color: var(--ion-color-primary);
        }
      }
    }

    .qr-section {
      margin-bottom: 20px;

      h4 {
        font-size: 16px;
        font-weight: 600;
        color: var(--ion-color-dark);
        margin: 0 0 12px 0;
      }

      .qr-container {
        display: flex;
        justify-content: center;
        padding: 20px;
        background: var(--ion-color-light-shade);
        border-radius: 8px;
        border: 1px solid var(--ion-color-medium);

        .qr-placeholder {
          text-align: center;
          color: var(--ion-color-medium);

          ion-icon {
            font-size: 48px;
            margin-bottom: 8px;
          }

          p {
            margin: 0;
            font-size: 14px;
          }
        }

        .qr-code-display {
          text-align: center;

          .qr-image {
            width: 200px;
            height: 200px;
            border-radius: 8px;
            border: 2px solid var(--ion-color-light);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            background: white;
            padding: 8px;
          }

          .qr-description {
            margin: 12px 0 0 0;
            font-size: 14px;
            color: var(--ion-color-medium);
            font-weight: 500;
          }
        }
      }
    }

    .wallet-instructions {
      h4 {
        font-size: 16px;
        font-weight: 600;
        color: var(--ion-color-dark);
        margin: 0 0 12px 0;
      }

      ol {
        margin: 0;
        padding-left: 20px;
        color: var(--ion-color-dark);

        li {
          margin-bottom: 8px;
          line-height: 1.4;

          strong {
            color: var(--ion-color-primary);
          }
        }
      }
    }
  }
}
