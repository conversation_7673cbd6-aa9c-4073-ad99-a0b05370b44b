# Docker Setup for XRPL Launchpad

This document describes the Docker configuration for the XRPL Launchpad application, which includes an Ionic Angular web frontend, NestJS API backend, and PostgreSQL database.

## Services Overview

### Database (PostgreSQL)

- **Container**: `xrpl_db`
- **Port**: `5432` (configurable via `DATABASE_PORT`)
- **Image**: `postgres:16-alpine`
- **Health Check**: Built-in PostgreSQL health check

### API (NestJS)

- **Container**: `xrpl_api`
- **Port**: `3000`
- **Health Check**: HTTP endpoint at `/api/health`
- **Dependencies**: Database service

### Web (Ionic Angular)

- **Container**: `xrpl_web` (production)
- **Container**: `xrpl_web_dev` (development)
- **Ports**:
  - Production: `80` (main), `8100` (alternative)
  - Development: `8100` (with live reload)
- **Dependencies**: API service

## Quick Start

### Production Environment

```bash
# Start all services (production build)
docker-compose up -d

# Access the application
# API: http://localhost:3000
# Web: http://localhost:80 or http://localhost:8100
```

### Development Environment

```bash
# Start development environment with live reload
docker-compose --profile dev up -d

# Access the application
# API: http://localhost:3000
# Web Dev: http://localhost:8100 (with live reload)
```

## Using the Helper Scripts

### Windows (PowerShell)

```powershell
# Production
.\scripts\docker-dev.ps1 prod

# Development
.\scripts\docker-dev.ps1 dev

# API only
.\scripts\docker-dev.ps1 api

# Web only
.\scripts\docker-dev.ps1 web

# Database only
.\scripts\docker-dev.ps1 db

# Stop all services
.\scripts\docker-dev.ps1 stop

# Clean everything
.\scripts\docker-dev.ps1 clean

# View logs
.\scripts\docker-dev.ps1 logs

# Check status
.\scripts\docker-dev.ps1 status
```

### Linux/macOS (Bash)

```bash
# Production
./scripts/docker-dev.sh prod

# Development
./scripts/docker-dev.sh dev

# API only
./scripts/docker-dev.sh api

# Web only
./scripts/docker-dev.sh web

# Database only
./scripts/docker-dev.sh db

# Stop all services
./scripts/docker-dev.sh stop

# Clean everything
./scripts/docker-dev.sh clean

# View logs
./scripts/docker-dev.sh logs

# Check status
./scripts/docker-dev.sh status
```

## Environment Variables

Create a `.env` file in the root directory with the following variables:

```env
# Database Configuration
DATABASE_NAME=xrpl_launchpad
DATABASE_USERNAME=postgres
DATABASE_PASSWORD=your_password_here
DATABASE_PORT=5432

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_REFRESH_SECRET=your-super-secret-refresh-key-change-this-in-production
JWT_EXPIRES_IN=15m
JWT_REFRESH_EXPIRES_IN=7d

# XRPL Configuration
XRPL_WS_URL=wss://s.altnet.rippletest.net:51233
XRPL_ISSUER_SEED=your_issuer_seed_here

# API Configuration
THROTTLE_TTL=60
THROTTLE_LIMIT=100

# Web Configuration
API_BASE_URL=http://api:3000/api
NG_BUILD_OUTPUT=www
```

## Dockerfiles

### Web Production Dockerfile (`web/Dockerfile`)

- Multi-stage build with Node.js 20 Alpine
- Installs Ionic CLI for proper Ionic builds
- Uses `ionic build --prod` for production builds
- Serves via Nginx with optimized configuration
- Includes gzip compression and security headers

### Web Development Dockerfile (`web/Dockerfile.dev`)

- Single-stage build for development
- Installs Ionic CLI
- Runs `ionic serve` with live reload
- Exposes port 8100 for development server
- Mounts source code for hot reloading

### API Dockerfile (`api/Dockerfile`)

- Multi-stage build with Node.js 20 Alpine
- Optimized for production with minimal dependencies
- Includes health check endpoint
- Uses dumb-init for proper signal handling

## Nginx Configuration

The web service uses a custom Nginx configuration (`web/nginx.conf`) that:

- Serves the Ionic Angular SPA
- Proxies `/api/` requests to the API service
- Includes security headers
- Provides gzip compression
- Handles SPA routing with fallback to `index.html`
- Includes health check endpoint at `/healthz`

## Health Checks

All services include health checks:

- **Database**: PostgreSQL `pg_isready` command
- **API**: HTTP GET to `/api/health`
- **Web**: HTTP GET to `/healthz`

## Development Workflow

1. **Start development environment**:

   ```bash
   docker-compose --profile dev up -d
   ```

2. **Make changes to your code** - the web service will automatically reload

3. **View logs**:

   ```bash
   docker-compose logs -f web-dev
   ```

4. **Stop development environment**:
   ```bash
   docker-compose --profile dev down
   ```

## Production Deployment

1. **Build and start production environment**:

   ```bash
   docker-compose up -d
   ```

2. **Verify all services are healthy**:

   ```bash
   docker-compose ps
   ```

3. **Check logs if needed**:
   ```bash
   docker-compose logs -f
   ```

## Troubleshooting

### Common Issues

1. **Port conflicts**: Ensure ports 3000, 80, 8100, and 5432 are not in use
2. **Database connection**: Wait for database health check to pass before API starts
3. **Build failures**: Check Docker build logs for dependency issues
4. **Permission issues**: On Linux/macOS, ensure scripts are executable

### Useful Commands

```bash
# View all container logs
docker-compose logs -f

# View specific service logs
docker-compose logs -f api
docker-compose logs -f web
docker-compose logs -f db

# Check service status
docker-compose ps

# Restart a specific service
docker-compose restart api

# Rebuild and restart a service
docker-compose up -d --build web

# Clean up everything
docker-compose down -v --remove-orphans
docker system prune -f
```

## File Structure

```
├── docker-compose.yml          # Main Docker Compose configuration
├── web/
│   ├── Dockerfile             # Production web build
│   ├── Dockerfile.dev         # Development web build
│   └── nginx.conf             # Nginx configuration
├── api/
│   └── Dockerfile             # API build
└── scripts/
    ├── docker-dev.ps1         # Windows PowerShell helper script
    └── docker-dev.sh          # Linux/macOS bash helper script
```
