import {
  Component,
  ChangeDetectionStrategy,
  inject,
  OnInit,
  On<PERSON><PERSON>roy,
  signal,
  HostListener,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule, ToastController, ModalController } from '@ionic/angular';
import { Router } from '@angular/router';
import { Observable, of, Subject } from 'rxjs';
import { map, catchError, startWith, takeUntil } from 'rxjs/operators';
import { ApiService } from '../../services/api/api.service';
import { WebSocketService } from '../../services/websocket/websocket.service';
import { BalanceResponse, Transaction, Sale, User } from '../../types';
import { TransactionDetailsModalComponent } from '../../components/modals/transaction-details/transaction-details.component';
import { ThemeToggleComponent } from '../../components/theme-toggle/theme-toggle.component';
import { BalanceCardComponent } from '../../components/balance-card/balance-card.component';
import { TransactionsListComponent } from '../../components/transactions-list/transactions-list.component';
import { SalesListComponent } from '../../components/sales-list/sales-list.component';
import { ConnectionStatusComponent } from '../../components/connection-status/connection-status.component';

/**
 * Main dashboard component that displays user's XRPL account information and active token sales.
 *
 * This component provides a comprehensive overview of:
 * - User's XRPL balance and transaction history
 * - Active token sales available for participation
 * - Real-time updates via WebSocket connections
 * - Quick access to XRPL account linking if not connected
 *
 * Features:
 * - Real-time balance and transaction updates
 * - Interactive transaction details modal
 * - Responsive design with loading states
 * - Automatic data refresh and error handling
 * - Theme toggle integration
 *
 * @example
 * ```html
 * <app-dashboard></app-dashboard>
 * ```
 *
 * @since 1.0.0
 * <AUTHOR>
 */
@Component({
  selector: 'app-dashboard',
  templateUrl: './dashboard.page.html',
  styleUrls: ['./dashboard.page.scss'],
  standalone: true,
  imports: [
    CommonModule,
    IonicModule,
    ThemeToggleComponent,
    BalanceCardComponent,
    TransactionsListComponent,
    SalesListComponent,
    ConnectionStatusComponent,
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DashboardPage implements OnInit, OnDestroy {
  private apiService = inject(ApiService);
  private webSocketService = inject(WebSocketService);
  private router = inject(Router);
  private toastController = inject(ToastController);
  private modalController = inject(ModalController);
  private destroy$ = new Subject<void>();

  // Observable properties for use with async pipe
  balance$!: Observable<BalanceResponse | null>;
  transactions$!: Observable<Transaction[]>;
  activeSales$!: Observable<Sale[]>;
  currentUser$!: Observable<User | null>;

  // WebSocket signals for real-time data
  realtimeBalance = this.webSocketService.balance;
  realtimeTransactions = this.webSocketService.transactions;
  isConnected = this.webSocketService.isConnected$;

  // Loading state
  isLoading = signal<boolean>(false);

  ngOnInit() {
    this.loadData();
    this.checkXrplAddress();

    // Start WebSocket service if ready
    if (this.webSocketService.isReady()) {
      console.log('Starting WebSocket service from dashboard...');
      this.webSocketService.start();

      // Request real-time updates with delay
      setTimeout(() => {
        if (this.webSocketService.getIsConnected()) {
          this.webSocketService.requestBalanceUpdate();
          this.webSocketService.requestTransactionsUpdate();
        }
      }, 1000);
    } else {
      console.log('WebSocket service not ready - no authentication token');
    }
  }

  private loadData() {
    this.isLoading.set(true);

    // Initialize observables - these will be consumed by async pipe in template
    this.balance$ = this.apiService.getBalance().pipe(
      startWith(null),
      catchError(() => of(null))
    );

    this.transactions$ = this.apiService.getTransactions().pipe(
      map(transactions => transactions.slice(0, 5)), // Show only recent 5 transactions
      catchError(() => of([])),
      startWith([])
    );

    this.activeSales$ = this.apiService.getActiveSales().pipe(
      catchError(() => of([])),
      startWith([])
    );

    this.currentUser$ = this.apiService.currentUser$;

    // Set loading to false after a short delay to show loading state
    setTimeout(() => this.isLoading.set(false), 500);
  }

  onRefreshData(): void {
    this.loadData();

    // Request real-time updates
    this.webSocketService.requestBalanceUpdate();
    this.webSocketService.requestTransactionsUpdate();
  }

  onViewSale(saleId: string): void {
    this.router.navigate(['/launchpad', saleId]);
  }

  onViewAllTransactions(): void {
    // TODO: Navigate to transactions page when implemented
    console.log('View all transactions');
  }

  onViewAllSales(): void {
    this.router.navigate(['/launchpad']);
  }

  onDebugWebSocket(): void {
    console.log('🐛 Debug WebSocket button clicked');
    this.webSocketService.debugStatus();

    // Try to start connection if not connected
    if (!this.webSocketService.getIsConnected()) {
      console.log('Attempting to start WebSocket connection...');
      this.webSocketService.start();
    }
  }

  private checkXrplAddress(): void {
    this.apiService.currentUser$
      .pipe(takeUntil(this.destroy$))
      .subscribe(user => {
        if (user && !user.xrplAddress) {
          // Redirect to XRPL Connect page if no address is linked
          this.router.navigate(['/xrpl-connect']);
        }
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  async onViewTransactionDetails(transaction: Transaction): Promise<void> {
    // TODO: Navigate to transaction details page when implemented
    console.log('View transaction details:', transaction);

    await this.showTransactionDetailsModal(transaction);
  }

  onCopyTransactionHash(hash: string, event: Event): void {
    event.stopPropagation();

    // Copy to clipboard
    if (navigator.clipboard) {
      navigator.clipboard
        .writeText(hash)
        .then(() => {
          this.showToast('Transaction hash copied to clipboard', 'success');
        })
        .catch(() => {
          this.showToast('Failed to copy transaction hash', 'danger');
        });
    } else {
      // Fallback for older browsers
      const textArea = document.createElement('textarea');
      textArea.value = hash;
      document.body.appendChild(textArea);
      textArea.select();
      try {
        document.execCommand('copy');
        this.showToast('Transaction hash copied to clipboard', 'success');
      } catch (err) {
        this.showToast('Failed to copy transaction hash', 'danger');
      }
      document.body.removeChild(textArea);
    }
  }

  private async showTransactionDetailsModal(
    transaction: Transaction
  ): Promise<void> {
    const modal = await this.modalController.create({
      component: TransactionDetailsModalComponent,
      componentProps: {
        transaction,
      },
      presentingElement:
        document.querySelector('ion-router-outlet') || document.body,
    });

    await modal.present();
  }

  private async showToast(
    message: string,
    color: string = 'primary'
  ): Promise<void> {
    const toast = await this.toastController.create({
      message,
      duration: 2000,
      position: 'bottom',
      color,
    });
    await toast.present();
  }

  // Keyboard navigation support
  @HostListener('keydown', ['$event'])
  onKeyDown(event: KeyboardEvent): void {
    // Handle keyboard shortcuts
    if (event.ctrlKey || event.metaKey) {
      switch (event.key) {
        case 'r':
          event.preventDefault();
          this.onRefreshData();
          break;
        case 'd':
          event.preventDefault();
          this.onDebugWebSocket();
          break;
      }
    }

    // Handle Enter key on focused elements
    if (event.key === 'Enter' && event.target instanceof HTMLElement) {
      const target = event.target as HTMLElement;
      if (
        target.hasAttribute('tabindex') &&
        target.getAttribute('tabindex') !== '-1'
      ) {
        target.click();
      }
    }
  }
}
