import { Is<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ption<PERSON>, Min } from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';

export class CreateContributionDto {
  @ApiProperty({
    description: 'ID of the token sale to contribute to',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @IsString()
  saleId!: string;

  @ApiProperty({
    description: 'Contribution amount in XRP (minimum 0.000001)',
    example: 100,
    minimum: 0.000001,
  })
  @IsNumber()
  @Min(0.000001) // Minimum amount: 1 drop (0.000001 XRP)
  @Type(() => Number)
  amount!: number;

  @ApiProperty({
    description: 'XRPL transaction hash of the contribution payment',
    example: '1234567890ABCDEF1234567890ABCDEF1234567890ABCDEF1234567890ABCDEF',
  })
  @IsString()
  txHash!: string;

  @ApiProperty({
    description: 'Optional status of the contribution',
    example: 'pending',
    required: false,
  })
  @IsOptional()
  @IsString()
  status?: string;
}
