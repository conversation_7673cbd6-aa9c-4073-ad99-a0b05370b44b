.container {
  padding: 16px;
  max-width: 600px;
  margin: 0 auto;
}

.already-linked-section {
  .connected-address {
    margin: 16px 0;

    strong {
      display: block;
      margin-bottom: 8px;
      color: var(--ion-color-primary);
    }

    ion-chip {
      margin-top: 8px;
    }
  }
}

.link-form-section {
  .error-message {
    margin-top: 8px;
    margin-left: 16px;
  }

  .help-text {
    margin-top: 8px;
    margin-left: 16px;

    ion-icon {
      margin-right: 4px;
      vertical-align: middle;
    }
  }

  .required {
    color: var(--ion-color-danger);
  }
}

.info-card {
  margin-top: 16px;

  ion-item {
    --padding-start: 0;
    --inner-padding-end: 0;

    ion-icon {
      margin-right: 12px;
    }

    ion-label {
      h3 {
        font-weight: 600;
        margin-bottom: 4px;
      }

      p {
        color: var(--ion-color-medium);
        font-size: 0.9em;
        margin: 0;
      }
    }
  }
}

// Form validation styles
ion-input.ion-invalid {
  --border-color: var(--ion-color-danger);
}

ion-input.ion-valid {
  --border-color: var(--ion-color-success);
}

// Loading state
ion-button[disabled] {
  opacity: 0.6;
}

// Responsive design
@media (max-width: 768px) {
  .container {
    padding: 12px;
  }

  .info-card ion-item {
    --padding-start: 8px;
  }
}
