import { Injectable, inject } from '@angular/core';
import {
  HttpClient,
  HttpHeaders,
  HttpErrorResponse,
} from '@angular/common/http';
import { Observable, throwError, BehaviorSubject, of } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import { ToastController } from '@ionic/angular';
import { Router } from '@angular/router';
import { environment } from '../../../environments/environment';
import {
  User,
  LoginRequest,
  RegisterRequest,
  AuthResponse,
  RefreshResponse,
  SuccessResponse,
  BalanceResponse,
  Transaction,
  PreparedTransaction,
  Sale,
  CreateSaleRequest,
  UpdateSaleRequest,
  CreateContributionRequest,
  UserAllocation,
  ClaimResponse,
  SendTransactionRequest,
} from '../../types';

/**
 * Central API service for handling all HTTP communications with the XRPL Launchpad backend.
 *
 * This service provides methods for:
 * - User authentication (login, register, refresh tokens)
 * - XRPL account management (linking addresses, balance, transactions)
 * - Token sale operations (creating, viewing, contributing to sales)
 * - Admin operations (managing sales, viewing statistics)
 *
 * The service automatically handles:
 * - JWT token management and refresh
 * - Error handling with user-friendly toast notifications
 * - User state management across the application
 * - Automatic logout on authentication failures
 *
 * @example
 * ```typescript
 * // Inject the service
 * constructor(private apiService: ApiService) {}
 *
 * // Login a user
 * this.apiService.login({ usernameOrEmail: '<EMAIL>', password: 'password' })
 *   .subscribe(response => {
 *     console.log('Login successful:', response);
 *   });
 *
 * // Get current user
 * this.apiService.getCurrentUser();
 * ```
 *
 * @since 1.0.0
 * <AUTHOR>
 */
@Injectable({
  providedIn: 'root',
})
export class ApiService {
  private http = inject(HttpClient);
  private toastController = inject(ToastController);
  private router = inject(Router);

  private readonly apiUrl = environment.apiUrl;
  private currentUserSubject = new BehaviorSubject<User | null>(null);
  public currentUser$ = this.currentUserSubject.asObservable();

  constructor() {
    // Load user from localStorage on service initialization
    const savedUser = localStorage.getItem('user');
    if (savedUser) {
      try {
        this.currentUserSubject.next(JSON.parse(savedUser));
      } catch (error) {
        console.error('Failed to parse saved user:', error);
        localStorage.removeItem('user');
      }
    }
  }

  // Private method to get auth headers
  private getAuthHeaders(): HttpHeaders {
    const token = localStorage.getItem('access_token');
    return new HttpHeaders({
      Authorization: token ? `Bearer ${token}` : '',
      'Content-Type': 'application/json',
    });
  }

  // Private method to handle errors with toast notifications
  private handleError = (error: HttpErrorResponse): Observable<never> => {
    let errorMessage = 'An unexpected error occurred';

    if (error.error instanceof ErrorEvent) {
      // Client-side error
      errorMessage = `Error: ${error.error.message}`;
    } else {
      // Server-side error
      if (error.status === 400) {
        errorMessage = error.error?.message || 'Bad request';
      } else if (error.status === 401) {
        errorMessage = 'Unauthorized. Please login again.';
        this.clearAuthData();
      } else if (error.status === 403) {
        errorMessage = 'Access denied';
      } else if (error.status === 404) {
        errorMessage = 'Resource not found';
      } else if (error.status === 409) {
        errorMessage = error.error?.message || 'Conflict occurred';
      } else if (error.status >= 500) {
        errorMessage = 'Server error. Please try again later.';
      } else {
        errorMessage = error.error?.message || `Error ${error.status}`;
      }
    }

    // Show toast notification
    this.showErrorToast(errorMessage);

    return throwError(() => error);
  };

  // Handle errors silently (no toast notifications)
  private handleErrorSilently = (
    error: HttpErrorResponse
  ): Observable<never> => {
    // For 401 errors, clear local storage but don't show toast
    if (error.status === 401) {
      this.clearAuthData();
    }

    return throwError(() => error);
  };

  // Show error toast
  private async showErrorToast(message: string) {
    const toast = await this.toastController.create({
      message,
      duration: 4000,
      position: 'top',
      color: 'danger',
      buttons: [
        {
          text: 'Dismiss',
          role: 'cancel',
        },
      ],
    });
    await toast.present();
  }

  // Show success toast
  private async showSuccessToast(message: string) {
    const toast = await this.toastController.create({
      message,
      duration: 3000,
      position: 'top',
      color: 'success',
    });
    await toast.present();
  }

  // AUTH ENDPOINTS

  login(credentials: LoginRequest): Observable<AuthResponse> {
    return this.http
      .post<AuthResponse>(`${this.apiUrl}/auth/login`, credentials)
      .pipe(
        tap(response => {
          localStorage.setItem('access_token', response.accessToken);
          localStorage.setItem('refresh_token', response.refreshToken);
          localStorage.setItem('user', JSON.stringify(response.user));
          this.currentUserSubject.next(response.user);
          this.showSuccessToast('Login successful');
        }),
        catchError(this.handleError)
      );
  }

  register(userData: RegisterRequest): Observable<AuthResponse> {
    return this.http
      .post<AuthResponse>(`${this.apiUrl}/auth/register`, userData)
      .pipe(
        tap(response => {
          localStorage.setItem('access_token', response.accessToken);
          localStorage.setItem('refresh_token', response.refreshToken);
          localStorage.setItem('user', JSON.stringify(response.user));
          this.currentUserSubject.next(response.user);
          this.showSuccessToast('Registration successful');
        }),
        catchError(this.handleError)
      );
  }

  refreshToken(): Observable<RefreshResponse> {
    const refreshToken = localStorage.getItem('refresh_token');
    if (!refreshToken) {
      return throwError(() => new Error('No refresh token available'));
    }

    return this.http
      .post<RefreshResponse>(`${this.apiUrl}/auth/refresh`, { refreshToken })
      .pipe(
        tap(response => {
          localStorage.setItem('access_token', response.accessToken);
        }),
        catchError(this.handleError)
      );
  }

  /**
   * Get current user profile
   */
  getMe(): Observable<User> {
    return this.http
      .get<User>(`${this.apiUrl}/auth/me`, { headers: this.getAuthHeaders() })
      .pipe(
        tap(user => {
          localStorage.setItem('user', JSON.stringify(user));
          this.currentUserSubject.next(user);
        }),
        catchError(this.handleErrorSilently)
      );
  }

  /**
   * Get current user profile only if not already loaded
   */
  getMeIfNeeded(): Observable<User> {
    const currentUser = this.getCurrentUser();
    if (currentUser) {
      return of(currentUser);
    }
    return this.getMe();
  }

  logout(): Observable<any> {
    const headers = this.getAuthHeaders();
    return this.http.post(`${this.apiUrl}/auth/logout`, {}, { headers }).pipe(
      tap(() => {
        this.clearAuthData();
        this.showSuccessToast('Logged out successfully');
      }),
      catchError(error => {
        // Even if logout fails on server, clear local storage
        this.clearAuthData();
        return throwError(() => error);
      })
    );
  }

  // XRPL ENDPOINTS

  getBalance(): Observable<BalanceResponse> {
    return this.http
      .get<BalanceResponse>(`${this.apiUrl}/xrpl/balance`, {
        headers: this.getAuthHeaders(),
      })
      .pipe(catchError(this.handleError));
  }

  getTransactions(): Observable<Transaction[]> {
    return this.http
      .get<
        Transaction[]
      >(`${this.apiUrl}/xrpl/transactions`, { headers: this.getAuthHeaders() })
      .pipe(catchError(this.handleError));
  }

  prepareSendTransaction(
    request: SendTransactionRequest
  ): Observable<PreparedTransaction> {
    return this.http
      .post<PreparedTransaction>(`${this.apiUrl}/xrpl/send`, request, {
        headers: this.getAuthHeaders(),
      })
      .pipe(catchError(this.handleError));
  }

  // LAUNCHPAD ENDPOINTS
  getSales(status?: 'active' | 'ended' | 'canceled'): Observable<Sale[]> {
    const params = status ? { status } : undefined;
    return this.http
      .get<Sale[]>(`${this.apiUrl}/launchpad/sales`, { params })
      .pipe(catchError(this.handleError));
  }

  getActiveSales(): Observable<Sale[]> {
    return this.http
      .get<Sale[]>(`${this.apiUrl}/launchpad/sales/active`)
      .pipe(catchError(this.handleError));
  }

  getSale(id: string): Observable<Sale> {
    return this.http
      .get<Sale>(`${this.apiUrl}/launchpad/sales/${id}`)
      .pipe(catchError(this.handleError));
  }

  createSale(saleData: CreateSaleRequest): Observable<Sale> {
    return this.http
      .post<Sale>(`${this.apiUrl}/launchpad/sales`, saleData, {
        headers: this.getAuthHeaders(),
      })
      .pipe(
        tap(() => this.showSuccessToast('Sale created successfully')),
        catchError(this.handleError)
      );
  }

  /**
   * Update token sale (admin only)
   */
  updateSale(id: string, saleData: UpdateSaleRequest): Observable<Sale> {
    return this.http
      .put<Sale>(`${this.apiUrl}/launchpad/sales/${id}`, saleData, {
        headers: this.getAuthHeaders(),
      })
      .pipe(
        tap(() => this.showSuccessToast('Sale updated successfully')),
        catchError(this.handleError)
      );
  }

  /**
   * Delete token sale (admin only)
   */
  deleteSale(id: string): Observable<void> {
    return this.http
      .delete<void>(`${this.apiUrl}/launchpad/sales/${id}`, {
        headers: this.getAuthHeaders(),
      })
      .pipe(
        tap(() => this.showSuccessToast('Sale deleted successfully')),
        catchError(this.handleError)
      );
  }

  createContribution(
    contributionData: CreateContributionRequest
  ): Observable<any> {
    return this.http
      .post(`${this.apiUrl}/launchpad/contributions`, contributionData, {
        headers: this.getAuthHeaders(),
      })
      .pipe(
        tap(() => this.showSuccessToast('Contribution recorded successfully')),
        catchError(this.handleError)
      );
  }

  getUserAllocation(saleId: string): Observable<UserAllocation> {
    return this.http
      .get<UserAllocation>(
        `${this.apiUrl}/launchpad/sales/${saleId}/allocation`,
        { headers: this.getAuthHeaders() }
      )
      .pipe(catchError(this.handleErrorSilently));
  }

  claimTokens(saleId: string): Observable<ClaimResponse> {
    return this.http
      .post<ClaimResponse>(
        `${this.apiUrl}/launchpad/sales/${saleId}/claim`,
        {},
        { headers: this.getAuthHeaders() }
      )
      .pipe(
        tap(response => {
          if (response.success) {
            this.showSuccessToast(response.message);
          }
        }),
        catchError(this.handleError)
      );
  }

  /**
   * Link XRPL address to user account
   */
  linkXrplAddress(xrplAddress: string): Observable<SuccessResponse> {
    return this.http
      .post<SuccessResponse>(
        `${this.apiUrl}/users/xrpl/link`,
        { xrplAddress },
        {
          headers: this.getAuthHeaders(),
        }
      )
      .pipe(
        tap(response => {
          if (response.success) {
            // Update the current user's XRPL address in local state
            const currentUser = this.currentUserSubject.value;
            if (currentUser) {
              const updatedUser = { ...currentUser, xrplAddress };
              localStorage.setItem('user', JSON.stringify(updatedUser));
              this.currentUserSubject.next(updatedUser);
            }
            this.showSuccessToast(response.message);
          }
        }),
        catchError(this.handleError)
      );
  }

  // UTILITY METHODS

  /**
   * Clear all authentication data (tokens and user info) and redirect to login
   */
  clearAuthData(): void {
    localStorage.removeItem('access_token');
    localStorage.removeItem('refresh_token');
    localStorage.removeItem('user');
    this.currentUserSubject.next(null);
    this.router.navigate(['/login']);
  }

  isAuthenticated(): boolean {
    return !!localStorage.getItem('access_token');
  }

  getCurrentUser(): User | null {
    return this.currentUserSubject.value;
  }

  isAdmin(): boolean {
    const user = this.getCurrentUser();
    return user?.role === 'admin';
  }
}
