import {
  IsString,
  IsN<PERSON>ber,
  IsPositive,
  IsNotEmpty,
  Matches,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class SendTransactionDto {
  @ApiProperty({
    description: 'Destination XRPL address for the payment',
    example: 'rPT1Sjq2YGrBMTttX4GZHjKu9yfEVRox89',
    pattern: '^r[1-9A-HJ-NP-Za-km-z]{25,34}$',
  })
  @IsString()
  @IsNotEmpty()
  @Matches(/^r[1-9A-HJ-NP-Za-km-z]{25,34}$/, {
    message:
      'Invalid XRPL address format. Must be a valid XRPL address starting with "r".',
  })
  destination!: string;

  @ApiProperty({
    description: 'Amount to send in XRP (minimum 0.000001)',
    example: 10.5,
    minimum: 0.000001,
  })
  @IsNumber()
  @IsPositive({ message: 'Amount must be a positive number' })
  amount!: number;
}
