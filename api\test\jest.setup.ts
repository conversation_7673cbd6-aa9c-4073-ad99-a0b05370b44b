import { config } from 'dotenv';
import { existsSync } from 'fs';

import 'jest-extended';

// Load environment variables for testing if .env.test exists
if (existsSync('.env.test')) {
  config({ path: '.env.test' });
}

// Set test environment
process.env.NODE_ENV = 'test';

// Set test database configuration
process.env.DATABASE_NAME = process.env.DATABASE_NAME || 'test_db';
process.env.JWT_SECRET =
  process.env.JWT_SECRET || 'test-super-secret-jwt-key-for-testing-32-chars';

// Disable NestJS Logger during tests
import { Logger } from '@nestjs/common';

// Override Logger methods to do nothing
Logger.prototype.log = () => {};
Logger.prototype.error = () => {};
Logger.prototype.warn = () => {};
Logger.prototype.debug = () => {};
Logger.prototype.verbose = () => {};

// Mock console methods to reduce noise in tests
const originalConsole = { ...console };
beforeAll(() => {
  console.log = jest.fn();
  console.warn = jest.fn();
  console.error = jest.fn();
});

afterAll(() => {
  console.log = originalConsole.log.bind(console);
  console.warn = originalConsole.warn.bind(console);
  console.error = originalConsole.error.bind(console);
});

// Global test timeout
jest.setTimeout(30000);
