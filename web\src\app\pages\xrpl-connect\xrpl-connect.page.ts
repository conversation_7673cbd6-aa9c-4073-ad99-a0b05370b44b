import {
  Component,
  ChangeDetectionStrategy,
  inject,
  OnInit,
  OnDestroy,
  signal,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';
import { Router } from '@angular/router';
import {
  FormBuilder,
  FormGroup,
  Validators,
  ReactiveFormsModule,
} from '@angular/forms';
import { Observable, of, Subject } from 'rxjs';
import { catchError, finalize, takeUntil } from 'rxjs/operators';
import { ApiService } from '../../services/api/api.service';
import { User } from '../../types';

@Component({
  selector: 'app-xrpl-connect',
  templateUrl: './xrpl-connect.page.html',
  styleUrls: ['./xrpl-connect.page.scss'],
  standalone: true,
  imports: [CommonModule, IonicModule, ReactiveFormsModule],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class XrplConnectPage implements OnInit, OnDestroy {
  private apiService = inject(ApiService);
  private router = inject(Router);
  private formBuilder = inject(FormBuilder);
  private destroy$ = new Subject<void>();

  // Form and state
  xrplForm!: FormGroup;
  currentUser$!: Observable<User | null>;

  // Loading state
  isSubmitting = signal<boolean>(false);
  isAlreadyLinked = signal<boolean>(false);

  // XRPL address validation regex (matches backend validation)
  private readonly XRPL_ADDRESS_REGEX = /^r[a-zA-Z0-9]{24,34}$/;

  ngOnInit() {
    this.initializeForm();
    this.loadCurrentUser();
  }

  private initializeForm() {
    this.xrplForm = this.formBuilder.group({
      xrplAddress: [
        '',
        [Validators.required, Validators.pattern(this.XRPL_ADDRESS_REGEX)],
      ],
    });
  }

  private loadCurrentUser() {
    this.currentUser$ = this.apiService.currentUser$;

    // Check if user already has a linked address
    this.currentUser$.pipe(takeUntil(this.destroy$)).subscribe(user => {
      if (user?.xrplAddress) {
        this.isAlreadyLinked.set(true);
        this.xrplForm.disable();
      }
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  onSubmit() {
    if (this.xrplForm.valid && !this.isSubmitting()) {
      this.isSubmitting.set(true);

      const { xrplAddress } = this.xrplForm.value;

      this.apiService
        .linkXrplAddress(xrplAddress)
        .pipe(
          finalize(() => this.isSubmitting.set(false)),
          catchError(error => {
            console.error('Failed to link XRPL address:', error);
            return of(null);
          })
        )
        .subscribe(response => {
          if (response) {
            // Success - redirect to dashboard
            this.router.navigate(['/dashboard']);
          }
        });
    }
  }

  onGoToDashboard() {
    this.router.navigate(['/dashboard']);
  }

  // Form validation helpers
  get xrplAddressControl() {
    return this.xrplForm.get('xrplAddress');
  }

  get isFormValid() {
    return this.xrplForm.valid && !this.isSubmitting();
  }

  get addressErrorMessage() {
    const control = this.xrplAddressControl;
    if (control?.errors?.['required']) {
      return 'XRPL address is required';
    }
    if (control?.errors?.['pattern']) {
      return 'Invalid XRPL address format. Must start with "r" followed by 24-34 alphanumeric characters.';
    }
    return '';
  }
}
