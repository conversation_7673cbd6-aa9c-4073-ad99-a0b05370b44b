ion-button {
  --color: var(--ion-color-medium);
  --color-hover: var(--ion-color-primary);
  --color-activated: var(--ion-color-primary);

  transition: color 0.2s ease;
}

ion-button:hover {
  --color: var(--ion-color-primary);
}

ion-button:focus {
  --color: var(--ion-color-primary);
  outline: 2px solid var(--ion-color-primary);
  outline-offset: 2px;
}

ion-icon {
  transition: transform 0.2s ease;
}

ion-button:active ion-icon {
  transform: scale(0.9);
}
