// Sale Item Component Styles
.sale-item-sliding {
  margin-bottom: 12px;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  animation: slideInUp 0.3s ease-out;

  &:last-child {
    margin-bottom: 0;
  }
}

.sale-item {
  --background: var(--ion-color-step-50);
  --padding-start: 20px;
  --padding-end: 20px;
  --padding-top: 20px;
  --padding-bottom: 20px;
  border-radius: 12px;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  }

  &.status-active {
    border-left: 4px solid var(--ion-color-success);
  }

  &.status-ended {
    border-left: 4px solid var(--ion-color-medium);
  }

  &.status-canceled {
    border-left: 4px solid var(--ion-color-danger);
  }

  .sale-content {
    width: 100%;

    .sale-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 16px;
      flex-wrap: wrap;
      gap: 12px;

      .sale-title {
        display: flex;
        align-items: center;
        gap: 12px;
        flex: 1;
        min-width: 200px;

        .symbol {
          font-size: 1.4rem;
          font-weight: 700;
          color: var(--ion-color-primary);
          margin: 0;
        }

        .status-chip {
          font-size: 0.8rem;
          height: 28px;
        }
      }

      .sale-price {
        text-align: right;
        min-width: 120px;

        .price-label {
          display: block;
          font-size: 0.85rem;
          color: var(--ion-color-step-600);
          margin-bottom: 4px;
        }

        .price-value {
          font-size: 1.2rem;
          font-weight: 600;
          color: var(--ion-color-success);
        }
      }
    }

    .sale-details {
      .detail-row {
        display: grid;
        grid-template-columns: 1fr;
        gap: 12px;
        margin-bottom: 12px;

        @media (min-width: 480px) {
          grid-template-columns: 1fr 1fr;
        }

        .detail-item {
          display: flex;
          align-items: center;
          gap: 8px;
          padding: 8px 12px;
          background: var(--ion-color-step-100);
          border-radius: 8px;
          min-height: 40px;

          &.full-width {
            grid-column: 1 / -1;
          }

          .detail-icon {
            color: var(--ion-color-primary);
            font-size: 1.1rem;
            min-width: 20px;
          }

          .detail-label {
            font-weight: 500;
            color: var(--ion-color-step-900);
            min-width: 80px;
          }

          .detail-value {
            color: var(--ion-color-step-700);
            font-size: 0.95rem;

            &.address {
              font-family: 'Courier New', monospace;
              font-size: 0.85rem;
              word-break: break-all;
            }

            &.description {
              font-style: italic;
              line-height: 1.4;
            }
          }
        }
      }

      .links-row {
        display: flex;
        gap: 8px;
        margin-top: 16px;
        flex-wrap: wrap;

        .link-button {
          --color: var(--ion-color-primary);
          font-size: 0.9rem;
          height: 36px;
          --border-radius: 8px;

          ion-icon {
            font-size: 1rem;
          }
        }
      }
    }
  }
}

.sale-options {
  ion-item-option {
    width: 60px;
    font-size: 1.2rem;
  }
}

// Animation
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Responsive Design
@media (max-width: 767px) {
  .sale-item {
    .sale-header {
      flex-direction: column;
      align-items: flex-start !important;

      .sale-price {
        text-align: left !important;
        min-width: auto !important;
      }
    }

    .sale-details {
      .detail-row {
        grid-template-columns: 1fr !important;
      }
    }
  }
}
