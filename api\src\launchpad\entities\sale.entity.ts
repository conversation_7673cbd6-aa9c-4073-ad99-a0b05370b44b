import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Contribution } from './contribution.entity';

export enum SaleStatus {
  ACTIVE = 'active',
  ENDED = 'ended',
  CANCELED = 'canceled',
}

@Entity('sales')
export class Sale {
  @ApiProperty({
    description: 'Unique identifier for the sale',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @ApiProperty({
    description: 'Token symbol for the sale',
    example: 'TOKEN',
    maxLength: 100,
  })
  @Column({ type: 'varchar', length: 100 })
  symbol!: string;

  @ApiProperty({
    description: 'Price per token in XRP',
    example: 0.001,
    minimum: 0,
  })
  @Column({ type: 'decimal', precision: 18, scale: 8 })
  price!: number;

  @ApiProperty({
    description: 'Sale start date and time',
    example: '2025-01-01T00:00:00.000Z',
  })
  @Column({ type: 'timestamp' })
  start!: Date;

  @ApiProperty({
    description: 'Sale end date and time',
    example: '2025-01-31T23:59:59.000Z',
  })
  @Column({ type: 'timestamp' })
  end!: Date;

  @ApiProperty({
    description: 'Minimum amount to raise for sale to be successful',
    example: 1000,
    minimum: 0,
  })
  @Column({ type: 'decimal', precision: 18, scale: 8 })
  softCap!: number;

  @ApiProperty({
    description: 'Maximum amount that can be raised',
    example: 10000,
    minimum: 0,
  })
  @Column({ type: 'decimal', precision: 18, scale: 8 })
  hardCap!: number;

  @ApiProperty({
    description: 'XRPL collection address for the tokens',
    example: 'rXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX',
    maxLength: 255,
  })
  @Column({ type: 'varchar', length: 255 })
  collectionAddress!: string;

  @ApiProperty({
    description: 'Current status of the sale',
    enum: SaleStatus,
    example: SaleStatus.ACTIVE,
  })
  @Column({ type: 'enum', enum: SaleStatus, default: SaleStatus.ACTIVE })
  status!: SaleStatus;

  @ApiProperty({
    description: 'Description of the token sale',
    required: false,
    example: 'A revolutionary token for the future of finance',
  })
  @Column({ type: 'text', nullable: true })
  description?: string;

  @ApiProperty({
    description: 'Website URL for the project',
    required: false,
    example: 'https://example.com',
    maxLength: 255,
  })
  @Column({ type: 'varchar', length: 255, nullable: true })
  website?: string;

  @ApiProperty({
    description: 'Whitepaper URL for the project',
    required: false,
    example: 'https://example.com/whitepaper.pdf',
    maxLength: 255,
  })
  @Column({ type: 'varchar', length: 255, nullable: true })
  whitepaper?: string;

  @ApiProperty({
    description: 'Date when the sale was created',
    example: '2025-01-01T00:00:00.000Z',
  })
  @CreateDateColumn()
  createdAt!: Date;

  @ApiProperty({
    description: 'Date when the sale was last updated',
    example: '2025-01-01T00:00:00.000Z',
  })
  @UpdateDateColumn()
  updatedAt!: Date;

  @ApiProperty({
    description: 'List of contributions to this sale',
    type: () => [Contribution],
    required: false,
  })
  @OneToMany(() => Contribution, (contribution) => contribution.sale)
  contributions!: Contribution[];
}
