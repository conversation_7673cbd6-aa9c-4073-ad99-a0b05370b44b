import { IsString, IsNotEmpty } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class LoginDto {
  @ApiProperty({
    description: 'Username or email for login',
    example: 'johndoe',
  })
  @IsString()
  @IsNotEmpty({ message: 'Username or email is required' })
  usernameOrEmail!: string;

  @ApiProperty({
    description: 'Password for login',
    example: 'SecurePass123!',
  })
  @IsString()
  @IsNotEmpty({ message: 'Password is required' })
  password!: string;
}
