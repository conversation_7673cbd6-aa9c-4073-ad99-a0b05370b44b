import { TypeOrmModuleOptions } from '@nestjs/typeorm';

export const testDatabaseConfig: TypeOrmModuleOptions = {
  type: 'postgres',
  host: process.env.DATABASE_HOST || 'localhost',
  port: parseInt(process.env.DATABASE_PORT || '5432', 10),
  username: process.env.DATABASE_USERNAME || 'test',
  password: process.env.DATABASE_PASSWORD || 'test',
  database: process.env.DATABASE_NAME || 'test_db',
  entities: [__dirname + '/../src/**/*.entity{.ts,.js}'],
  synchronize: true, // Use synchronize for tests
  logging: false, // Disable logging in tests
  dropSchema: true, // Drop and recreate schema for each test run
};
