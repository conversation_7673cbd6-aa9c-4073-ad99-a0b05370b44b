import {
  ComponentFixture,
  TestBed,
  fakeAsync,
  tick,
} from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { NavController } from '@ionic/angular';
import { of, throwError } from 'rxjs';
import { LoginPage } from './login.page';
import { ApiService } from '../../services/api/api.service';
import { LoginRequest } from '../../types';

describe('LoginPage', () => {
  let component: LoginPage;
  let fixture: ComponentFixture<LoginPage>;
  let mockApiService: jasmine.SpyObj<ApiService>;
  let mockRouter: jasmine.SpyObj<Router>;
  let mockNavController: jasmine.SpyObj<NavController>;
  let mockActivatedRoute: jasmine.SpyObj<ActivatedRoute>;

  const mockAuthResponse = {
    accessToken: 'mock-access-token',
    refreshToken: 'mock-refresh-token',
    user: {
      id: '1',
      username: 'testuser',
      email: '<EMAIL>',
      role: 'user',
      xrplAddress: null,
    },
  };

  beforeEach(async () => {
    const apiServiceSpy = jasmine.createSpyObj('ApiService', ['login']);
    const routerSpy = jasmine.createSpyObj(
      'Router',
      ['navigate', 'createUrlTree', 'serializeUrl'],
      {
        events: of({}),
      }
    );
    const navControllerSpy = jasmine.createSpyObj('NavController', [
      'navigateBack',
      'navigateForward',
      'navigateRoot',
    ]);
    const activatedRouteSpy = jasmine.createSpyObj('ActivatedRoute', [], {
      params: of({}),
      queryParams: of({}),
      snapshot: { params: {}, queryParams: {} },
    });

    // Mock the Router methods
    routerSpy.createUrlTree.and.returnValue({} as any);
    routerSpy.serializeUrl.and.returnValue('/register');

    // Set default return value for ApiService.login to prevent subscribe errors
    apiServiceSpy.login.and.returnValue(of(mockAuthResponse));

    await TestBed.configureTestingModule({
      imports: [LoginPage, ReactiveFormsModule],
      providers: [
        { provide: ApiService, useValue: apiServiceSpy },
        { provide: Router, useValue: routerSpy },
        { provide: NavController, useValue: navControllerSpy },
        { provide: ActivatedRoute, useValue: activatedRouteSpy },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(LoginPage);
    component = fixture.componentInstance;
    fixture.detectChanges(); // Trigger initial change detection
    mockApiService = TestBed.inject(ApiService) as jasmine.SpyObj<ApiService>;
    mockRouter = TestBed.inject(Router) as jasmine.SpyObj<Router>;
    mockNavController = TestBed.inject(
      NavController
    ) as jasmine.SpyObj<NavController>;
    mockActivatedRoute = TestBed.inject(
      ActivatedRoute
    ) as jasmine.SpyObj<ActivatedRoute>;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize form with empty values', () => {
    expect(component.loginForm.get('username')?.value).toBe('');
    expect(component.loginForm.get('password')?.value).toBe('');
  });

  it('should validate required fields', () => {
    const usernameControl = component.loginForm.get('username');
    const passwordControl = component.loginForm.get('password');

    expect(usernameControl?.hasError('required')).toBeTruthy();
    expect(passwordControl?.hasError('required')).toBeTruthy();
  });

  it('should validate password minimum length', () => {
    const passwordControl = component.loginForm.get('password');
    passwordControl?.setValue('123');
    expect(passwordControl?.hasError('minlength')).toBeTruthy();

    passwordControl?.setValue('123456');
    expect(passwordControl?.hasError('minlength')).toBeFalsy();
  });

  it('should not submit form when invalid', () => {
    // Reset the mock to track calls but keep the return value
    mockApiService.login.calls.reset();
    mockApiService.login.and.returnValue(of(mockAuthResponse));
    spyOn(component, 'onSubmit').and.callThrough();
    component.onSubmit();
    expect(mockApiService.login).not.toHaveBeenCalled();
  });

  it('should submit form when valid and call API service', () => {
    const loginData: LoginRequest = {
      usernameOrEmail: '<EMAIL>',
      password: 'password123',
    };

    component.loginForm.patchValue({
      username: '<EMAIL>',
      password: 'password123',
    });

    mockApiService.login.and.returnValue(of(mockAuthResponse));

    component.onSubmit();

    expect(mockApiService.login).toHaveBeenCalledWith(loginData);
    expect(mockRouter.navigate).toHaveBeenCalledWith(['/dashboard']);
  });

  it('should handle login success', fakeAsync(() => {
    component.loginForm.patchValue({
      username: '<EMAIL>',
      password: 'password123',
    });

    mockApiService.login.and.returnValue(of(mockAuthResponse));

    component.onSubmit();

    // Advance the fake async timer to complete the Observable
    tick();
    fixture.detectChanges();

    expect(component.isLoading()).toBeFalsy();
    expect(component.errorMessage()).toBe('');
  }));

  it('should handle login error', fakeAsync(() => {
    component.loginForm.patchValue({
      username: '<EMAIL>',
      password: 'wrongpassword',
    });

    const errorResponse = {
      error: { message: 'Invalid credentials' },
      status: 401,
    };

    mockApiService.login.and.returnValue(throwError(() => errorResponse));

    component.onSubmit();

    // Advance the fake async timer to complete the Observable
    tick();
    fixture.detectChanges();

    expect(component.errorMessage()).toBe(
      'Login failed. Please check your credentials.'
    );
    expect(component.isLoading()).toBeFalsy();
  }));

  it('should set loading state during submission', fakeAsync(() => {
    component.loginForm.patchValue({
      username: '<EMAIL>',
      password: 'password123',
    });

    mockApiService.login.and.returnValue(of(mockAuthResponse));

    component.onSubmit();

    // Advance the fake async timer to complete the Observable
    tick();
    fixture.detectChanges();

    expect(component.isLoading()).toBeFalsy(); // Should be false after completion
  }));

  it('should not submit when already loading', () => {
    component.loginForm.patchValue({
      username: '<EMAIL>',
      password: 'password123',
    });

    // Reset the mock to track calls but keep the return value
    mockApiService.login.calls.reset();
    mockApiService.login.and.returnValue(of(mockAuthResponse));
    component.isLoading.set(true);
    component.onSubmit();

    expect(mockApiService.login).not.toHaveBeenCalled();
  });

  it('should call onForgotPassword when forgot password is clicked', () => {
    spyOn(component, 'onForgotPassword').and.callThrough();
    component.onForgotPassword();
    expect(component.onForgotPassword).toHaveBeenCalled();
  });
});
