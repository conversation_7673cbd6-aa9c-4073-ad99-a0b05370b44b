import { TestBed } from '@angular/core/testing';
import {
  HttpClientTestingModule,
  HttpTestingController,
} from '@angular/common/http/testing';
import { ToastController } from '@ionic/angular';
import { of, throwError } from 'rxjs';
import { ApiService } from './api.service';
import { environment } from '../../../environments/environment';
import {
  LoginRequest,
  RegisterRequest,
  AuthResponse,
  RefreshResponse,
  SuccessResponse,
  BalanceResponse,
  Transaction,
  Sale,
  CreateSaleRequest,
  UpdateSaleRequest,
  UserAllocation,
  ClaimResponse,
  SendTransactionRequest,
  PreparedTransaction,
  User,
} from '../../types';

describe('ApiService', () => {
  let service: ApiService;
  let httpMock: HttpTestingController;
  let mockToastController: jasmine.SpyObj<ToastController>;
  let mockLocalStorage: jasmine.SpyObj<Storage>;

  const mockUser: User = {
    id: '1',
    username: 'testuser',
    email: '<EMAIL>',
    role: 'user',
    xrplAddress: 'rTestAddress123456789',
  };

  const mockAuthResponse: AuthResponse = {
    accessToken: 'mock-access-token',
    refreshToken: 'mock-refresh-token',
    user: mockUser,
  };

  const mockBalance: BalanceResponse = {
    xrp: 1000.5,
    currency: 'XRP',
    address: 'rTestAddress123456789',
    ledgerIndex: 12345,
    validated: true,
  };

  const mockTransactions: Transaction[] = [
    {
      hash: 'tx1',
      account: 'rTestAddress123456789',
      destination: 'rDestination123',
      amount: 100,
      currency: 'XRP',
      fee: 0.000012,
      ledgerIndex: 12345,
      date: '2024-01-01T00:00:00Z',
      type: 'payment',
      validated: true,
    },
  ];

  const mockSale: Sale = {
    id: 'sale1',
    symbol: 'TEST',
    price: 0.1,
    description: 'Test description',
    status: 'active',
    start: '2024-01-01T00:00:00Z',
    end: '2024-12-31T23:59:59Z',
    softCap: 1000,
    hardCap: 5000,
    collectionAddress: 'rCollection123',
    website: 'https://test.com',
    whitepaper: 'https://test.com/whitepaper',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
  };

  beforeEach(() => {
    const toastControllerSpy = jasmine.createSpyObj('ToastController', [
      'create',
    ]);

    // Mock localStorage
    mockLocalStorage = jasmine.createSpyObj('localStorage', [
      'getItem',
      'setItem',
      'removeItem',
      'clear',
    ]);
    Object.defineProperty(window, 'localStorage', { value: mockLocalStorage });

    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [{ provide: ToastController, useValue: toastControllerSpy }],
    });

    service = TestBed.inject(ApiService);
    httpMock = TestBed.inject(HttpTestingController);
    mockToastController = TestBed.inject(
      ToastController
    ) as jasmine.SpyObj<ToastController>;

    // Mock toast creation
    mockToastController.create.and.returnValue(
      Promise.resolve({
        present: jasmine.createSpy('present'),
      } as any)
    );

    // Clear localStorage
    mockLocalStorage.clear();
  });

  afterEach(() => {
    httpMock.verify();
    mockLocalStorage.clear();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('Authentication', () => {
    it('should login successfully', () => {
      const loginData: LoginRequest = {
        usernameOrEmail: '<EMAIL>',
        password: 'password123',
      };

      service.login(loginData).subscribe(response => {
        expect(response).toEqual(mockAuthResponse);
        expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
          'access_token',
          'mock-access-token'
        );
        expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
          'refresh_token',
          'mock-refresh-token'
        );
        expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
          'user',
          JSON.stringify(mockUser)
        );
      });

      const req = httpMock.expectOne(`${environment.apiUrl}/auth/login`);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual(loginData);
      req.flush(mockAuthResponse);
    });

    it('should register successfully', () => {
      const registerData: RegisterRequest = {
        username: 'testuser',
        email: '<EMAIL>',
        password: 'password123',
      };

      service.register(registerData).subscribe(response => {
        expect(response).toEqual(mockAuthResponse);
      });

      const req = httpMock.expectOne(`${environment.apiUrl}/auth/register`);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual(registerData);
      req.flush(mockAuthResponse);
    });

    it('should refresh token successfully', () => {
      mockLocalStorage.getItem.and.returnValue('mock-refresh-token');
      const mockRefreshResponse: RefreshResponse = {
        accessToken: 'new-access-token',
      };

      service.refreshToken().subscribe(response => {
        expect(response).toEqual(mockRefreshResponse);
        expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
          'access_token',
          'new-access-token'
        );
      });

      const req = httpMock.expectOne(`${environment.apiUrl}/auth/refresh`);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual({ refreshToken: 'mock-refresh-token' });
      req.flush(mockRefreshResponse);
    });

    it('should get current user profile', () => {
      mockLocalStorage.getItem.and.returnValue('mock-token');

      service.getMe().subscribe(response => {
        expect(response).toEqual(mockUser);
        expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
          'user',
          JSON.stringify(mockUser)
        );
      });

      const req = httpMock.expectOne(`${environment.apiUrl}/auth/me`);
      expect(req.request.method).toBe('GET');
      expect(req.request.headers.get('Authorization')).toBe(
        'Bearer mock-token'
      );
      req.flush(mockUser);
    });

    it('should logout successfully', () => {
      mockLocalStorage.getItem.and.returnValue('mock-token');

      service.logout().subscribe();

      const req = httpMock.expectOne(`${environment.apiUrl}/auth/logout`);
      expect(req.request.method).toBe('POST');
      req.flush({});

      expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('access_token');
      expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('refresh_token');
      expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('user');
    });

    it('should clear local storage even if logout fails', () => {
      mockLocalStorage.getItem.and.returnValue('mock-token');

      service.logout().subscribe({
        error: () => {
          expect(mockLocalStorage.removeItem).toHaveBeenCalledWith(
            'access_token'
          );
          expect(mockLocalStorage.removeItem).toHaveBeenCalledWith(
            'refresh_token'
          );
          expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('user');
        },
      });

      const req = httpMock.expectOne(`${environment.apiUrl}/auth/logout`);
      req.flush('Error', { status: 500, statusText: 'Internal Server Error' });
    });
  });

  describe('XRPL Endpoints', () => {
    beforeEach(() => {
      mockLocalStorage.getItem.and.returnValue('mock-token');
    });

    it('should get balance', () => {
      service.getBalance().subscribe(response => {
        expect(response).toEqual(mockBalance);
      });

      const req = httpMock.expectOne(`${environment.apiUrl}/xrpl/balance`);
      expect(req.request.method).toBe('GET');
      expect(req.request.headers.get('Authorization')).toBe(
        'Bearer mock-token'
      );
      req.flush(mockBalance);
    });

    it('should get transactions', () => {
      service.getTransactions().subscribe(response => {
        expect(response).toEqual(mockTransactions);
      });

      const req = httpMock.expectOne(`${environment.apiUrl}/xrpl/transactions`);
      expect(req.request.method).toBe('GET');
      expect(req.request.headers.get('Authorization')).toBe(
        'Bearer mock-token'
      );
      req.flush(mockTransactions);
    });

    it('should prepare send transaction', () => {
      const sendRequest: SendTransactionRequest = {
        destination: 'rDestination123',
        amount: 100,
      };

      const mockPreparedTx: PreparedTransaction = {
        transactionBlob: 'mock-blob',
        hash: 'mock-hash',
        amount: 100,
        fee: 0.000012,
        sequence: 123,
        lastLedgerSequence: 12345,
        message: 'Mock transaction',
      };

      service.prepareSendTransaction(sendRequest).subscribe(response => {
        expect(response).toEqual(mockPreparedTx);
      });

      const req = httpMock.expectOne(`${environment.apiUrl}/xrpl/send`);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual(sendRequest);
      req.flush(mockPreparedTx);
    });
  });

  describe('Launchpad Endpoints', () => {
    it('should get sales', () => {
      service.getSales().subscribe(response => {
        expect(response).toEqual([mockSale]);
      });

      const req = httpMock.expectOne(`${environment.apiUrl}/launchpad/sales`);
      expect(req.request.method).toBe('GET');
      req.flush([mockSale]);
    });

    it('should get sales with status filter', () => {
      service.getSales('active').subscribe(response => {
        expect(response).toEqual([mockSale]);
      });

      const req = httpMock.expectOne(
        `${environment.apiUrl}/launchpad/sales?status=active`
      );
      expect(req.request.method).toBe('GET');
      req.flush([mockSale]);
    });

    it('should get active sales', () => {
      service.getActiveSales().subscribe(response => {
        expect(response).toEqual([mockSale]);
      });

      const req = httpMock.expectOne(
        `${environment.apiUrl}/launchpad/sales/active`
      );
      expect(req.request.method).toBe('GET');
      req.flush([mockSale]);
    });

    it('should get sale by id', () => {
      service.getSale('sale1').subscribe(response => {
        expect(response).toEqual(mockSale);
      });

      const req = httpMock.expectOne(
        `${environment.apiUrl}/launchpad/sales/sale1`
      );
      expect(req.request.method).toBe('GET');
      req.flush(mockSale);
    });

    it('should create sale', () => {
      mockLocalStorage.getItem.and.returnValue('mock-token');
      const createSaleData: CreateSaleRequest = {
        symbol: 'NEW',
        price: 0.1,
        description: 'New sale description',
        start: '2024-01-01T00:00:00Z',
        end: '2024-12-31T23:59:59Z',
        softCap: 1000,
        hardCap: 5000,
        collectionAddress: 'rCollection123',
      };

      service.createSale(createSaleData).subscribe(response => {
        expect(response).toEqual(mockSale);
      });

      const req = httpMock.expectOne(`${environment.apiUrl}/launchpad/sales`);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual(createSaleData);
      req.flush(mockSale);
    });

    it('should update sale', () => {
      mockLocalStorage.getItem.and.returnValue('mock-token');
      const updateSaleData: UpdateSaleRequest = {
        symbol: 'UPDATED',
      };

      service.updateSale('sale1', updateSaleData).subscribe(response => {
        expect(response).toEqual(mockSale);
      });

      const req = httpMock.expectOne(
        `${environment.apiUrl}/launchpad/sales/sale1`
      );
      expect(req.request.method).toBe('PUT');
      expect(req.request.body).toEqual(updateSaleData);
      req.flush(mockSale);
    });

    it('should delete sale', () => {
      mockLocalStorage.getItem.and.returnValue('mock-token');

      service.deleteSale('sale1').subscribe();

      const req = httpMock.expectOne(
        `${environment.apiUrl}/launchpad/sales/sale1`
      );
      expect(req.request.method).toBe('DELETE');
      req.flush({});
    });

    it('should get user allocation', () => {
      mockLocalStorage.getItem.and.returnValue('mock-token');
      const mockAllocation: UserAllocation = {
        saleId: 'sale1',
        userId: '1',
        contributionAmount: 100,
        allocationAmount: 100,
        allocationPercentage: 10,
        saleStatus: 'ended',
        saleEnded: true,
      };

      service.getUserAllocation('sale1').subscribe(response => {
        expect(response).toEqual(mockAllocation);
      });

      const req = httpMock.expectOne(
        `${environment.apiUrl}/launchpad/sales/sale1/allocation`
      );
      expect(req.request.method).toBe('GET');
      req.flush(mockAllocation);
    });

    it('should claim tokens', () => {
      mockLocalStorage.getItem.and.returnValue('mock-token');
      const mockClaimResponse: ClaimResponse = {
        success: true,
        message: 'Tokens claimed successfully',
      };

      service.claimTokens('sale1').subscribe(response => {
        expect(response).toEqual(mockClaimResponse);
      });

      const req = httpMock.expectOne(
        `${environment.apiUrl}/launchpad/sales/sale1/claim`
      );
      expect(req.request.method).toBe('POST');
      req.flush(mockClaimResponse);
    });
  });

  describe('User Management', () => {
    it('should link XRPL address', () => {
      mockLocalStorage.getItem.and.callFake(key => {
        if (key === 'access_token') return 'mock-token';
        if (key === 'user') return JSON.stringify(mockUser);
        return null;
      });
      service['currentUserSubject'].next(mockUser);

      const successResponse: SuccessResponse = {
        success: true,
        message: 'XRPL address successfully linked',
      };

      const expectedUpdatedUser = {
        ...mockUser,
        xrplAddress: 'rNewAddress123456789',
      };

      service.linkXrplAddress('rNewAddress123456789').subscribe(response => {
        expect(response).toEqual(successResponse);
        expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
          'user',
          JSON.stringify(expectedUpdatedUser)
        );
      });

      const req = httpMock.expectOne(`${environment.apiUrl}/users/xrpl/link`);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual({ xrplAddress: 'rNewAddress123456789' });
      req.flush(successResponse);
    });
  });

  describe('Utility Methods', () => {
    it('should check authentication status', () => {
      mockLocalStorage.getItem.and.returnValue(null);
      expect(service.isAuthenticated()).toBeFalsy();

      mockLocalStorage.getItem.and.returnValue('mock-token');
      expect(service.isAuthenticated()).toBeTruthy();
    });

    it('should get current user', () => {
      expect(service.getCurrentUser()).toBeNull();

      service['currentUserSubject'].next(mockUser);
      expect(service.getCurrentUser()).toEqual(mockUser);
    });

    it('should check admin role', () => {
      expect(service.isAdmin()).toBeFalsy();

      const adminUser = { ...mockUser, role: 'admin' };
      service['currentUserSubject'].next(adminUser);
      expect(service.isAdmin()).toBeTruthy();
    });

    it('should get current user if needed', () => {
      // When no current user
      service.getMeIfNeeded().subscribe();

      const req = httpMock.expectOne(`${environment.apiUrl}/auth/me`);
      req.flush(mockUser);

      // When current user exists
      service['currentUserSubject'].next(mockUser);
      service.getMeIfNeeded().subscribe(response => {
        expect(response).toEqual(mockUser);
      });
    });
  });

  describe('Error Handling', () => {
    it('should handle 401 errors with auto-logout', () => {
      mockLocalStorage.getItem.and.callFake(key => {
        if (key === 'access_token') return 'mock-token';
        if (key === 'user') return JSON.stringify(mockUser);
        return null;
      });

      service.getBalance().subscribe({
        error: () => {
          expect(mockLocalStorage.removeItem).toHaveBeenCalledWith(
            'access_token'
          );
          expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('user');
        },
      });

      const req = httpMock.expectOne(`${environment.apiUrl}/xrpl/balance`);
      req.flush('Unauthorized', { status: 401, statusText: 'Unauthorized' });
    });

    it('should show error toast for different error statuses', () => {
      service.getBalance().subscribe({
        error: () => {
          expect(mockToastController.create).toHaveBeenCalled();
        },
      });

      const req = httpMock.expectOne(`${environment.apiUrl}/xrpl/balance`);
      req.flush('Bad Request', { status: 400, statusText: 'Bad Request' });
    });
  });
});
