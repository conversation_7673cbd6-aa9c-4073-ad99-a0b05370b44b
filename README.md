# XRPL Launchpad

[![CI](https://github.com/opensea712/xrpl-launchpad/workflows/CI%20Pipeline/badge.svg)](https://github.com/opensea712/xrpl-launchpad/actions)
[![License](https://img.shields.io/badge/license-MIT-blue.svg)](LICENSE)
[![Node.js](https://img.shields.io/badge/node-%3E%3D18.0.0-brightgreen.svg)](https://nodejs.org/)
[![TypeScript](https://img.shields.io/badge/typescript-%3E%3D5.0.0-blue.svg)](https://www.typescriptlang.org/)

A comprehensive launchpad platform for XRPL (XRP Ledger) projects, enabling token sales, XRPL account integration, and real-time transaction monitoring. Built with NestJS backend and Angular/Ionic frontend for cross-platform support.

## 📚 Documentation

- **Backend API Documentation**: [Swagger UI](http://localhost:3000/api/docs) (when running)
- **Frontend Documentation**: [Compodoc](web/documentation/index.html)
- **API OpenAPI Spec**: [JSON Format](http://localhost:3000/api-json) (when running)
- **Docker Setup Guide**: [Docker Setup Documentation](docs/DOCKER_SETUP.md)

## 🚀 Features

### Core Functionality

- **Token Sales Management**: Create, manage, and participate in XRPL token sales
- **XRPL Account Integration**: Link and manage XRPL Testnet accounts
- **Real-time Updates**: WebSocket-based live balance and transaction monitoring
- **Admin Dashboard**: Comprehensive admin interface for sale management
- **User Authentication**: Secure JWT-based authentication with role management

### Technical Features

- **Backend API**: NestJS with TypeScript, PostgreSQL, JWT authentication
- **Frontend**: Angular with Ionic for cross-platform mobile support
- **XRPL Integration**: Built-in XRPL client and utilities
- **Real-time Communication**: WebSocket integration for live updates
- **Client-side Signing**: Secure transaction preparation with client-side signing
- **Monorepo Structure**: Efficient development with npm workspaces
- **CI/CD Pipeline**: Automated testing, linting, and deployment

## 📸 Screenshots

Take a look at the XRPL Launchpad in action:

<div align="center">
  <img src="docs/screenshots/dashboard.png" alt="Dashboard" width="300"/>
  <img src="docs/screenshots/dashboard2-dark.png" alt="Dashboard Dark Mode" width="300"/>
  <img src="docs/screenshots/launchpad.png" alt="Launchpad" width="300"/>
</div>

<div align="center">
  <img src="docs/screenshots/login.png" alt="Login" width="300"/>
  <img src="docs/screenshots/register.png" alt="Register" width="300"/>
  <img src="docs/screenshots/sidemenu.png" alt="Side Menu" width="300"/>
</div>

<div align="center">
  <img src="docs/screenshots/sale-details1.png" alt="Sale Details 1" width="300"/>
  <img src="docs/screenshots/sale-details2.png" alt="Sale Details 2" width="300"/>
  <img src="docs/screenshots/sale-details3.png" alt="Sale Details 3" width="300"/>
</div>

## 🏗️ Architecture

### System Architecture Diagram

```
┌─────────────────────────────────────────────────────────────────┐
│                        XRPL Launchpad                          │
├─────────────────────────────────────────────────────────────────┤
│  Frontend (Angular/Ionic)     │  Backend (NestJS)              │
│  ┌─────────────────────────┐   │  ┌─────────────────────────┐   │
│  │ • Dashboard             │   │  │ • Authentication        │   │
│  │ • Token Sales           │   │  │ • User Management       │   │
│  │ • XRPL Connect          │   │  │ • Sale Management       │   │
│  │ • Admin Panel           │   │  │ • XRPL Integration      │   │
│  │ • Real-time Updates     │   │  │ • WebSocket Gateway     │   │
│  └─────────────────────────┘   │  └─────────────────────────┘   │
│              │                 │              │                 │
│              │ HTTP/WebSocket  │              │                 │
│              └─────────────────┼──────────────┘                 │
│                                │                                │
│                                ▼                                │
│  ┌─────────────────────────────────────────────────────────┐   │
│  │                PostgreSQL Database                      │   │
│  │  • Users & Authentication                              │   │
│  │  • Token Sales & Contributions                         │   │
│  │  • XRPL Address Linking                                │   │
│  └─────────────────────────────────────────────────────────┘   │
│                                │                                │
│                                ▼                                │
│  ┌─────────────────────────────────────────────────────────┐   │
│  │                XRPL Testnet                            │   │
│  │  • Account Balance Queries                             │   │
│  │  • Transaction History                                 │   │
│  │  • Transaction Preparation                             │   │
│  └─────────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────┘
```

### Project Structure

```
xrpl-launchpad/
├── api/                    # NestJS backend API
│   ├── src/
│   │   ├── auth/          # Authentication module
│   │   ├── users/         # User management
│   │   ├── launchpad/     # Token sale management
│   │   ├── xrpl/          # XRPL integration
│   │   └── common/        # Shared utilities
│   ├── test/              # Backend tests
│   └── dist/              # Compiled backend
├── web/                    # Angular/Ionic frontend
│   ├── src/app/
│   │   ├── pages/         # Application pages
│   │   ├── services/      # API and utility services
│   │   ├── components/    # Reusable components
│   │   └── guards/        # Route guards
│   ├── documentation/     # Generated Compodoc docs
│   └── www/               # Built frontend
├── scripts/               # Development and CI scripts
└── .github/workflows/     # GitHub Actions CI/CD
```

## 🛠️ Tech Stack

### Backend

- **Framework**: NestJS 11
- **Language**: TypeScript 5
- **Database**: PostgreSQL with TypeORM
- **Authentication**: JWT with Passport
- **Testing**: Jest

### Frontend

- **Framework**: Angular 20
- **UI Framework**: Ionic 8
- **Language**: TypeScript 5
- **Testing**: Karma/Jasmine
- **Mobile**: Capacitor for native builds

## 📋 Prerequisites

- Node.js 18+
- npm 9+
- PostgreSQL 15+
- Git

## 🚀 Quick Start

### Option 1: Docker (Recommended)

For the fastest setup, use Docker:

```bash
# Clone the repository
git clone https://github.com/opensea712/xrpl-launchpad.git
cd xrpl-launchpad

# Start all services with Docker
docker-compose up -d

# Access the application
# API: http://localhost:3000
# Web: http://localhost:80 or http://localhost:8100
```

For detailed Docker setup instructions, see [Docker Setup Documentation](docs/DOCKER_SETUP.md).

### Option 2: Manual Setup

#### 1. Clone the Repository

```bash
git clone https://github.com/opensea712/xrpl-launchpad.git
cd xrpl-launchpad
```

#### 2. Install Dependencies

```bash
npm ci
```

#### 3. Set Up Environment

Create environment files for your local development:

```bash
# API environment
cp api/env.example api/.env

# Configure your environment variables
# Edit api/.env with your database and XRPL settings
```

**Required Environment Variables:**

```bash
# Database
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=xrpl_launchpad
DATABASE_USERNAME=postgres
DATABASE_PASSWORD=your_password

# JWT Secrets (generate secure random strings)
JWT_SECRET=your_jwt_secret_here
JWT_REFRESH_SECRET=your_refresh_secret_here

# XRPL Configuration
XRPL_WS_URL=wss://s.altnet.rippletest.net:51233
XRPL_ISSUER_SEED=your_issuer_seed_here

# Application
PORT=3000
FRONTEND_URL=http://localhost:8100
```

#### 4. Set Up Database

```bash
# Start PostgreSQL (if using Docker)
docker-compose up -d postgres

# Run database migrations
cd api
npm run migration:run

# Seed the database (admin user) (optional)
npm run db:seed
```

#### 5. Start Development Servers

```bash
# Start both API and Web in parallel
npm run dev

# Or start individually:
npm run api:dev    # API backend (http://localhost:3000)
npm run web:dev    # Web frontend (http://localhost:8100)
```

#### 6. Access the Application

- **Frontend**: http://localhost:8100
- **Backend API**: http://localhost:3000
- **API Documentation**: http://localhost:3000/api/docs
- **Frontend Documentation**: `web/documentation/index.html`

## 📱 Ionic Setup & Mobile Development

### Prerequisites for Mobile Development

```bash
# Install Ionic CLI globally
npm install -g @ionic/cli

# Install Capacitor CLI
npm install -g @capacitor/cli
```

### Building for Mobile

```bash
# Build the web app
cd web
npm run build

# Add mobile platforms
npx cap add ios
npx cap add android

# Sync with native projects
npx cap sync

# Open in native IDEs
npx cap open ios     # Opens Xcode
npx cap open android # Opens Android Studio
```

### Running on Device/Emulator

```bash
# Run on iOS simulator
npx cap run ios

# Run on Android emulator
npx cap run android

# Run on connected device
npx cap run ios --target="device-name"
npx cap run android --target="device-name"
```

## 🔗 XRPL Account Connection

### Setting Up XRPL Testnet Account

1. **Generate XRPL Testnet Account**:
   - Visit [XRPL Testnet Faucet](https://xrpl.org/xrp-testnet-faucet.html)
   - Generate a new testnet account
   - Save your account address and secret key securely

2. **Link Account in Application**:
   - Navigate to "Connect XRPL Account" in the app
   - Enter your XRPL Testnet address
   - The system will validate and link your account

3. **Using XRPL Features**:
   - View real-time balance updates
   - Monitor transaction history
   - Participate in token sales
   - Prepare transactions for client-side signing

### Admin Sales Page

Admin users can access the sales management interface at `/admin/sales` to:

- Create new token sales with comprehensive validation
- Manage existing sales (update, cancel, end)
- Monitor sale statistics and contributions
- View detailed sale analytics

## ⚖️ Trade-offs & Design Decisions

### Client-side Signing

- **Decision**: Transactions are prepared server-side but signed client-side
- **Rationale**: Enhanced security by keeping private keys on the client
- **Trade-off**: Requires more complex transaction flow and user interaction

### Admin Role Guard

- **Decision**: Role-based access control for admin functions
- **Rationale**: Secure separation of admin and user functionality
- **Trade-off**: Additional complexity in authentication and authorization

### WebSocket Integration

- **Decision**: Real-time updates for balance and transactions
- **Rationale**: Better user experience with live data
- **Trade-off**: Additional complexity in connection management and error handling

### XRPL Testnet Focus

- **Decision**: Built for XRPL Testnet environment
- **Rationale**: Safe testing environment for development and demonstration
- **Trade-off**: Requires separate implementation for mainnet deployment

## 🧪 Running Tests

### Run All Tests

```bash
npm run test
```

### Run Specific Workspace Tests

```bash
npm run api:test    # Backend tests
npm run web:test    # Frontend tests
```

### Run CI Checks Locally

```bash
# Unix/macOS
./scripts/ci-local.sh

# Windows PowerShell
.\scripts\ci-local.ps1
```

## 🔧 Development Commands

```bash
# Linting
npm run lint              # Run linting
npm run lint:check        # Check linting without auto-fix
npm run format            # Format code with Prettier
npm run format:check      # Check formatting

# Building
npm run build             # Build all workspaces
npm run api:build         # Build API only
npm run web:build         # Build Web only

# Development
npm run dev               # Start both dev servers
npm run api:dev           # Start API dev server
npm run web:dev           # Start Web dev server

# Ionic-specific commands
npm run start:ionic       # Start Ionic development server
npm run docs              # Generate frontend documentation
npm run docs:build        # Build frontend documentation
```

## 🚀 Quick Run Instructions

### Start the Application

```bash
# 1. Install dependencies
npm ci

# 2. Set up environment (copy and configure .env file)
cp api/.env.example api/.env
# Edit api/.env with your configuration

# 3. Set up database
cd api
npm run migration:run

# 4. Start the application
cd ..
npm run start:ionic
```

The application will be available at:

- **Frontend**: http://localhost:8100
- **Backend API**: http://localhost:3000
- **API Documentation**: http://localhost:3000/api/docs

## 📊 CI/CD Pipeline

This project uses GitHub Actions for continuous integration:

- **Automated Testing**: Runs on every push and PR
- **Code Quality**: ESLint and Prettier checks
- **Type Safety**: TypeScript compilation validation
- **Security**: Dependency vulnerability scanning
- **Build Verification**: Ensures all components build successfully

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Make your changes
4. Run CI checks locally (`./scripts/ci-local.sh`)
5. Commit your changes (`git commit -m 'Add amazing feature'`)
6. Push to the branch (`git push origin feature/amazing-feature`)
7. Open a Pull Request

### Development Guidelines

- Follow the established code style (ESLint + Prettier)
- Write tests for new functionality
- Ensure all CI checks pass
- Update documentation as needed

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Issues**: [GitHub Issues](https://github.com/opensea712/xrpl-launchpad/issues)
- **Discussions**: [GitHub Discussions](https://github.com/opensea712/xrpl-launchpad/discussions)

## 🙏 Acknowledgments

- [XRPL Foundation](https://xrpl.org/) for the XRP Ledger
- [NestJS](https://nestjs.com/) for the backend framework
- [Angular](https://angular.io/) for the frontend framework
- [Ionic](https://ionicframework.com/) for cross-platform mobile support
