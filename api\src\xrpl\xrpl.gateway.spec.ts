import { Test, TestingModule } from '@nestjs/testing';
import { XrplGateway } from './xrpl.gateway';
import { XrplService } from './xrpl.service';
import { Subject } from 'rxjs';
import { Server, Socket } from 'socket.io';
import { WsJwtAuthGuard } from '../auth/guards/ws-jwt-auth.guard';

// Define a proper mock Socket type that extends the base Socket
type MockSocket = Partial<Socket> & {
  id: string;
  emit: jest.Mock;
  join: jest.Mock;
  leave: jest.Mock;
  user?: { id: string; email: string } | null;
};

// Mock the WsJwtAuthGuard
const mockWsJwtAuthGuard = {
  canActivate: jest.fn().mockReturnValue(true),
};

describe('XrplGateway', () => {
  let gateway: XrplGateway;
  let xrplService: jest.Mocked<XrplService>;
  let mockServer: jest.Mocked<Server>;
  let mockSocket: MockSocket;

  const mockUser = {
    id: 'user123',
    email: '<EMAIL>',
  };

  const mockTransaction = {
    transaction: {
      Account: 'rTestAddress123456789',
      Destination: 'rDestAddress123456789',
      Amount: '1000000',
      hash: 'txHash123',
    },
  };

  beforeEach(async () => {
    const mockXrplService = {
      subscribeToAccount: jest.fn(),
      unsubscribeFromAccount: jest.fn(),
      getBalance: jest.fn().mockResolvedValue({
        address: 'rTestAddress123456789',
        xrp: 1000,
        currency: 'XRP',
        ledgerIndex: 12345,
        validated: true,
      }),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        XrplGateway,
        {
          provide: XrplService,
          useValue: mockXrplService,
        },
      ],
    })
      .overrideGuard(WsJwtAuthGuard)
      .useValue(mockWsJwtAuthGuard)
      .compile();

    gateway = module.get<XrplGateway>(XrplGateway);
    xrplService = module.get(XrplService);

    // Mock Socket.IO server
    mockServer = {
      to: jest.fn().mockReturnThis(),
      emit: jest.fn(),
    } as any;

    // Mock Socket.IO client
    mockSocket = {
      id: 'socket123',
      emit: jest.fn(),
      join: jest.fn(),
      leave: jest.fn(),
      user: mockUser,
    };

    // Set the server property
    (gateway as any).server = mockServer;
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('handleConnection', () => {
    it('should log client connection', () => {
      const logSpy = jest.spyOn(gateway['logger'], 'log');

      gateway.handleConnection(mockSocket as any);

      expect(logSpy).toHaveBeenCalledWith('Client connected: socket123');
    });
  });

  describe('handleDisconnect', () => {
    it('should log client disconnection and remove from subscriptions', () => {
      const logSpy = jest.spyOn(gateway['logger'], 'log');
      const removeClientSpy = jest.spyOn(
        gateway as any,
        'removeClientFromAllSubscriptions',
      );

      gateway.handleDisconnect(mockSocket as any);

      expect(logSpy).toHaveBeenCalledWith('Client disconnected: socket123');
      expect(removeClientSpy).toHaveBeenCalledWith(mockSocket);
    });
  });

  describe('handleSubscribe', () => {
    it('should subscribe to address successfully', () => {
      const mockSubscription = new Subject();
      const address = 'rTestAddress123456789';
      const data = { address };

      xrplService.subscribeToAccount.mockReturnValue(mockSubscription);

      gateway.handleSubscribe(mockSocket as any, data);

      expect(xrplService.subscribeToAccount).toHaveBeenCalledWith(address);
      expect(mockSocket.emit).toHaveBeenCalledWith('subscribed', { address });
    });

    it('should handle missing authentication', () => {
      const socketWithoutUser: MockSocket = {
        ...mockSocket,
        user: null,
      };
      const data = { address: 'rTestAddress123456789' };

      gateway.handleSubscribe(socketWithoutUser as any, data);

      expect(socketWithoutUser.emit).toHaveBeenCalledWith('error', {
        type: 'authentication_error',
        message: 'Authentication required',
      });
      expect(xrplService.subscribeToAccount).not.toHaveBeenCalled();
    });

    it('should handle missing address', () => {
      const data = { address: '' };

      gateway.handleSubscribe(mockSocket as any, data);

      expect(mockSocket.emit).toHaveBeenCalledWith('error', {
        type: 'validation_error',
        message: 'Address is required',
      });
      expect(xrplService.subscribeToAccount).not.toHaveBeenCalled();
    });

    it('should handle subscription errors', () => {
      const address = 'rTestAddress123456789';
      const data = { address };
      const error = new Error('Subscription failed');

      xrplService.subscribeToAccount.mockImplementation(() => {
        throw error;
      });

      gateway.handleSubscribe(mockSocket as any, data);

      expect(mockSocket.emit).toHaveBeenCalledWith('error', {
        type: 'subscription_error',
        message: 'Subscription failed',
        details: 'Subscription failed',
      });
    });

    it('should emit transaction updates to subscribed users', () => {
      const address = 'rTestAddress123456789';
      const data = { address };
      const mockSubscription = new Subject();

      // Set up user sockets mapping
      (gateway as any).userSockets.set(mockUser.id, [mockSocket]);
      (gateway as any).addressSubscriptions.set(
        address,
        new Set([mockUser.id]),
      );

      xrplService.subscribeToAccount.mockReturnValue(mockSubscription);

      gateway.handleSubscribe(mockSocket as any, data);

      // Simulate transaction emission
      mockSubscription.next(mockTransaction);

      expect(mockSocket.emit).toHaveBeenCalledWith('transaction', {
        address,
        transaction: mockTransaction,
        timestamp: expect.any(String),
      });
    });

    it('should handle subscription errors gracefully', () => {
      const address = 'rTestAddress123456789';
      const data = { address };
      const mockSubscription = new Subject();

      xrplService.subscribeToAccount.mockReturnValue(mockSubscription);

      gateway.handleSubscribe(mockSocket as any, data);

      // Simulate subscription error
      mockSubscription.error(new Error('Subscription error'));

      expect(mockSocket.emit).toHaveBeenCalledWith('error', {
        type: 'subscription_error',
        message: 'Subscription failed',
        address,
        details: 'Subscription error',
      });
    });
  });

  describe('handleUnsubscribe', () => {
    it('should unsubscribe from address successfully', () => {
      const address = 'rTestAddress123456789';
      const data = { address };

      // Set up existing subscription
      (gateway as any).userSockets.set(mockUser.id, [mockSocket]);
      (gateway as any).addressSubscriptions.set(
        address,
        new Set([mockUser.id]),
      );

      gateway.handleUnsubscribe(mockSocket as any, data);

      expect(xrplService.unsubscribeFromAccount).toHaveBeenCalledWith(address);
      expect(mockSocket.emit).toHaveBeenCalledWith('unsubscribed', { address });
    });

    it('should handle missing authentication', () => {
      const socketWithoutUser: MockSocket = {
        ...mockSocket,
        user: null,
      };
      const data = { address: 'rTestAddress123456789' };

      gateway.handleUnsubscribe(socketWithoutUser as any, data);

      expect(socketWithoutUser.emit).toHaveBeenCalledWith('error', {
        type: 'authentication_error',
        message: 'Authentication required',
      });
    });

    it('should handle missing address', () => {
      const data = { address: '' };

      gateway.handleUnsubscribe(mockSocket as any, data);

      expect(mockSocket.emit).toHaveBeenCalledWith('error', {
        type: 'validation_error',
        message: 'Address is required',
      });
    });

    it('should stop monitoring address when no users are subscribed', () => {
      const address = 'rTestAddress123456789';
      const data = { address };

      // Set up existing subscription
      (gateway as any).userSockets.set(mockUser.id, [mockSocket]);
      (gateway as any).addressSubscriptions.set(
        address,
        new Set([mockUser.id]),
      );

      gateway.handleUnsubscribe(mockSocket as any, data);

      expect(xrplService.unsubscribeFromAccount).toHaveBeenCalledWith(address);
      expect((gateway as any).addressSubscriptions.has(address)).toBe(false);
    });

    it('should handle unsubscribe errors gracefully', () => {
      const address = 'rTestAddress123456789';
      const data = { address };

      // Set up existing subscription
      (gateway as any).userSockets.set(mockUser.id, [mockSocket]);
      (gateway as any).addressSubscriptions.set(
        address,
        new Set([mockUser.id]),
      );

      xrplService.unsubscribeFromAccount.mockImplementation(() => {
        throw new Error('Unsubscribe error');
      });

      gateway.handleUnsubscribe(mockSocket as any, data);

      expect(mockSocket.emit).toHaveBeenCalledWith('error', {
        type: 'unsubscribe_error',
        message: 'Unsubscribe failed',
        details: 'Unsubscribe error',
      });
    });
  });

  describe('removeClientFromAllSubscriptions', () => {
    it('should remove client from all subscriptions', () => {
      const address = 'rTestAddress123456789';

      // Set up existing subscriptions
      (gateway as any).userSockets.set(mockUser.id, [mockSocket]);
      (gateway as any).addressSubscriptions.set(
        address,
        new Set([mockUser.id]),
      );

      (gateway as any).removeClientFromAllSubscriptions(mockSocket);

      expect((gateway as any).userSockets.has(mockUser.id)).toBe(false);
      expect((gateway as any).addressSubscriptions.has(address)).toBe(false);
    });

    it('should handle user without subscriptions gracefully', () => {
      expect(() => {
        (gateway as any).removeClientFromAllSubscriptions(mockSocket);
      }).not.toThrow();
    });

    it('should stop monitoring addresses when user has no more connections', () => {
      const address = 'rTestAddress123456789';

      // Set up existing subscriptions
      (gateway as any).userSockets.set(mockUser.id, [mockSocket]);
      (gateway as any).addressSubscriptions.set(
        address,
        new Set([mockUser.id]),
      );

      (gateway as any).removeClientFromAllSubscriptions(mockSocket);

      expect(xrplService.unsubscribeFromAccount).toHaveBeenCalledWith(address);
    });
  });

  describe('subscription management', () => {
    it('should maintain user socket mappings correctly', () => {
      const address = 'rTestAddress123456789';
      const data = { address };

      gateway.handleSubscribe(mockSocket as any, data);

      expect((gateway as any).userSockets.get(mockUser.id)).toContain(
        mockSocket,
      );
    });

    it('should maintain address subscription mappings correctly', () => {
      const address = 'rTestAddress123456789';
      const data = { address };

      gateway.handleSubscribe(mockSocket as any, data);

      expect((gateway as any).addressSubscriptions.get(address)).toContain(
        mockUser.id,
      );
    });

    it('should handle multiple clients for the same user', () => {
      const address = 'rTestAddress123456789';
      const data = { address };
      const mockSocket2: MockSocket = {
        ...mockSocket,
        id: 'socket456',
        user: mockUser,
      };

      gateway.handleSubscribe(mockSocket as any, data);
      gateway.handleSubscribe(mockSocket2 as any, data);

      const userSockets = (gateway as any).userSockets.get(mockUser.id);
      expect(userSockets).toContain(mockSocket);
      expect(userSockets).toContain(mockSocket2);
    });

    it('should handle multiple users subscribing to the same address', () => {
      const address = 'rTestAddress123456789';
      const user2 = { id: 'user456', email: '<EMAIL>' };
      const mockSocket2: MockSocket = {
        ...mockSocket,
        id: 'socket456',
        user: user2,
      };

      gateway.handleSubscribe(mockSocket as any, { address });
      gateway.handleSubscribe(mockSocket2 as any, { address });

      const addressSubs = (gateway as any).addressSubscriptions.get(address);
      expect(addressSubs).toContain(mockUser.id);
      expect(addressSubs).toContain(user2.id);
    });
  });

  describe('error handling', () => {
    it('should handle XRPL service errors gracefully', () => {
      const address = 'rTestAddress123456789';
      const data = { address };

      xrplService.subscribeToAccount.mockImplementation(() => {
        throw new Error('XRPL service error');
      });

      expect(() => {
        gateway.handleSubscribe(mockSocket as any, data);
      }).not.toThrow();

      expect(mockSocket.emit).toHaveBeenCalledWith('error', {
        type: 'subscription_error',
        message: 'Subscription failed',
        details: 'XRPL service error',
      });
    });

    it('should handle missing user data gracefully', () => {
      const socketWithoutUser: MockSocket = {
        ...mockSocket,
        user: null,
      };
      const data = { address: 'rTestAddress123456789' };

      expect(() => {
        gateway.handleSubscribe(socketWithoutUser as any, data);
      }).not.toThrow();

      expect(socketWithoutUser.emit).toHaveBeenCalledWith('error', {
        type: 'authentication_error',
        message: 'Authentication required',
      });
    });
  });
});
