// Realtime and transaction component styles

.realtime-card {
  .balance-section {
    margin-bottom: 20px;

    h4 {
      font-size: 16px;
      font-weight: 600;
      color: var(--ion-color-dark);
      margin: 0 0 12px 0;
    }

    .balance-info {
      display: flex;
      flex-direction: column;
      padding: 12px;
      background: var(--ion-color-primary-tint);
      border-radius: 8px;

      .balance-amount {
        font-size: 18px;
        font-weight: 700;
        color: var(--ion-color-primary);
        margin-bottom: 4px;
      }

      .balance-address {
        font-size: 12px;
        color: var(--ion-color-medium);
        word-break: break-all;
      }
    }
  }

  .transactions-section {
    h4 {
      font-size: 16px;
      font-weight: 600;
      color: var(--ion-color-dark);
      margin: 0 0 12px 0;
    }

    .transactions-list {
      .transaction-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px;
        background: var(--ion-color-light-shade);
        border-radius: 8px;
        margin-bottom: 8px;

        &:last-child {
          margin-bottom: 0;
        }

        .transaction-info {
          display: flex;
          flex-direction: column;

          .transaction-amount {
            font-size: 14px;
            font-weight: 600;
            color: var(--ion-color-dark);
            margin-bottom: 2px;
          }

          .transaction-date {
            font-size: 12px;
            color: var(--ion-color-medium);
          }
        }

        .transaction-hash {
          font-size: 12px;
          color: var(--ion-color-medium);
          font-family: monospace;
        }
      }
    }
  }
}

.transaction-blob-card {
  ion-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .close-button {
      --color: var(--ion-color-medium);
    }
  }

  .transaction-info {
    .instruction-text {
      font-size: 16px;
      color: var(--ion-color-dark);
      margin: 0 0 20px 0;
      line-height: 1.5;
    }

    .transaction-details {
      margin-bottom: 20px;
      padding: 16px;
      background: var(--ion-color-light-shade);
      border-radius: 8px;

      .detail-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;

        &:last-child {
          margin-bottom: 0;
        }

        .label {
          font-size: 14px;
          color: var(--ion-color-medium);
          font-weight: 500;
        }

        .value {
          font-size: 14px;
          color: var(--ion-color-dark);
          font-weight: 600;
        }
      }
    }

    .transaction-blob-section {
      h4 {
        font-size: 16px;
        font-weight: 600;
        color: var(--ion-color-dark);
        margin: 0 0 12px 0;
      }

      .blob-container {
        padding: 12px;
        background: var(--ion-color-light-shade);
        border-radius: 8px;
        border: 1px solid var(--ion-color-medium);
        margin-bottom: 12px;

        .transaction-blob {
          font-family: monospace;
          font-size: 12px;
          color: var(--ion-color-dark);
          word-break: break-all;
          line-height: 1.4;
        }
      }

      .blob-actions {
        display: flex;
        gap: 12px;

        .copy-blob-button {
          flex: 1;
          --border-radius: 8px;
          font-weight: 500;
        }

        .xaman-button {
          flex: 1;
          --border-radius: 8px;
          font-weight: 500;
        }
      }
    }
  }
}
