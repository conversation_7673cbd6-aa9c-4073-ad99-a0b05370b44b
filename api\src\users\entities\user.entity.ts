import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';

export enum UserRole {
  USER = 'user',
  ADMIN = 'admin',
}

@Entity('users')
export class User {
  @ApiProperty({
    description: 'Unique identifier for the user',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @ApiProperty({
    description: 'Unique username for the user',
    example: 'johndoe',
    maxLength: 255,
  })
  @Column({ type: 'varchar', length: 255, unique: true })
  username!: string;

  @ApiProperty({
    description: 'Unique email address for the user',
    example: '<EMAIL>',
    maxLength: 255,
  })
  @Column({ type: 'varchar', length: 255, unique: true })
  email!: string;

  @ApiProperty({
    description: 'Hashed password for the user',
    example: 'hashed_password_string',
    maxLength: 255,
  })
  @Column({ type: 'varchar', length: 255 })
  password!: string;

  @ApiProperty({
    description: 'Role of the user in the system',
    enum: UserRole,
    example: UserRole.USER,
  })
  @Column({ type: 'enum', enum: UserRole, default: UserRole.USER })
  role!: UserRole;

  @ApiProperty({
    description: 'XRPL address linked to the user account',
    required: false,
    example: 'rXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX',
    maxLength: 255,
  })
  @Column({ type: 'varchar', length: 255, nullable: true })
  xrplAddress?: string;

  @ApiProperty({
    description: 'Date when the user account was created',
    example: '2025-01-01T00:00:00.000Z',
  })
  @CreateDateColumn()
  createdAt!: Date;

  @ApiProperty({
    description: 'Date when the user account was last updated',
    example: '2025-01-01T00:00:00.000Z',
  })
  @UpdateDateColumn()
  updatedAt!: Date;
}
