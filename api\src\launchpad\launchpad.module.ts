import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { LaunchpadController } from './launchpad.controller';
import { LaunchpadService } from './launchpad.service';
import { LaunchpadGateway } from './launchpad.gateway';
import { Sale } from './entities/sale.entity';
import { Contribution } from './entities/contribution.entity';
import { XrplModule } from '../xrpl/xrpl.module';
import { UsersModule } from '../users/users.module';
import { AuthModule } from '../auth/auth.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Sale, Contribution]),
    XrplModule,
    UsersModule,
    AuthModule,
  ],
  controllers: [LaunchpadController],
  providers: [LaunchpadService, LaunchpadGateway],
  exports: [LaunchpadService, LaunchpadGateway],
})
export class LaunchpadModule {}
