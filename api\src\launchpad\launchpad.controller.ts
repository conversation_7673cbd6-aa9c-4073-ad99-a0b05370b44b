import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  UseGuards,
  Request,
  ParseUUIDPipe,
  Query,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
  ApiBody,
  ApiParam,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { OptionalJwtAuthGuard } from '../auth/guards/optional-jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { Public } from '../auth/decorators/public.decorator';
import { UserRole } from '../users/entities/user.entity';
import { LaunchpadService } from './launchpad.service';
import { CreateSaleDto } from './dto/create-sale.dto';
import { UpdateSaleDto } from './dto/update-sale.dto';
import { CreateContributionDto } from './dto/create-contribution.dto';
import { UserAllocationDto } from './dto/allocation.dto';
import { ClaimResponseDto } from './dto/claim-response.dto';
import { SaleStatus } from './entities/sale.entity';

@ApiTags('Launchpad')
@Controller('launchpad')
export class LaunchpadController {
  constructor(private readonly launchpadService: LaunchpadService) {}

  // Public endpoints
  @Get('sales')
  @Public()
  @UseGuards(OptionalJwtAuthGuard)
  @ApiOperation({
    summary: 'Get all token sales',
    description:
      'Retrieve a list of all token sales with optional status filtering',
  })
  @ApiQuery({
    name: 'status',
    required: false,
    enum: SaleStatus,
    description: 'Filter sales by status',
  })
  @ApiResponse({
    status: 200,
    description: 'Sales retrieved successfully',
    schema: {
      type: 'array',
      items: {
        $ref: '#/components/schemas/Sale',
      },
    },
  })
  async getAllSales(@Query('status') status?: SaleStatus) {
    return this.launchpadService.findAllSales(status);
  }

  @Get('sales/active')
  @Public()
  @UseGuards(OptionalJwtAuthGuard)
  @ApiOperation({
    summary: 'Get active token sales',
    description: 'Retrieve a list of currently active token sales',
  })
  @ApiResponse({
    status: 200,
    description: 'Active sales retrieved successfully',
    schema: {
      type: 'array',
      items: {
        $ref: '#/components/schemas/Sale',
      },
    },
  })
  async getActiveSales() {
    return this.launchpadService.findActiveSales();
  }

  @Get('sales/:id')
  @Public()
  @UseGuards(OptionalJwtAuthGuard)
  @ApiOperation({
    summary: 'Get sale by ID',
    description: 'Retrieve a specific token sale by its unique identifier',
  })
  @ApiParam({
    name: 'id',
    description: 'Unique identifier of the token sale',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiResponse({
    status: 200,
    description: 'Sale retrieved successfully',
    schema: {
      $ref: '#/components/schemas/Sale',
    },
  })
  @ApiResponse({
    status: 404,
    description: 'Sale not found',
  })
  async getSaleById(@Param('id', ParseUUIDPipe) id: string) {
    return this.launchpadService.findSaleById(id);
  }

  @Get('sales/:id/contributions')
  @Public()
  @UseGuards(OptionalJwtAuthGuard)
  @ApiOperation({
    summary: 'Get sale contributions',
    description: 'Retrieve all contributions for a specific token sale',
  })
  @ApiParam({
    name: 'id',
    description: 'Unique identifier of the token sale',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiResponse({
    status: 200,
    description: 'Contributions retrieved successfully',
    schema: {
      type: 'array',
      items: {
        $ref: '#/components/schemas/Contribution',
      },
    },
  })
  @ApiResponse({
    status: 404,
    description: 'Sale not found',
  })
  async getSaleContributions(@Param('id', ParseUUIDPipe) id: string) {
    return this.launchpadService.findSaleContributions(id);
  }

  @Get('sales/:id/stats')
  @Public()
  @UseGuards(OptionalJwtAuthGuard)
  @ApiOperation({
    summary: 'Get sale statistics',
    description: 'Retrieve comprehensive statistics for a specific token sale',
  })
  @ApiParam({
    name: 'id',
    description: 'Unique identifier of the token sale',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiResponse({
    status: 200,
    description: 'Sale statistics retrieved successfully',
  })
  @ApiResponse({
    status: 404,
    description: 'Sale not found',
  })
  async getSaleStats(@Param('id', ParseUUIDPipe) id: string) {
    return this.launchpadService.getSaleStats(id);
  }

  // Admin-only endpoints
  @Post('sales')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Create new token sale',
    description: 'Create a new token sale (Admin only)',
  })
  @ApiBody({
    type: CreateSaleDto,
    description: 'Token sale creation data',
  })
  @ApiResponse({
    status: 201,
    description: 'Token sale created successfully',
    schema: {
      $ref: '#/components/schemas/Sale',
    },
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Admin access required',
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid sale data or validation failed',
  })
  async createSale(@Body() createSaleDto: CreateSaleDto) {
    return this.launchpadService.createSale(createSaleDto);
  }

  @Put('sales/:id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Update token sale',
    description: 'Update an existing token sale (Admin only)',
  })
  @ApiParam({
    name: 'id',
    description: 'Unique identifier of the token sale',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiBody({
    type: UpdateSaleDto,
    description: 'Updated sale data',
  })
  @ApiResponse({
    status: 200,
    description: 'Sale updated successfully',
    schema: {
      $ref: '#/components/schemas/Sale',
    },
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Admin access required',
  })
  @ApiResponse({
    status: 404,
    description: 'Sale not found',
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid update data or validation failed',
  })
  async updateSale(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateSaleDto: UpdateSaleDto,
  ) {
    return this.launchpadService.updateSale(id, updateSaleDto);
  }

  @Delete('sales/:id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Delete token sale',
    description: 'Permanently delete a token sale (Admin only)',
  })
  @ApiParam({
    name: 'id',
    description: 'Unique identifier of the token sale',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiResponse({
    status: 200,
    description: 'Sale deleted successfully',
    schema: {
      type: 'object',
      properties: {
        message: {
          type: 'string',
          example: 'Sale deleted successfully',
        },
      },
    },
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Admin access required',
  })
  @ApiResponse({
    status: 404,
    description: 'Sale not found',
  })
  async deleteSale(@Param('id', ParseUUIDPipe) id: string) {
    await this.launchpadService.deleteSale(id);
    return { message: 'Sale deleted successfully' };
  }

  @Post('sales/:id/end')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'End token sale',
    description: 'End a token sale and compute final allocations (Admin only)',
  })
  @ApiParam({
    name: 'id',
    description: 'Unique identifier of the token sale',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiResponse({
    status: 200,
    description: 'Sale ended successfully and allocations computed',
    schema: {
      $ref: '#/components/schemas/Sale',
    },
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Admin access required',
  })
  @ApiResponse({
    status: 404,
    description: 'Sale not found',
  })
  @ApiResponse({
    status: 400,
    description:
      'Sale cannot be ended (already ended, canceled, or not active)',
  })
  async endSale(@Param('id', ParseUUIDPipe) id: string) {
    return this.launchpadService.endSale(id);
  }

  @Post('sales/:id/cancel')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles(UserRole.ADMIN)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Cancel token sale',
    description: 'Cancel a token sale and refund contributions (Admin only)',
  })
  @ApiParam({
    name: 'id',
    description: 'Unique identifier of the token sale',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiResponse({
    status: 200,
    description: 'Sale canceled successfully',
    schema: {
      $ref: '#/components/schemas/Sale',
    },
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Admin access required',
  })
  @ApiResponse({
    status: 404,
    description: 'Sale not found',
  })
  @ApiResponse({
    status: 400,
    description: 'Sale cannot be canceled (already ended or canceled)',
  })
  async cancelSale(@Param('id', ParseUUIDPipe) id: string) {
    return this.launchpadService.cancelSale(id);
  }

  // User contribution endpoints
  @Post('contributions')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Create contribution',
    description: 'Create a new contribution to a token sale',
  })
  @ApiBody({
    type: CreateContributionDto,
    description: 'Contribution data including sale ID and amount',
  })
  @ApiResponse({
    status: 201,
    description: 'Contribution created successfully',
    schema: {
      $ref: '#/components/schemas/Contribution',
    },
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Authentication required',
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid contribution data or sale not found',
  })
  async createContribution(
    @Request() req: any,
    @Body() createContributionDto: CreateContributionDto,
  ) {
    return this.launchpadService.createContribution(
      req.user.id,
      createContributionDto,
    );
  }

  @Get('contributions/my')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Get my contributions',
    description: 'Retrieve all contributions made by the authenticated user',
  })
  @ApiResponse({
    status: 200,
    description: 'User contributions retrieved successfully',
    schema: {
      type: 'array',
      items: {
        $ref: '#/components/schemas/Contribution',
      },
    },
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Authentication required',
  })
  async getMyContributions(@Request() req: any) {
    return this.launchpadService.findUserContributions(req.user.id);
  }

  // User allocation and claim endpoints
  @Get('sales/:id/allocation')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Get user allocation',
    description:
      'Retrieve the token allocation for the authenticated user in a specific sale',
  })
  @ApiParam({
    name: 'id',
    description: 'Unique identifier of the token sale',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiResponse({
    status: 200,
    description: 'Allocation retrieved successfully',
    schema: {
      $ref: '#/components/schemas/UserAllocationDto',
    },
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Authentication required',
  })
  @ApiResponse({
    status: 404,
    description: 'Sale not found',
  })
  async getUserAllocation(
    @Request() req: any,
    @Param('id', ParseUUIDPipe) id: string,
  ): Promise<UserAllocationDto> {
    return await this.launchpadService.getUserAllocation(req.user.id, id);
  }

  @Post('sales/:id/claim')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Claim tokens',
    description:
      'Claim allocated tokens for the authenticated user in a completed sale',
  })
  @ApiParam({
    name: 'id',
    description: 'Unique identifier of the token sale',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiResponse({
    status: 200,
    description: 'Tokens claimed successfully',
    schema: {
      $ref: '#/components/schemas/ClaimResponseDto',
    },
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Authentication required',
  })
  @ApiResponse({
    status: 400,
    description: 'Sale not completed or user has no allocation',
  })
  @ApiResponse({
    status: 404,
    description: 'Sale not found',
  })
  async claimTokens(
    @Request() req: any,
    @Param('id', ParseUUIDPipe) id: string,
  ): Promise<ClaimResponseDto> {
    return await this.launchpadService.claimTokens(req.user.id, id);
  }
}
