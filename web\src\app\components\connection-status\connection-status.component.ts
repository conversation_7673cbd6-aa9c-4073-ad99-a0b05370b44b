import { Component, Input, ChangeDetectionStrategy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';

/**
 * Component that displays the WebSocket connection status.
 *
 * Features:
 * - Shows real-time connection status with visual indicators
 * - Displays appropriate icons and colors for connected/disconnected states
 * - Accessible with proper ARIA labels
 *
 * @example
 * ```html
 * <app-connection-status [isConnected]="isConnected()"></app-connection-status>
 * ```
 */
@Component({
  selector: 'app-connection-status',
  templateUrl: './connection-status.component.html',
  styleUrls: ['./connection-status.component.scss'],
  standalone: true,
  imports: [CommonModule, IonicModule],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ConnectionStatusComponent {
  @Input() isConnected: boolean = false;
}
