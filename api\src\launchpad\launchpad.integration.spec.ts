import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { LaunchpadService } from './launchpad.service';
import { Sale, SaleStatus } from './entities/sale.entity';
import { Contribution } from './entities/contribution.entity';
import { XrplService } from '../xrpl/xrpl.service';
import { UsersService } from '../users/users.service';
import { CreateSaleDto } from './dto/create-sale.dto';
import { BadRequestException, NotFoundException } from '@nestjs/common';
import { User, UserRole } from '../users/entities/user.entity';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule } from '@nestjs/config';
import { testDatabaseConfig } from '../../test/test-database.config';

describe('Launchpad Service Integration Tests', () => {
  let service: LaunchpadService;
  let saleRepository: Repository<Sale>;
  let contributionRepository: Repository<Contribution>;
  let xrplService: XrplService;
  let module: TestingModule;

  // Use fixed dates for consistent testing
  const yesterday = new Date(Date.now() - 86400000);
  const tomorrow = new Date(Date.now() + 86400000);
  const dayAfterTomorrow = new Date(Date.now() + 172800000);

  const mockUser: User = {
    id: '123e4567-e89b-12d3-a456-426614174010',
    email: '<EMAIL>',
    username: 'testuser',
    xrplAddress: 'rTestUserAddress123456789',
    password: 'hashedPassword',
    role: UserRole.USER,
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockSale: Sale = {
    id: '123e4567-e89b-12d3-a456-426614174000',
    symbol: 'TEST',
    description: 'Test sale description',
    price: 0.1,
    softCap: 1000,
    hardCap: 5000,
    start: yesterday,
    end: dayAfterTomorrow,
    status: SaleStatus.ACTIVE,
    collectionAddress: 'rTestCollectionAddress123456789',
    createdAt: new Date(),
    updatedAt: new Date(),
    contributions: [],
  } as Sale;

  const mockContribution: Contribution = {
    id: '123e4567-e89b-12d3-a456-426614174001',
    userId: '123e4567-e89b-12d3-a456-426614174002',
    saleId: '123e4567-e89b-12d3-a456-426614174030',
    amount: 500,
    txHash: 'txHash123',
    status: 'pending',
    createdAt: new Date(),
    updatedAt: new Date(),
    user: mockUser,
    sale: mockSale,
  } as Contribution;

  beforeAll(async () => {
    module = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({
          isGlobal: true,
          load: [
            () => ({
              JWT_SECRET: 'test-secret-key-for-testing-only',
              JWT_EXPIRES_IN: '1h',
              REFRESH_TOKEN_EXPIRES_IN: '7d',
            }),
          ],
        }),
        TypeOrmModule.forRoot({
          ...testDatabaseConfig,
          entities: [Sale, Contribution, User],
        }),
        TypeOrmModule.forFeature([Sale, Contribution, User]),
      ],
      providers: [
        LaunchpadService,
        UsersService,
        {
          provide: XrplService,
          useValue: {
            isConnected: jest.fn(),
            getConnectionStatus: jest.fn(),
            request: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<LaunchpadService>(LaunchpadService);
    saleRepository = module.get<Repository<Sale>>(getRepositoryToken(Sale));
    contributionRepository = module.get<Repository<Contribution>>(
      getRepositoryToken(Contribution),
    );
    xrplService = module.get<XrplService>(XrplService);

    // Mock XRPL service methods
    jest.spyOn(xrplService, 'isConnected').mockReturnValue(true);
    jest.spyOn(xrplService, 'getConnectionStatus').mockReturnValue({
      isConnected: true,
      connectionAttempts: 0,
      reconnectDelay: 1000,
    });
    jest.spyOn(xrplService, 'request').mockResolvedValue({
      result: {
        account_data: {
          Balance: '**********',
          Sequence: 123,
        },
      },
    });
  });

  afterAll(async () => {
    await module?.close();
  });

  beforeEach(async () => {
    try {
      // Clear database before each test
      await contributionRepository.clear();
      await saleRepository.clear();

      // Clear users as well
      const userRepository = module.get<Repository<User>>(
        getRepositoryToken(User),
      );
      await userRepository.clear();
    } catch (error) {
      console.warn('Database cleanup failed:', error);
    }

    // Reset mocks
    jest.clearAllMocks();
  });

  describe('Sale Management', () => {
    it('should create a new sale', async () => {
      // Ensure user exists in database
      const userRepository = module.get<Repository<User>>(
        getRepositoryToken(User),
      );
      await userRepository.save(mockUser);

      const createSaleDto: CreateSaleDto = {
        symbol: 'TEST',
        description: 'A sale for integration testing',
        price: 0.5,
        softCap: 1000,
        hardCap: 2000,
        start: tomorrow.toISOString(),
        end: dayAfterTomorrow.toISOString(),
        collectionAddress: 'rTestCollectionAddress123456789',
      };

      const result = await service.createSale(createSaleDto);

      expect(result).toBeDefined();
      expect(result.symbol).toBe(createSaleDto.symbol);
      expect(result.status).toBe(SaleStatus.ACTIVE);
      expect(result.symbol).toBe('TEST');
    });

    it('should validate sale dates', async () => {
      // Ensure user exists in database
      const userRepository = module.get<Repository<User>>(
        getRepositoryToken(User),
      );
      await userRepository.save(mockUser);

      const createSaleDto: CreateSaleDto = {
        symbol: 'INVALID',
        description: 'A sale with invalid dates',
        price: 0.5,
        softCap: 1000,
        hardCap: 2000,
        start: new Date(Date.now() - 86400000).toISOString(), // Past date
        end: tomorrow.toISOString(),
        collectionAddress: 'rTestCollectionAddress123456789',
      };

      await expect(service.createSale(createSaleDto)).rejects.toThrow(
        BadRequestException,
      );
    });

    it('should update sale', async () => {
      // Create a sale first
      const sale = await saleRepository.save(mockSale);

      // Ensure user exists in database
      const userRepository = module.get<Repository<User>>(
        getRepositoryToken(User),
      );
      await userRepository.save(mockUser);

      const updateResult = await service.updateSale(sale.id, {
        description: 'Updated description',
      });

      expect(updateResult.description).toBe('Updated description');
    });
  });

  describe('Contribution Management', () => {
    it('should create a contribution', async () => {
      // Create a sale first
      const sale = await saleRepository.save(mockSale);

      // Ensure user exists in database
      const userRepository = module.get<Repository<User>>(
        getRepositoryToken(User),
      );
      await userRepository.save(mockUser);

      const contribution = await service.createContribution(mockUser.id, {
        saleId: sale.id,
        amount: 100,
        txHash: 'txHash456',
      });

      expect(contribution).toBeDefined();
      expect(contribution.saleId).toBe(sale.id);
      expect(contribution.userId).toBe(mockUser.id);
      expect(contribution.amount).toBe(100);
    });

    it('should validate contribution amount', async () => {
      const sale = await saleRepository.save(mockSale);

      // Ensure user exists in database
      const userRepository = module.get<Repository<User>>(
        getRepositoryToken(User),
      );
      await userRepository.save(mockUser);

      await expect(
        service.createContribution(mockUser.id, {
          saleId: sale.id,
          amount: -100, // Negative amount
          txHash: 'txHash789',
        }),
      ).rejects.toThrow(BadRequestException);
    });

    it('should get user contributions', async () => {
      // Ensure user exists in database
      const userRepository = module.get<Repository<User>>(
        getRepositoryToken(User),
      );
      await userRepository.save(mockUser);

      await contributionRepository.save(mockContribution);

      const contributions = await service.findUserContributions(mockUser.id);

      expect(contributions).toBeDefined();
      expect(contributions.length).toBeGreaterThan(0);
      expect(contributions[0].userId).toBe(mockUser.id);
    });
  });

  describe('XRPL Integration', () => {
    it('should monitor XRPL transactions for contributions', () => {
      // The service should be able to process XRPL events
      expect(service).toBeDefined();
      expect(typeof service.processContributionEvent).toBe('function');
    });

    it('should handle XRPL connection issues gracefully', async () => {
      // Mock XRPL service as disconnected
      jest.spyOn(xrplService, 'isConnected').mockReturnValue(false);

      const sale = await saleRepository.save(mockSale);

      // Ensure user exists in database
      const userRepository = module.get<Repository<User>>(
        getRepositoryToken(User),
      );
      await userRepository.save(mockUser);

      // Should still be able to create contributions even if XRPL is down
      const contribution = await service.createContribution(mockUser.id, {
        saleId: sale.id,
        amount: 100,
        txHash: 'txHashOffline',
      });

      expect(contribution).toBeDefined();
      expect(contribution.status).toBe('pending');
    });
  });

  describe('Sale Analytics', () => {
    it('should calculate sale stats', async () => {
      const sale = await saleRepository.save(mockSale);

      // Ensure user exists in database
      const userRepository = module.get<Repository<User>>(
        getRepositoryToken(User),
      );
      await userRepository.save(mockUser);

      // Add some contributions
      await contributionRepository.save([
        { ...mockContribution, amount: 300, saleId: sale.id },
        {
          ...mockContribution,
          amount: 200,
          saleId: sale.id,
          id: '123e4567-e89b-12d3-a456-426614174002',
        },
      ]);

      const stats = await service.getSaleStats(sale.id);

      expect(stats).toBeDefined();
      expect(stats.totalAmount).toBe(500);
      expect(stats.contributorCount).toBe(1); // Same user, different contributions
    });

    it('should handle sales with no contributions', async () => {
      const sale = await saleRepository.save(mockSale);

      // Ensure user exists in database
      const userRepository = module.get<Repository<User>>(
        getRepositoryToken(User),
      );
      await userRepository.save(mockUser);

      const stats = await service.getSaleStats(sale.id);

      expect(stats.totalAmount).toBe(0);
      expect(stats.contributorCount).toBe(0);
    });
  });

  describe('Error Handling', () => {
    it('should handle non-existent sales gracefully', async () => {
      await expect(
        service.findSaleById('123e4567-e89b-12d3-a456-426614174999'),
      ).rejects.toThrow(NotFoundException);
    });

    it('should validate sale data integrity', async () => {
      // Ensure user exists in database
      const userRepository = module.get<Repository<User>>(
        getRepositoryToken(User),
      );
      await userRepository.save(mockUser);

      const invalidSale: CreateSaleDto = {
        ...mockSale,
        start: mockSale.start.toISOString(),
        end: mockSale.end.toISOString(),
        softCap: 2000,
        hardCap: 1000, // softCap > hardCap
      };

      await expect(service.createSale(invalidSale)).rejects.toThrow(
        BadRequestException,
      );
    });
  });
});
