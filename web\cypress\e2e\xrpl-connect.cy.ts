/// <reference types="cypress" />

describe('XRPL Connect Flow', () => {
  beforeEach(() => {
    // Clear localStorage and cookies before each test
    cy.clearLocalStorage();
    cy.clearCookies();

    // Mock authenticated user without XRPL address
    cy.window().then(win => {
      win.localStorage.setItem('access_token', 'mock-access-token');
      win.localStorage.setItem(
        'user',
        JSON.stringify({
          id: '1',
          username: 'testuser',
          email: '<EMAIL>',
          role: 'user',
          xrplAddress: null,
        })
      );
    });
  });

  it('should display XRPL connect page correctly', () => {
    cy.visit('/xrpl-connect');

    // Check that the form is visible
    cy.get('ion-content').should('be.visible');
    cy.getIonInput('xrplAddress').should('be.visible');
    cy.getIonButton('submit').should('be.visible');

    // Check that the form has proper labels/placeholders
    cy.getIonInput('xrplAddress').should('have.attr', 'placeholder');
  });

  it('should show validation errors for empty form', () => {
    cy.visit('/xrpl-connect');

    // Try to submit empty form
    cy.getIonButton('submit').click();

    // Check that form validation prevents submission
    cy.getIonInput('xrplAddress').should('have.attr', 'aria-invalid', 'true');
  });

  it('should show validation error for invalid XRPL address format', () => {
    cy.visit('/xrpl-connect');

    // Test various invalid formats
    const invalidAddresses = [
      'invalid-address',
      'rShort',
      'notStartingWithR123456789012345678901234',
      'rInvalidChars!@#',
      'rTooLongAddress123456789012345678901234567890',
    ];

    invalidAddresses.forEach(address => {
      cy.getIonInput('xrplAddress').clear().type(address);
      cy.getIonButton('submit').click();

      // Check that validation error is shown
      cy.getIonInput('xrplAddress').should('have.attr', 'aria-invalid', 'true');
    });
  });

  it('should accept valid XRPL address format', () => {
    cy.visit('/xrpl-connect');

    const validAddress = 'rValidTestnetAddress123456789';
    cy.getIonInput('xrplAddress').type(validAddress);

    // Check that form is valid
    cy.getIonInput('xrplAddress').should(
      'not.have.attr',
      'aria-invalid',
      'true'
    );
  });

  it('should handle successful XRPL address linking', () => {
    // Mock successful XRPL linking response
    cy.intercept('POST', '**/users/xrpl/link', {
      statusCode: 200,
      body: {
        success: true,
        message: 'XRPL address successfully linked',
      },
    }).as('linkRequest');

    cy.visit('/xrpl-connect');

    cy.getIonInput('xrplAddress').type('rValidTestnetAddress123456789');
    cy.getIonButton('submit').click();

    // Wait for link request
    cy.wait('@linkRequest');

    // Check that success toast is shown
    cy.getIonToast().should('be.visible');
    cy.getIonToast().should('contain', 'Address linked');

    // Check that user is redirected to dashboard
    cy.url().should('include', '/dashboard');
    cy.waitForIonicPage();
  });

  it('should handle XRPL address linking failure', () => {
    // Mock failed XRPL linking response
    cy.intercept('POST', '**/users/xrpl/link', {
      statusCode: 409,
      body: {
        message: 'Address already linked to another account',
      },
    }).as('linkRequest');

    cy.visit('/xrpl-connect');

    cy.getIonInput('xrplAddress').type('rValidTestnetAddress123456789');
    cy.getIonButton('submit').click();

    // Wait for link request
    cy.wait('@linkRequest');

    // Check that error toast is shown
    cy.getIonToast().should('be.visible');
    cy.getIonToast().should('contain', 'Address already linked');

    // Check that user stays on XRPL connect page
    cy.url().should('include', '/xrpl-connect');
  });

  it('should handle network error during XRPL linking', () => {
    // Mock network error
    cy.intercept('POST', '**/users/xrpl/link', {
      forceNetworkError: true,
    }).as('linkRequest');

    cy.visit('/xrpl-connect');

    cy.getIonInput('xrplAddress').type('rValidTestnetAddress123456789');
    cy.getIonButton('submit').click();

    // Wait for link request
    cy.wait('@linkRequest');

    // Check that error toast is shown
    cy.getIonToast().should('be.visible');
    cy.getIonToast().should('contain', 'Server error');

    // Check that user stays on XRPL connect page
    cy.url().should('include', '/xrpl-connect');
  });

  it('should disable form when user already has linked address', () => {
    // Mock user with already linked XRPL address
    cy.window().then(win => {
      win.localStorage.setItem(
        'user',
        JSON.stringify({
          id: '1',
          username: 'testuser',
          email: '<EMAIL>',
          role: 'user',
          xrplAddress: 'rAlreadyLinkedAddress123456789',
        })
      );
    });

    cy.visit('/xrpl-connect');

    // Check that form is disabled
    cy.getIonInput('xrplAddress').should('be.disabled');
    cy.getIonButton('submit').should('be.disabled');

    // Check that there's a message indicating address is already linked
    cy.get('ion-content').should('contain', 'already linked');
  });

  it('should navigate to dashboard when go to dashboard is clicked', () => {
    cy.visit('/xrpl-connect');

    // Click on "Go to Dashboard" button (if it exists)
    cy.getIonButton().contains('Go to Dashboard').click();

    // Check that user is redirected to dashboard
    cy.url().should('include', '/dashboard');
    cy.waitForIonicPage();
  });

  it('should show loading state during submission', () => {
    // Mock slow response
    cy.intercept('POST', '**/users/xrpl/link', {
      delay: 2000,
      statusCode: 200,
      body: {
        success: true,
        message: 'XRPL address successfully linked',
      },
    }).as('linkRequest');

    cy.visit('/xrpl-connect');

    cy.getIonInput('xrplAddress').type('rValidTestnetAddress123456789');
    cy.getIonButton('submit').click();

    // Check that loading state is shown
    cy.getIonSpinner().should('be.visible');
    cy.getIonButton('submit').should('be.disabled');

    // Wait for request to complete
    cy.wait('@linkRequest');
  });

  it('should redirect to XRPL connect from dashboard if no address linked', () => {
    // Mock user without XRPL address
    cy.window().then(win => {
      win.localStorage.setItem(
        'user',
        JSON.stringify({
          id: '1',
          username: 'testuser',
          email: '<EMAIL>',
          role: 'user',
          xrplAddress: null,
        })
      );
    });

    // Mock dashboard data
    cy.intercept('GET', '**/xrpl/balance', {
      statusCode: 200,
      body: { balance: 1000, currency: 'XRP' },
    });
    cy.intercept('GET', '**/xrpl/transactions', { statusCode: 200, body: [] });
    cy.intercept('GET', '**/launchpad/sales/active', {
      statusCode: 200,
      body: [],
    });

    cy.visit('/dashboard');

    // Check that user is redirected to XRPL connect
    cy.url().should('include', '/xrpl-connect');
  });
});
