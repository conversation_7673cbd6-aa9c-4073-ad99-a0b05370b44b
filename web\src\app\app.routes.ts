import { Routes } from '@angular/router';
import { JwtAuthGuard } from './guards/jwt-auth.guard';
import { AdminRoleGuard } from './guards/admin-role.guard';

export const routes: Routes = [
  {
    path: '',
    redirectTo: 'login',
    pathMatch: 'full',
  },
  {
    path: 'register',
    loadComponent: () =>
      import('./pages/register/register.page').then(m => m.RegisterPage),
  },
  {
    path: 'login',
    loadComponent: () =>
      import('./pages/login/login.page').then(m => m.LoginPage),
  },
  {
    path: 'dashboard',
    canActivate: [JwtAuthGuard],
    loadComponent: () =>
      import('./pages/dashboard/dashboard.page').then(m => m.DashboardPage),
  },
  {
    path: 'launchpad',
    children: [
      {
        path: '',
        loadComponent: () =>
          import('./pages/launchpad-list/launchpad-list.page').then(
            m => m.LaunchpadListPage
          ),
      },
      {
        path: 'detail/:id',
        loadComponent: () =>
          import('./pages/launchpad-detail/launchpad-detail.page').then(
            m => m.LaunchpadDetailPage
          ),
      },
    ],
  },
  {
    path: 'xrpl-connect',
    canActivate: [JwtAuthGuard],
    loadComponent: () =>
      import('./pages/xrpl-connect/xrpl-connect.page').then(
        m => m.XrplConnectPage
      ),
  },
  {
    path: 'admin',
    canActivate: [AdminRoleGuard],
    children: [
      {
        path: 'sales',
        loadComponent: () =>
          import('./pages/admin/admin-sales.page').then(m => m.AdminSalesPage),
      },
    ],
  },
];
