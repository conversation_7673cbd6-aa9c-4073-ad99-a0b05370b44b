ion-card {
  margin-bottom: 16px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  ion-card-header {
    padding: 16px 16px 8px 16px;

    ion-card-title {
      display: flex;
      align-items: center;
      font-size: 18px;
      font-weight: 600;
      color: var(--ion-color-dark);

      ion-icon {
        margin-right: 8px;
        font-size: 20px;
      }
    }
  }

  ion-card-content {
    padding: 8px 16px 16px 16px;
  }
}

ion-list {
  padding: 0;
}

ion-item {
  --padding-start: 0;
  --padding-end: 0;
  --inner-padding-end: 0;
  margin-bottom: 8px;
  border-radius: 8px;

  &:last-child {
    margin-bottom: 0;
  }

  ion-label {
    h3 {
      font-size: 16px;
      font-weight: 600;
      color: var(--ion-color-dark);
      margin: 0 0 4px 0;
    }

    p {
      font-size: 14px;
      color: var(--ion-color-medium);
      margin: 0 0 2px 0;

      &.sale-price {
        font-weight: 600;
        color: var(--ion-color-primary);
        margin-bottom: 4px;
      }

      &.sale-dates {
        font-size: 12px;
        color: var(--ion-color-medium-shade);
      }
    }
  }

  ion-icon {
    font-size: 20px;
    margin-right: 12px;
  }
}

.sale-item {
  .sale-progress {
    margin: 8px 0;

    ion-progress-bar {
      margin-bottom: 4px;
    }

    .progress-text {
      font-size: 12px;
      color: var(--ion-color-medium);
      font-weight: 500;
    }
  }
}

.empty-state {
  text-align: center;
  padding: 32px 16px;
  color: var(--ion-color-medium);

  ion-icon {
    margin-bottom: 16px;
    opacity: 0.5;
  }

  p {
    margin: 0;
    font-size: 16px;
  }
}

ion-badge {
  font-size: 12px;
  font-weight: 500;
}

ion-button[fill='clear'] {
  --color: var(--ion-color-primary);
  font-size: 14px;
  font-weight: 500;
  margin: 0;
  --padding-start: 8px;
  --padding-end: 8px;
}
