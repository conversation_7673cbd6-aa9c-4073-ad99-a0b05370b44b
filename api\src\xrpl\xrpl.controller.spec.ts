import { Test, TestingModule } from '@nestjs/testing';
import { XrplController } from './xrpl.controller';
import { XrplService } from './xrpl.service';
import { User } from '../users/entities/user.entity';
import { BalanceDto } from './dto/balance.dto';
import { TransactionDto } from './dto/transaction.dto';
import { PreparedTransactionDto } from './dto/prepared-transaction.dto';
import { SendTransactionDto } from './dto/send-transaction.dto';
import { HttpException, HttpStatus } from '@nestjs/common';

describe('XrplController', () => {
  let controller: XrplController;
  let xrplService: jest.Mocked<XrplService>;

  const mockUser: User = {
    id: 'user123',
    email: '<EMAIL>',
    username: 'testuser',
    xrplAddress: 'rTestAddress123456789',
    password: 'hashedPassword',
    createdAt: new Date(),
    updatedAt: new Date(),
  } as User;

  const mockUserWithoutXrpl: User = {
    ...mockUser,
    xrplAddress: '',
  };

  const mockBalanceDto: BalanceDto = {
    address: 'rTestAddress123456789',
    xrp: 1000,
    currency: 'XRP',
    ledgerIndex: 12345,
    validated: true,
  };

  const mockTransactionDto: TransactionDto = {
    hash: 'txHash123',
    account: 'rTestAddress123456789',
    destination: 'rDestAddress123456789',
    amount: 1.5,
    currency: 'XRP',
    fee: 0.01,
    ledgerIndex: 12345,
    date: new Date(),
    type: 'Payment',
    memo: 'Test transaction',
    validated: true,
  };

  const mockPreparedTransactionDto: PreparedTransactionDto = {
    transactionBlob: 'preparedTransactionBlob',
    account: 'rTestAddress123456789',
    destination: 'rDestAddress123456789',
    amount: 1.5,
    fee: 0.00001,
    sequence: 123,
    lastLedgerSequence: 1004,
    memo: 'Test memo',
    message: 'Transaction prepared successfully',
  };

  beforeEach(async () => {
    const mockXrplService = {
      getBalance: jest.fn(),
      getTransactions: jest.fn(),
      prepareSendTransaction: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [XrplController],
      providers: [
        {
          provide: XrplService,
          useValue: mockXrplService,
        },
      ],
    }).compile();

    controller = module.get<XrplController>(XrplController);
    xrplService = module.get(XrplService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getBalance', () => {
    it('should return balance successfully', async () => {
      xrplService.getBalance.mockResolvedValue(mockBalanceDto);

      const result = await controller.getBalance(mockUser);

      expect(result).toEqual(mockBalanceDto);
      expect(xrplService.getBalance).toHaveBeenCalledWith(mockUser.xrplAddress);
    });

    it('should throw error when user has no XRPL address', async () => {
      await expect(controller.getBalance(mockUserWithoutXrpl)).rejects.toThrow(
        new HttpException(
          'No XRPL address linked to this account',
          HttpStatus.BAD_REQUEST,
        ),
      );

      expect(xrplService.getBalance).not.toHaveBeenCalled();
    });

    it('should handle XRPL service errors', async () => {
      const errorMessage = 'XRPL service error';
      xrplService.getBalance.mockRejectedValue(new Error(errorMessage));

      await expect(controller.getBalance(mockUser)).rejects.toThrow(
        new HttpException(
          `Failed to fetch balance: ${errorMessage}`,
          HttpStatus.INTERNAL_SERVER_ERROR,
        ),
      );
    });

    it('should handle unknown errors', async () => {
      xrplService.getBalance.mockRejectedValue('Unknown error');

      await expect(controller.getBalance(mockUser)).rejects.toThrow(
        new HttpException(
          'Failed to fetch balance: Unknown error',
          HttpStatus.INTERNAL_SERVER_ERROR,
        ),
      );
    });
  });

  describe('getTransactions', () => {
    it('should return transactions successfully', async () => {
      xrplService.getTransactions.mockResolvedValue([mockTransactionDto]);

      const result = await controller.getTransactions(mockUser);

      expect(result).toEqual([mockTransactionDto]);
      expect(xrplService.getTransactions).toHaveBeenCalledWith(
        mockUser.xrplAddress,
        10,
      );
    });

    it('should throw error when user has no XRPL address', async () => {
      await expect(
        controller.getTransactions(mockUserWithoutXrpl),
      ).rejects.toThrow(
        new HttpException(
          'No XRPL address linked to this account',
          HttpStatus.BAD_REQUEST,
        ),
      );

      expect(xrplService.getTransactions).not.toHaveBeenCalled();
    });

    it('should handle XRPL service errors', async () => {
      const errorMessage = 'XRPL service error';
      xrplService.getTransactions.mockRejectedValue(new Error(errorMessage));

      await expect(controller.getTransactions(mockUser)).rejects.toThrow(
        new HttpException(
          `Failed to fetch transactions: ${errorMessage}`,
          HttpStatus.INTERNAL_SERVER_ERROR,
        ),
      );
    });

    it('should handle unknown errors', async () => {
      xrplService.getTransactions.mockRejectedValue('Unknown error');

      await expect(controller.getTransactions(mockUser)).rejects.toThrow(
        new HttpException(
          'Failed to fetch transactions: Unknown error',
          HttpStatus.INTERNAL_SERVER_ERROR,
        ),
      );
    });
  });

  describe('prepareSendTransaction', () => {
    const sendDto: SendTransactionDto = {
      destination: 'rDestAddress123456789',
      amount: 1.5,
    };

    it('should prepare transaction successfully', async () => {
      xrplService.prepareSendTransaction.mockResolvedValue(
        mockPreparedTransactionDto,
      );

      const result = await controller.prepareSendTransaction(mockUser, sendDto);

      expect(result).toEqual(mockPreparedTransactionDto);
      expect(xrplService.prepareSendTransaction).toHaveBeenCalledWith(
        mockUser.xrplAddress,
        sendDto.destination,
        sendDto.amount,
      );
    });

    it('should throw error when user has no XRPL address', async () => {
      await expect(
        controller.prepareSendTransaction(mockUserWithoutXrpl, sendDto),
      ).rejects.toThrow(
        new HttpException(
          'No XRPL address linked to this account',
          HttpStatus.BAD_REQUEST,
        ),
      );

      expect(xrplService.prepareSendTransaction).not.toHaveBeenCalled();
    });

    it('should re-throw XrplException with code', async () => {
      const xrplError = new Error('Insufficient balance');
      (xrplError as any).code = 'tecUNFUNDED_PAYMENT';
      (xrplError as any).statusCode = HttpStatus.BAD_REQUEST;

      xrplService.prepareSendTransaction.mockRejectedValue(xrplError);

      await expect(
        controller.prepareSendTransaction(mockUser, sendDto),
      ).rejects.toThrow(xrplError);
    });

    it('should handle unknown errors', async () => {
      xrplService.prepareSendTransaction.mockRejectedValue('Unknown error');

      await expect(
        controller.prepareSendTransaction(mockUser, sendDto),
      ).rejects.toThrow(
        new HttpException(
          'Failed to prepare transaction: Unknown error',
          HttpStatus.INTERNAL_SERVER_ERROR,
        ),
      );
    });

    it('should handle errors without code property', async () => {
      const errorWithoutCode = new Error('Generic error');
      xrplService.prepareSendTransaction.mockRejectedValue(errorWithoutCode);

      await expect(
        controller.prepareSendTransaction(mockUser, sendDto),
      ).rejects.toThrow(
        new HttpException(
          'Failed to prepare transaction: Generic error',
          HttpStatus.INTERNAL_SERVER_ERROR,
        ),
      );
    });
  });
});
