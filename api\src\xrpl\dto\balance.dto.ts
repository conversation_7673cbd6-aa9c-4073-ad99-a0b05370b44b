import { IsN<PERSON>ber, IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class BalanceDto {
  @ApiProperty({
    description: 'XRPL address of the account',
    example: 'rPT1Sjq2YGrBMTttX4GZHjKu9yfEVRox89',
  })
  @IsString()
  address!: string;

  @ApiProperty({
    description: 'XRP balance of the account',
    example: 1000.5,
  })
  @IsNumber()
  xrp!: number;

  @ApiProperty({
    description: 'Currency type (always XRP for this endpoint)',
    example: 'XRP',
    default: 'XRP',
  })
  @IsString()
  currency: string = 'XRP';

  @ApiProperty({
    description: 'Current ledger index when balance was fetched',
    example: ********,
  })
  @IsNumber()
  ledgerIndex!: number;

  @ApiProperty({
    description: 'Whether the ledger is validated',
    example: true,
  })
  @IsString()
  validated!: boolean;
}
