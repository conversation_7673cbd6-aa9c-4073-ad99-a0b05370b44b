.register-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 100%;
  padding: 20px;
  background: linear-gradient(
    135deg,
    var(--ion-color-primary) 0%,
    var(--ion-color-primary-shade) 100%
  );
}

.register-card {
  background: var(--ion-color-light);
  border-radius: 16px;
  padding: 32px 24px;
  width: 100%;
  max-width: 400px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);

  h1 {
    color: var(--ion-color-dark);
    font-size: 28px;
    font-weight: 700;
    margin: 0 0 8px 0;
    text-align: center;
  }

  p {
    color: var(--ion-color-medium);
    font-size: 16px;
    margin: 0 0 32px 0;
    text-align: center;
  }
}

ion-item {
  --border-radius: 8px;
  --background: var(--ion-color-light-shade);
  --border-color: var(--ion-color-medium);
  --border-width: 1px;
  --border-style: solid;
  margin-bottom: 16px;
  --padding-start: 16px;
  --padding-end: 16px;
  --padding-top: 8px;
  --padding-bottom: 8px;

  &:focus-within {
    --border-color: var(--ion-color-primary);
    --background: var(--ion-color-light);
  }
}

ion-label {
  color: var(--ion-color-dark);
  font-weight: 500;
}

ion-input {
  --color: var(--ion-color-dark);
  font-size: 16px;
}

.error-message {
  color: var(--ion-color-danger);
  font-size: 14px;
  margin: -8px 0 16px 16px;
  min-height: 20px;
}

.register-button {
  margin: 24px 0 16px 0;
  --border-radius: 8px;
  --padding-top: 16px;
  --padding-bottom: 16px;
  font-weight: 600;
  font-size: 16px;
}

.login-link-button {
  --color: var(--ion-color-medium);
  font-size: 14px;
  margin: 0;
  --padding-top: 8px;
  --padding-bottom: 8px;

  &:hover {
    --color: var(--ion-color-primary);
  }
}

@media (max-width: 576px) {
  .register-container {
    padding: 16px;
  }

  .register-card {
    padding: 24px 20px;
  }

  .register-card h1 {
    font-size: 24px;
  }
}
