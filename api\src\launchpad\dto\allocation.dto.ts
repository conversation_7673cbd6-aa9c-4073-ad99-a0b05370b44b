import { Is<PERSON><PERSON>, <PERSON><PERSON><PERSON>ber, IsEnum, IsBoolean } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { SaleStatus } from '../entities/sale.entity';

export class UserAllocationDto {
  @ApiProperty({
    description: 'Unique identifier of the token sale',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @IsString()
  saleId!: string;

  @ApiProperty({
    description: 'Unique identifier of the user',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @IsString()
  userId!: string;

  @ApiProperty({
    description: 'Total amount contributed by the user in XRP',
    example: 500,
  })
  @IsNumber()
  contributionAmount!: number;

  @ApiProperty({
    description: 'Number of tokens allocated to the user',
    example: 5000,
  })
  @IsNumber()
  allocationAmount!: number;

  @ApiProperty({
    description: 'Percentage of total tokens allocated to the user',
    example: 2.5,
  })
  @IsNumber()
  allocationPercentage!: number;

  @ApiProperty({
    description: 'Current status of the token sale',
    enum: SaleStatus,
    example: SaleStatus.ACTIVE,
  })
  @IsEnum(SaleStatus)
  saleStatus!: SaleStatus;

  @ApiProperty({
    description: 'Whether the sale has ended',
    example: false,
  })
  @IsBoolean()
  saleEnded!: boolean;
}
