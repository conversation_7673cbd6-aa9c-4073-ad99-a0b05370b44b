import { Injectable, inject, signal, computed } from '@angular/core';
import { io, Socket } from 'socket.io-client';
import { Observable, BehaviorSubject, timer } from 'rxjs';
import { environment } from '../../../environments/environment';
import { BalanceResponse, Transaction } from '../../types';

export interface SaleProgress {
  totalRaised: number;
  progressPercent: number;
  userAllocation: number;
  saleId: string;
}

export interface WebSocketMessage {
  type: 'balance' | 'transactions' | 'saleProgress' | 'error' | 'connected';
  data?: any;
  error?: string;
}

@Injectable({
  providedIn: 'root',
})
export class WebSocketService {
  private socket: Socket | null = null;
  private xrplSocket: Socket | null = null;
  private launchpadSocket: Socket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;
  private isConnected = signal<boolean>(false);
  private connectionStatus$ = new BehaviorSubject<
    'connecting' | 'connected' | 'disconnected' | 'error'
  >('disconnected');

  // Signals for reactive state
  public balance = signal<BalanceResponse | null>(null);
  public transactions = signal<Transaction[]>([]);
  public saleProgress = signal<SaleProgress | null>(null);

  // Computed signals
  public isConnected$ = computed(() => this.isConnected());
  public connectionStatus = computed(() => this.connectionStatus$.value);

  constructor() {}

  /**
   * Check if user is authenticated and connect to WebSocket
   */
  private checkAndConnect(): void {
    const token = localStorage.getItem('access_token');
    if (token) {
      console.log('Token found, attempting to connect...');
      this.connect();
    } else {
      console.log('No access token found, cannot connect to WebSocket');
    }
  }

  /**
   * Connect to WebSocket with JWT authentication
   */
  public connect(): void {
    const token = localStorage.getItem('access_token');
    if (!token) {
      console.warn('No access token found, cannot connect to WebSocket');
      this.connectionStatus$.next('error');
      return;
    }

    if (this.socket && this.socket.connected) {
      console.log('WebSocket already connected');
      return;
    }

    console.log('Starting WebSocket connection to:', environment.wsUrl);
    this.connectionStatus$.next('connecting');

    try {
      // Connect to the main namespace first
      this.socket = io(environment.wsUrl, {
        auth: {
          token: token,
        },
        transports: ['websocket', 'polling'],
        timeout: 20000,
        forceNew: true,
        autoConnect: true,
      });
    } catch (error) {
      console.error('Failed to create Socket.IO connection:', error);
      this.connectionStatus$.next('error');
      return;
    }

    // Connection event handlers
    this.socket.on('connect', () => {
      console.log('✅ WebSocket connected successfully!');
      console.log('Socket ID:', this.socket?.id);
      this.isConnected.set(true);
      this.connectionStatus$.next('connected');
      this.reconnectAttempts = 0;
    });

    this.socket.on('disconnect', reason => {
      console.log('❌ WebSocket disconnected:', reason);
      this.isConnected.set(false);
      this.connectionStatus$.next('disconnected');
      if (reason === 'io server disconnect') {
        // Server disconnected, try to reconnect
        this.handleReconnect();
      }
    });

    this.socket.on('connect_error', error => {
      console.error('❌ WebSocket connection error:', error);
      console.error('Error details:', error.message);
      this.connectionStatus$.next('error');
      this.isConnected.set(false);
      this.handleReconnect();
    });

    // Message handlers
    this.socket.on('balance', data => {
      this.balance.set(data);
    });

    this.socket.on('transaction', data => {
      // Update transactions when new ones are received
      const currentTransactions = this.transactions();
      this.transactions.set(
        [data.transaction, ...currentTransactions].slice(0, 10)
      );
    });

    this.socket.on('saleProgress', data => {
      this.saleProgress.set(data);
    });

    this.socket.on('error', error => {
      console.error('WebSocket error message:', error);
      // Handle structured error messages
      if (error && typeof error === 'object') {
        console.error(`Error type: ${error.type}, Message: ${error.message}`);
        if (error.details) {
          console.error(`Error details: ${error.details}`);
        }
      }
    });
  }

  /**
   * Disconnect from WebSocket
   */
  public disconnect(): void {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
    if (this.xrplSocket) {
      this.xrplSocket.disconnect();
      this.xrplSocket = null;
    }
    if (this.launchpadSocket) {
      this.launchpadSocket.disconnect();
      this.launchpadSocket = null;
    }
    this.isConnected.set(false);
    this.connectionStatus$.next('disconnected');
  }

  /**
   * Handle reconnection logic
   */
  private handleReconnect(): void {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      console.log(
        `Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})`
      );

      setTimeout(
        () => {
          this.connect();
        },
        this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1)
      );
    } else {
      console.error('Max reconnection attempts reached');
      this.connectionStatus$.next('error');
    }
  }

  /**
   * Subscribe to specific sale progress updates
   */
  public subscribeToSale(saleId: string): void {
    const token = localStorage.getItem('access_token');
    if (!token) {
      console.warn('No access token found, cannot subscribe to sale');
      return;
    }

    // Disconnect existing launchpad socket if any
    if (this.launchpadSocket) {
      this.launchpadSocket.disconnect();
    }

    // Connect to launchpad namespace and join sale
    this.launchpadSocket = io(`${environment.wsUrl}/launchpad`, {
      auth: {
        token: token,
      },
      transports: ['websocket', 'polling'],
      timeout: 20000,
      forceNew: true,
      autoConnect: true,
    });

    this.launchpadSocket.on('connect', () => {
      console.log('Connected to launchpad namespace');
      this.launchpadSocket!.emit('joinSale', { saleId });
    });

    this.launchpadSocket.on('disconnect', reason => {
      console.log('Disconnected from launchpad namespace:', reason);
    });

    this.launchpadSocket.on('saleProgress', data => {
      this.saleProgress.set(data);
    });

    this.launchpadSocket.on('contributionReceived', data => {
      // Update sale progress when new contributions are received
      if (data.progress) {
        this.saleProgress.set(data.progress);
      }
    });

    this.launchpadSocket.on('error', error => {
      console.error('Launchpad socket error:', error);
      // Handle structured error messages
      if (error && typeof error === 'object') {
        console.error(`Error type: ${error.type}, Message: ${error.message}`);
        if (error.details) {
          console.error(`Error details: ${error.details}`);
        }
      }
    });
  }

  /**
   * Unsubscribe from sale updates
   */
  public unsubscribeFromSale(): void {
    if (this.launchpadSocket) {
      this.launchpadSocket.disconnect();
      this.launchpadSocket = null;
    }
    this.saleProgress.set(null);
  }

  /**
   * Request balance update
   */
  public requestBalanceUpdate(): void {
    const token = localStorage.getItem('access_token');
    if (!token) {
      console.warn('No access token found, cannot request balance update');
      return;
    }

    // Disconnect existing XRPL socket if any
    if (this.xrplSocket) {
      this.xrplSocket.disconnect();
    }

    // Connect to XRPL namespace for balance updates
    this.xrplSocket = io(`${environment.wsUrl}/xrpl`, {
      auth: {
        token: token,
      },
      transports: ['websocket', 'polling'],
      timeout: 20000,
      forceNew: true,
      autoConnect: true,
    });

    this.xrplSocket.on('connect', () => {
      console.log('Connected to XRPL namespace');
      // Subscribe to user's XRPL address for balance updates
      const user = JSON.parse(localStorage.getItem('user') || '{}');
      if (user.xrplAddress) {
        this.xrplSocket!.emit('subscribe', { address: user.xrplAddress });
      }
    });

    this.xrplSocket.on('disconnect', reason => {
      console.log('Disconnected from XRPL namespace:', reason);
    });

    this.xrplSocket.on('transaction', data => {
      // Update transactions when new ones are received
      const currentTransactions = this.transactions();
      this.transactions.set(
        [data.transaction, ...currentTransactions].slice(0, 10)
      );
    });

    this.xrplSocket.on('balance', data => {
      this.balance.set(data);
    });

    this.xrplSocket.on('error', error => {
      console.error('XRPL socket error:', error);
      // Handle structured error messages
      if (error && typeof error === 'object') {
        console.error(`Error type: ${error.type}, Message: ${error.message}`);
        if (error.details) {
          console.error(`Error details: ${error.details}`);
        }
      }
    });
  }

  /**
   * Request transactions update
   */
  public requestTransactionsUpdate(): void {
    // This is handled by the balance update subscription
    this.requestBalanceUpdate();
  }

  /**
   * Get connection status observable
   */
  public getConnectionStatus(): Observable<string> {
    return this.connectionStatus$.asObservable();
  }

  /**
   * Check if WebSocket is connected
   */
  public getIsConnected(): boolean {
    return this.isConnected();
  }

  /**
   * Get connection status for all sockets
   */
  public getAllConnectionStatus(): {
    main: boolean;
    xrpl: boolean;
    launchpad: boolean;
  } {
    return {
      main: this.socket?.connected || false,
      xrpl: this.xrplSocket?.connected || false,
      launchpad: this.launchpadSocket?.connected || false,
    };
  }

  /**
   * Reconnect manually
   */
  public reconnect(): void {
    this.disconnect();
    this.reconnectAttempts = 0;
    this.connect();
  }

  /**
   * Start WebSocket connection (public method for components to call)
   */
  public start(): void {
    console.log('WebSocketService.start() called');
    this.checkAndConnect();
  }

  /**
   * Check if service is ready to connect
   */
  public isReady(): boolean {
    const token = localStorage.getItem('access_token');
    return !!token;
  }

  /**
   * Debug method to check service status
   */
  public debugStatus(): void {
    const connectionStatus = this.getAllConnectionStatus();
    console.log('🔍 WebSocket Service Debug Status:');
    console.log('- Environment WS URL:', environment.wsUrl);
    console.log('- Has token:', !!localStorage.getItem('access_token'));
    console.log(
      '- Token preview:',
      localStorage.getItem('access_token')?.substring(0, 20) + '...'
    );
    console.log('- Main socket exists:', !!this.socket);
    console.log('- Main socket connected:', connectionStatus.main);
    console.log('- XRPL socket exists:', !!this.xrplSocket);
    console.log('- XRPL socket connected:', connectionStatus.xrpl);
    console.log('- Launchpad socket exists:', !!this.launchpadSocket);
    console.log('- Launchpad socket connected:', connectionStatus.launchpad);
    console.log('- Is connected signal:', this.isConnected());
    console.log('- Connection status:', this.connectionStatus$.value);
    console.log('- Reconnect attempts:', this.reconnectAttempts);
  }
}
